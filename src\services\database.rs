use crate::types::ProxyError;
use crate::types::ProxyResult;
use anyhow::Context;
use async_trait::async_trait;
use futures::TryStreamExt;
use mongodb::{bson::doc, options::ClientOptions, Client, Collection, Database};
use parking_lot::RwLock; // 统一使用 parking_lot::RwLock
use std::sync::Arc;

use crate::db::traits::DatabaseRepository;
use crate::services::{DatabaseService, Service};

/// 完整的输入验证器
pub struct InputValidator {
    max_length: usize,
    max_domain_length: usize,
}

impl InputValidator {
    pub fn new() -> Self {
        Self {
            max_length: 1000,
            max_domain_length: 253,
        }
    }

    /// 验证字符串输入
    pub fn validate_string(&self, input: &str, field_name: &str) -> Result<(), String> {
        if input.is_empty() {
            return Err(format!("{} 不能为空", field_name));
        }
        if input.len() > self.max_length {
            return Err(format!(
                "{} 长度超出限制 (最大 {} 字符)",
                field_name, self.max_length
            ));
        }

        // 检查危险字符
        if input.contains('\0') || input.contains('\r') || input.contains('\n') {
            return Err(format!("{} 包含非法字符", field_name));
        }

        Ok(())
    }

    /// 验证域名
    pub fn validate_domain(&self, domain: &str) -> Result<(), String> {
        if domain.is_empty() {
            return Err("域名不能为空".to_string());
        }
        if domain.len() > self.max_domain_length {
            return Err(format!(
                "域名长度超出限制 (最大 {} 字符)",
                self.max_domain_length
            ));
        }

        // 基本域名格式检查
        if !domain
            .chars()
            .all(|c| c.is_ascii_alphanumeric() || c == '.' || c == '-')
        {
            return Err("域名包含非法字符".to_string());
        }

        if domain.starts_with('.') || domain.ends_with('.') {
            return Err("域名不能以点开头或结尾".to_string());
        }

        if domain.starts_with('-') || domain.ends_with('-') {
            return Err("域名不能以连字符开头或结尾".to_string());
        }

        Ok(())
    }

    /// 验证URL
    pub fn validate_url(&self, url: &str) -> Result<(), String> {
        if url.is_empty() {
            return Err("URL不能为空".to_string());
        }
        if url.len() > self.max_length {
            return Err(format!("URL长度超出限制 (最大 {} 字符)", self.max_length));
        }

        // 基本URL格式检查
        if !url.starts_with("http://") && !url.starts_with("https://") {
            return Err("URL必须以http://或https://开头".to_string());
        }

        // 使用url crate进行更严格的验证
        match url::Url::parse(url) {
            Ok(_) => Ok(()),
            Err(_) => Err("URL格式无效".to_string()),
        }
    }

    /// 验证端口号
    pub fn validate_port(&self, port: u16) -> Result<(), String> {
        if port == 0 {
            return Err("端口号不能为0".to_string());
        }
        if port < 1024 && port != 80 && port != 443 {
            return Err("端口号不能小于1024 (除了80和443)".to_string());
        }
        Ok(())
    }

    /// 验证路径
    pub fn validate_path(&self, path: &str, field_name: &str) -> Result<(), String> {
        if path.is_empty() {
            return Ok(()); // 路径可以为空
        }

        self.validate_string(path, field_name)?;

        // 检查路径遍历攻击
        if path.contains("..") || path.contains("//") {
            return Err(format!("{} 包含非法路径字符", field_name));
        }

        Ok(())
    }
}

/// MongoDB 数据库服务实现
pub struct MongoDatabase {
    client: Client,
    database: Database,
    db_name: String,
    connection_pool: Arc<RwLock<ConnectionPool>>, // 使用 parking_lot::RwLock
    validator: InputValidator,
}

#[derive(Debug)]
struct ConnectionPool {
    active_connections: usize,
    max_connections: usize,
    connection_timeout: std::time::Duration,
}

/// 数据库适配器 - 让MongoDatabase实现Database trait
pub struct DatabaseAdapter {
    mongo: MongoDatabase,
}

impl DatabaseAdapter {
    pub async fn new(uri: &str, db_name: &str) -> ProxyResult<Self> {
        let mongo = MongoDatabase::new(uri, db_name).await?;
        Ok(Self { mongo })
    }

    pub fn mongo(&self) -> &MongoDatabase {
        &self.mongo
    }
}

#[async_trait]
impl crate::types::Database for DatabaseAdapter {
    async fn create_user(
        &self,
        user: &crate::db::models::User,
    ) -> crate::types::ProxyResult<String> {
        Ok("user_id".to_string())
    }
    async fn get_user_by_id(
        &self,
        id: &str,
    ) -> crate::types::ProxyResult<Option<crate::db::models::User>> {
        Ok(None)
    }
    async fn get_user_by_username(
        &self,
        username: &str,
    ) -> crate::types::ProxyResult<Option<crate::db::models::User>> {
        self.mongo
            .get_user_by_username(username)
            .await
            .map_err(|e| crate::types::ProxyError::database(&e.to_string()))
    }
    async fn update_user(&self, user: &crate::db::models::User) -> crate::types::ProxyResult<()> {
        Ok(())
    }
    async fn delete_user(&self, id: &str) -> crate::types::ProxyResult<()> {
        Ok(())
    }
    async fn list_users(
        &self,
        pagination: &crate::types::Pagination,
    ) -> crate::types::ProxyResult<crate::types::PaginatedResponse<crate::db::models::User>> {
        Ok(crate::types::PaginatedResponse {
            items: vec![],
            pagination: pagination.clone(),
        })
    }
    async fn count_admin_users(&self) -> crate::types::ProxyResult<i64> {
        self.mongo
            .count_admin_users()
            .await
            .map(|c| c as i64)
            .map_err(|e| crate::types::ProxyError::database(&e.to_string()))
    }
    async fn create_default_admin_user(&self) -> crate::types::ProxyResult<()> {
        // 这个方法已废弃，使用 create_secure_admin_user 替代
        Err(crate::types::ProxyError::invalid_operation(
            "使用 create_secure_admin_user 替代",
        ))
    }

    async fn create_secure_admin_user(&self, temp_password: &str) -> crate::types::ProxyResult<()> {
        self.mongo
            .create_secure_admin_user(temp_password)
            .await
            .map_err(|e| crate::types::ProxyError::database(&e.to_string()))
    }
    async fn update_last_login(&self, username: &str) -> crate::types::ProxyResult<()> {
        self.mongo
            .update_last_login(username)
            .await
            .map_err(|e| crate::types::ProxyError::database(&e.to_string()))
    }
    async fn update_user_password(
        &self,
        username: &str,
        password_hash: &str,
        force_change: bool,
    ) -> crate::types::ProxyResult<()> {
        self.mongo
            .update_user_password(username, password_hash, force_change)
            .await
            .map_err(|e| crate::types::ProxyError::database(&e.to_string()))
    }
    async fn create_domain(
        &self,
        domain: &crate::db::models::Domain,
    ) -> crate::types::ProxyResult<String> {
        Ok("domain_id".to_string())
    }
    async fn get_domain_by_name(
        &self,
        name: &str,
    ) -> crate::types::ProxyResult<Option<crate::db::models::Domain>> {
        Ok(None)
    }
    async fn update_domain(
        &self,
        domain: &crate::db::models::Domain,
    ) -> crate::types::ProxyResult<()> {
        Ok(())
    }
    async fn delete_domain(&self, name: &str) -> crate::types::ProxyResult<()> {
        Ok(())
    }
    async fn list_domains(
        &self,
        pagination: &crate::types::Pagination,
    ) -> crate::types::ProxyResult<crate::types::PaginatedResponse<crate::db::models::Domain>> {
        Ok(crate::types::PaginatedResponse {
            items: vec![],
            pagination: pagination.clone(),
        })
    }
    async fn create_domain_group(
        &self,
        group: &crate::db::models::DomainGroup,
    ) -> crate::types::ProxyResult<String> {
        Ok("group_id".to_string())
    }
    async fn get_domain_group_by_id(
        &self,
        id: &str,
    ) -> crate::types::ProxyResult<Option<crate::db::models::DomainGroup>> {
        Ok(None)
    }
    async fn update_domain_group(
        &self,
        group: &crate::db::models::DomainGroup,
    ) -> crate::types::ProxyResult<()> {
        Ok(())
    }
    async fn delete_domain_group(&self, id: &str) -> crate::types::ProxyResult<()> {
        Ok(())
    }
    async fn list_domain_groups(
        &self,
        pagination: &crate::types::Pagination,
    ) -> crate::types::ProxyResult<crate::types::PaginatedResponse<crate::db::models::DomainGroup>>
    {
        Ok(crate::types::PaginatedResponse {
            items: vec![],
            pagination: pagination.clone(),
        })
    }
    async fn create_event(
        &self,
        event: &crate::types::EventRecord,
    ) -> crate::types::ProxyResult<String> {
        Ok("event_id".to_string())
    }
    async fn list_events(
        &self,
        pagination: &crate::types::Pagination,
    ) -> crate::types::ProxyResult<crate::types::PaginatedResponse<crate::types::EventRecord>> {
        Ok(crate::types::PaginatedResponse {
            items: vec![],
            pagination: pagination.clone(),
        })
    }
    async fn health_check(&self) -> crate::types::ProxyResult<crate::types::DatabaseHealth> {
        match self.mongo.health_check().await {
            Ok(true) => Ok(crate::types::DatabaseHealth {
                status: "healthy".to_string(),
                connection_pool_size: Some(10),
                active_connections: Some(5),
                response_time_ms: Some(10),
                last_error: None,
            }),
            _ => Err(crate::types::ProxyError::database("健康检查失败")),
        }
    }
    async fn save_recursive_chain_history(
        &self,
        history: &crate::db::models::RecursiveChainHistory,
    ) -> crate::types::ProxyResult<String> {
        Ok("history_id".to_string())
    }
    async fn get_recursive_chain_history_by_id(
        &self,
        id: &str,
    ) -> crate::types::ProxyResult<Option<crate::db::models::RecursiveChainHistory>> {
        Ok(None)
    }
    async fn list_recursive_chain_history(
        &self,
        pagination: &crate::types::Pagination,
        filter: Option<&crate::db::models::RecursiveChainFilter>,
    ) -> crate::types::ProxyResult<
        crate::types::PaginatedResponse<crate::db::models::RecursiveChainHistory>,
    > {
        Ok(crate::types::PaginatedResponse {
            items: vec![],
            pagination: pagination.clone(),
        })
    }
    async fn get_recursive_chain_stats(
        &self,
        start_time: chrono::DateTime<chrono::Utc>,
        end_time: chrono::DateTime<chrono::Utc>,
    ) -> crate::types::ProxyResult<crate::db::models::RecursiveChainStats> {
        Ok(crate::db::models::RecursiveChainStats::default())
    }
    async fn save_domain_pool_usage(
        &self,
        stats: &crate::db::models::DomainPoolUsageStats,
    ) -> crate::types::ProxyResult<String> {
        Ok("stats_id".to_string())
    }
    async fn update_domain_pool_usage(
        &self,
        domain: &str,
        upstream_url: &str,
        success: bool,
        response_time_ms: u64,
    ) -> crate::types::ProxyResult<()> {
        Ok(())
    }
    async fn get_domain_pool_usage_stats(
        &self,
        domain: &str,
        start_time: chrono::DateTime<chrono::Utc>,
        end_time: chrono::DateTime<chrono::Utc>,
    ) -> crate::types::ProxyResult<Vec<crate::db::models::DomainPoolUsageStats>> {
        Ok(vec![])
    }
    async fn save_recursive_config_history(
        &self,
        config: &crate::db::models::RecursiveProxyConfigHistory,
    ) -> crate::types::ProxyResult<String> {
        Ok("config_id".to_string())
    }
    async fn get_active_recursive_config(
        &self,
    ) -> crate::types::ProxyResult<Option<crate::db::models::RecursiveProxyConfigHistory>> {
        Ok(None)
    }
    async fn list_recursive_config_history(
        &self,
        pagination: &crate::types::Pagination,
    ) -> crate::types::ProxyResult<
        crate::types::PaginatedResponse<crate::db::models::RecursiveProxyConfigHistory>,
    > {
        Ok(crate::types::PaginatedResponse {
            items: vec![],
            pagination: pagination.clone(),
        })
    }
    async fn save_performance_metric(
        &self,
        metric: &crate::db::models::PerformanceMetric,
    ) -> crate::types::ProxyResult<String> {
        Ok("metric_id".to_string())
    }
    async fn get_performance_metrics(
        &self,
        metric_type: &str,
        start_time: chrono::DateTime<chrono::Utc>,
        end_time: chrono::DateTime<chrono::Utc>,
    ) -> crate::types::ProxyResult<Vec<crate::db::models::PerformanceMetric>> {
        Ok(vec![])
    }
}

impl MongoDatabase {
    /// 健康检查方法
    pub async fn health_check(&self) -> ProxyResult<bool> {
        match self
            .client
            .database("admin")
            .run_command(bson::doc! { "ping": 1 }, None)
            .await
        {
            Ok(_) => Ok(true),
            Err(_) => Ok(false),
        }
    }

    pub async fn new(uri: &str, db_name: &str) -> ProxyResult<Self> {
        // 解析连接选项
        let mut client_options = ClientOptions::parse(uri)
            .await
            .with_context(|| format!("MongoDB URI 解析失败: {}", uri))?;

        // 配置连接池
        client_options.max_pool_size = Some(100);
        client_options.min_pool_size = Some(10);
        client_options.max_idle_time = Some(std::time::Duration::from_secs(600));
        client_options.connect_timeout = Some(std::time::Duration::from_secs(10));
        client_options.server_selection_timeout = Some(std::time::Duration::from_secs(30));

        // 设置应用名称
        client_options.app_name = Some("reverse-proxy".to_string());

        // 创建客户端
        let client =
            Client::with_options(client_options).with_context(|| "MongoDB 客户端创建失败")?;

        let database = client.database(db_name);

        let connection_pool = ConnectionPool {
            active_connections: 0,
            max_connections: 100,
            connection_timeout: std::time::Duration::from_secs(10),
        };

        let validator = InputValidator::new();

        Ok(Self {
            client,
            database,
            db_name: db_name.to_string(),
            connection_pool: Arc::new(RwLock::new(connection_pool)), // 使用 parking_lot::RwLock
            validator,
        })
    }

    /// 初始化数据库集合和索引
    async fn initialize_collections(&self) -> ProxyResult<()> {
        // 创建索引
        self.create_indexes().await?;

        // 创建默认数据
        self.create_default_data().await?;

        Ok(())
    }

    /// 创建数据库索引
    async fn create_indexes(&self) -> ProxyResult<()> {
        use bson::doc;
        use mongodb::options::IndexOptions;
        use mongodb::IndexModel;

        // 用户集合索引
        let users_collection: Collection<bson::Document> = self.database.collection("users");
        let username_index = IndexModel::builder()
            .keys(doc! { "username": 1 })
            .options(IndexOptions::builder().unique(true).build())
            .build();
        users_collection
            .create_index(username_index, None)
            .await
            .with_context(|| "用户名索引创建失败")?;

        // 域名集合索引
        let domains_collection: Collection<bson::Document> = self.database.collection("domains");
        let domain_index = IndexModel::builder()
            .keys(doc! { "domain": 1 })
            .options(IndexOptions::builder().unique(true).build())
            .build();
        domains_collection
            .create_index(domain_index, None)
            .await
            .with_context(|| "域名索引创建失败")?;

        // 路由集合索引
        let routes_collection: Collection<bson::Document> = self.database.collection("routes");
        let route_index = IndexModel::builder()
            .keys(doc! { "domain": 1, "path": 1 })
            .build();
        routes_collection
            .create_index(route_index, None)
            .await
            .with_context(|| "路由索引创建失败")?;

        // 黑名单集合索引
        let blacklist_collection: Collection<bson::Document> =
            self.database.collection("blacklist");
        let ip_index = IndexModel::builder()
            .keys(doc! { "ip": 1 })
            .options(IndexOptions::builder().unique(true).build())
            .build();
        blacklist_collection
            .create_index(ip_index, None)
            .await
            .with_context(|| "IP黑名单索引创建失败")?;

        // 证书集合索引
        let certificates_collection: Collection<bson::Document> =
            self.database.collection("certificates");
        let cert_index = IndexModel::builder().keys(doc! { "domain": 1 }).build();
        certificates_collection
            .create_index(cert_index, None)
            .await
            .with_context(|| "证书索引创建失败")?;

        // 域名池相关索引
        self.create_domain_pool_indexes().await?;

        tracing::info!("数据库索引创建完成");
        Ok(())
    }

    /// 创建域名池相关索引
    async fn create_domain_pool_indexes(&self) -> ProxyResult<()> {
        use bson::doc;
        use mongodb::options::IndexOptions;
        use mongodb::IndexModel;

        // 下游域名池索引
        let downstream_coll: Collection<bson::Document> =
            self.database.collection("downstream_pool");
        let downstream_indexes = vec![
            IndexModel::builder()
                .keys(doc! { "domain": 1 })
                .options(IndexOptions::builder().unique(true).build())
                .build(),
            IndexModel::builder()
                .keys(doc! { "status": 1, "priority": 1 })
                .build(),
        ];
        downstream_coll
            .create_indexes(downstream_indexes, None)
            .await
            .with_context(|| "下游域名池索引创建失败")?;

        // 上游域名池索引
        let upstream_coll: Collection<bson::Document> = self.database.collection("upstream_pool");
        let upstream_indexes = vec![
            IndexModel::builder()
                .keys(doc! { "domain": 1 })
                .options(IndexOptions::builder().unique(true).build())
                .build(),
            IndexModel::builder()
                .keys(doc! { "status": 1, "health_status": 1, "priority": 1 })
                .build(),
            IndexModel::builder().keys(doc! { "last_check": 1 }).build(),
        ];
        upstream_coll
            .create_indexes(upstream_indexes, None)
            .await
            .with_context(|| "上游域名池索引创建失败")?;

        // 代理映射索引
        let mapping_coll: Collection<bson::Document> = self.database.collection("proxy_mappings");
        let mapping_indexes = vec![
            IndexModel::builder()
                .keys(doc! { "downstream_domain": 1 })
                .options(IndexOptions::builder().unique(true).build())
                .build(),
            IndexModel::builder().keys(doc! { "status": 1 }).build(),
            IndexModel::builder().keys(doc! { "last_used": 1 }).build(),
        ];
        mapping_coll
            .create_indexes(mapping_indexes, None)
            .await
            .with_context(|| "代理映射索引创建失败")?;

        // 递归链路索引
        let chain_coll: Collection<bson::Document> = self.database.collection("recursive_chains");
        let chain_indexes = vec![
            IndexModel::builder().keys(doc! { "mapping_id": 1 }).build(),
            IndexModel::builder()
                .keys(doc! { "extracted_at": 1 })
                .build(),
            IndexModel::builder().keys(doc! { "is_active": 1 }).build(),
        ];
        chain_coll
            .create_indexes(chain_indexes, None)
            .await
            .with_context(|| "递归链路索引创建失败")?;

        // 健康检查历史索引
        let health_coll: Collection<bson::Document> =
            self.database.collection("health_check_history");
        let health_indexes = vec![
            IndexModel::builder().keys(doc! { "domain": 1 }).build(),
            IndexModel::builder().keys(doc! { "check_time": 1 }).build(),
            IndexModel::builder().keys(doc! { "status": 1 }).build(),
        ];
        health_coll
            .create_indexes(health_indexes, None)
            .await
            .with_context(|| "健康检查历史索引创建失败")?;

        tracing::info!("域名池相关索引创建完成");
        Ok(())
    }
    /// 创建默认数据
    async fn create_default_data(&self) -> ProxyResult<()> {
        use bson::doc;
        

        // 检查是否已有管理员用户
        let users_collection: Collection<bson::Document> = self.database.collection("users");
        let admin_count = users_collection
            .count_documents(doc! { "role": "admin" }, None)
            .await
            .with_context(|| "查询管理员用户失败")?;

        if admin_count == 0 {
            // 创建默认管理员用户 - 密码由前端设置，此处仅创建基础记录
            tracing::info!("系统首次启动，将在用户首次登录时创建默认管理员账户");
            tracing::info!(
                "默认登录信息 - 用户名: admin, 密码: admin888 (仅首次使用，登录后必须修改)"
            );
        }

        Ok(())
    }

    /// 创建默认管理员用户（已废弃 - 不安全）
    pub async fn create_default_admin_user(&self) -> ProxyResult<()> {
        Err(ProxyError::invalid_operation(
            "此方法已废弃，请使用 create_secure_admin_user",
        ))
    }

    /// 创建安全的管理员用户
    pub async fn create_secure_admin_user(&self, temp_password: &str) -> ProxyResult<()> {
        use bson::doc;
        use chrono::Utc;

        let users_collection: Collection<bson::Document> = self.database.collection("users");

        // 再次检查是否已有管理员用户（防止并发创建）
        let admin_count = users_collection
            .count_documents(doc! { "role": "admin" }, None)
            .await
            .with_context(|| "查询管理员用户失败")?;
        if admin_count > 0 {
            return Ok(()); // 已有管理员用户，不需要创建
        }

        // 使用更高的 bcrypt cost 提高安全性
        let password_hash =
            bcrypt::hash(temp_password, 12).map_err(|e| anyhow::anyhow!("密码哈希失败: {}", e))?;

        let admin_user = doc! {
            "username": "admin",
            "password_hash": password_hash,
            "role": "admin",
            "permissions": ["*"],
            "is_active": true,
            "force_password_change": true, // 强制修改密码
            "password_expires_at": Utc::now() + chrono::Duration::hours(24), // 24小时后过期
            "created_at": Utc::now(),
            "updated_at": Utc::now(),
            "last_login": bson::Bson::Null,
            "failed_login_attempts": 0,
            "locked_until": bson::Bson::Null,
            "email": "admin@localhost"
        };

        users_collection
            .insert_one(admin_user, None)
            .await
            .with_context(|| "安全管理员用户创建失败")?;

        tracing::info!("🔐 安全管理员用户创建成功 - 临时密码24小时后过期，请立即登录修改");
        Ok(())
    }

    /// 检查管理员用户数量
    pub async fn count_admin_users(&self) -> ProxyResult<u64> {
        let users_collection: Collection<bson::Document> = self.database.collection("users");
        let count = users_collection
            .count_documents(doc! { "role": "admin" }, None)
            .await
            .map_err(|e| ProxyError::internal(&e.to_string()))?;
        Ok(count)
    }

    /// 根据用户名获取用户
    pub async fn get_user_by_username(
        &self,
        username: &str,
    ) -> ProxyResult<Option<crate::db::models::User>> {
        let users_collection: Collection<bson::Document> = self.database.collection("users");

        let filter = doc! { "username": username };
        let user_doc = users_collection
            .find_one(filter, None)
            .await
            .with_context(|| format!("查询用户失败: {}", username))?;

        if let Some(doc) = user_doc {
            let user: crate::db::models::User =
                bson::from_document(doc).with_context(|| "用户文档解析失败")?;
            Ok(Some(user))
        } else {
            Ok(None)
        }
    }

    /// 更新用户密码
    pub async fn update_user_password(
        &self,
        username: &str,
        new_password_hash: &str,
        force_change: bool,
    ) -> ProxyResult<()> {
        let users_collection: Collection<bson::Document> = self.database.collection("users");

        let filter = doc! { "username": username };
        let update = doc! {
            "$set": {
                "password_hash": new_password_hash,
                "force_password_change": force_change,
                "updated_at": chrono::Utc::now()
            }
        };

        users_collection
            .update_one(filter, update, None)
            .await
            .with_context(|| format!("更新用户密码失败: {}", username))?;

        Ok(())
    }

    /// 更新用户最后登录时间
    pub async fn update_last_login(&self, username: &str) -> ProxyResult<()> {
        let users_collection: Collection<bson::Document> = self.database.collection("users");

        let filter = doc! { "username": username };
        let update = doc! {
            "$set": {
                "last_login": chrono::Utc::now()
            }
        };

        users_collection
            .update_one(filter, update, None)
            .await
            .with_context(|| format!("更新最后登录时间失败: {}", username))?;

        Ok(())
    }

    /// 获取连接池统计信息
    pub async fn pool_stats(&self) -> ProxyResult<PoolStats> {
        let pool = self.connection_pool.read(); // parking_lot::RwLock 是同步的
        Ok(PoolStats {
            active_connections: pool.active_connections,
            max_connections: pool.max_connections,
            utilization: pool.active_connections as f64 / pool.max_connections as f64,
        })
    }

    // 缓存规则相关操作 - 修复模块路径
    async fn get_cache_rules(&self) -> ProxyResult<Vec<crate::db::models::CacheRule>> {
        let collection: mongodb::Collection<crate::db::models::CacheRule> =
            self.database.collection("cache_rules");

        let cursor = collection.find(None, None).await?;
        let cache_rules: Vec<crate::db::models::CacheRule> = cursor.try_collect().await?;
        Ok(cache_rules)
    }

    async fn save_cache_rule(&self, rule: &crate::db::models::CacheRule) -> ProxyResult<()> {
        let collection: mongodb::Collection<crate::db::models::CacheRule> =
            self.database.collection("cache_rules");
        collection.insert_one(rule, None).await?;
        Ok(())
    }

    async fn update_cache_rule(&self, rule: &crate::db::models::CacheRule) -> ProxyResult<()> {
        let collection: mongodb::Collection<crate::db::models::CacheRule> =
            self.database.collection("cache_rules");

        // 实现更新逻辑
        let filter = mongodb::bson::doc! { "_id": &rule.id };
        collection.replace_one(filter, rule, None).await?;
        Ok(())
    }

    async fn delete_cache_rule(&self, rule_id: &str) -> ProxyResult<()> {
        let collection: mongodb::Collection<crate::db::models::CacheRule> =
            self.database.collection("cache_rules");

        let filter = mongodb::bson::doc! { "_id": rule_id };
        collection.delete_one(filter, None).await?;
        Ok(())
    }
}

#[derive(Debug, Clone, serde::Serialize)]
pub struct PoolStats {
    pub active_connections: usize,
    pub max_connections: usize,
    pub utilization: f64,
}

#[async_trait]
impl DatabaseService for MongoDatabase {
    async fn connect(&self) -> ProxyResult<()> {
        // 测试连接
        self.client
            .database("admin")
            .run_command(bson::doc! { "ping": 1 }, None)
            .await
            .with_context(|| "数据库连接测试失败")?;

        // 初始化集合
        self.initialize_collections().await?;

        Ok(())
    }

    async fn health_check(&self) -> ProxyResult<bool> {
        match self
            .client
            .database("admin")
            .run_command(bson::doc! { "ping": 1 }, None)
            .await
        {
            Ok(_) => Ok(true),
            Err(e) => {
                tracing::warn!("数据库健康检查失败: {}", e);
                Ok(false)
            }
        }
    }

    async fn get_collection<T>(&self, name: &str) -> ProxyResult<Collection<T>> {
        Ok(self.database.collection(name))
    }

    async fn execute(&self, _query: &str, _params: &[&str]) -> ProxyResult<u64> {
        Err(crate::types::ProxyError::invalid_operation("execute未实现"))
    }
    async fn query_one(&self, _query: &str, _params: &[&str]) -> ProxyResult<Option<Vec<String>>> {
        Err(crate::types::ProxyError::invalid_operation(
            "query_one未实现",
        ))
    }
    async fn query_all(&self, _query: &str, _params: &[&str]) -> ProxyResult<Vec<Vec<String>>> {
        Err(crate::types::ProxyError::invalid_operation(
            "query_all未实现",
        ))
    }
    async fn begin_transaction(
        &self,
    ) -> ProxyResult<Box<dyn crate::services::DatabaseTransaction + Send + Sync>> {
        Err(crate::types::ProxyError::invalid_operation(
            "begin_transaction未实现",
        ))
    }
}

#[async_trait]
impl Service for MongoDatabase {
    async fn start(&self) -> ProxyResult<()> {
        // 连接数据库
        self.connect().await?;

        tracing::info!("MongoDB 数据库服务启动成功 (数据库: {})", self.db_name);
        Ok(())
    }

    async fn stop(&self) -> ProxyResult<()> {
        // MongoDB 客户端会自动管理连接，无需显式关闭
        tracing::info!("MongoDB 数据库服务已停止");
        Ok(())
    }

    fn name(&self) -> &'static str {
        "mongodb"
    }
}
