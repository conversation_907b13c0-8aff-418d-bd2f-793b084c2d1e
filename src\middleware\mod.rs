use async_trait::async_trait;
use hyper::{Body, Request, Response};
use std::sync::Arc;

pub type BoxedMiddleware = Arc<dyn Middleware + Send + Sync>;

/// 中间件 trait
#[async_trait]
pub trait Middleware: Send + Sync {
    async fn handle(&self, req: Request<Body>, next: Next<'_>) -> hyper::Result<Response<Body>>;
}

pub struct Next<'a> {
    pub middlewares: &'a [BoxedMiddleware],
    pub endpoint: &'a dyn Endpoint,
}

impl<'a> Next<'a> {
    pub async fn run(&self, req: Request<Body>) -> hyper::Result<Response<Body>> {
        if let Some((first, rest)) = self.middlewares.split_first() {
            let next = Next {
                middlewares: rest,
                endpoint: self.endpoint,
            };
            first.handle(req, next).await
        } else {
            self.endpoint.call(req).await
        }
    }
}

#[async_trait]
pub trait Endpoint: Send + Sync {
    async fn call(&self, req: Request<Body>) -> hyper::Result<Response<Body>>;
}

// 具体中间件实现模块
pub mod cors;
pub mod headers;
pub mod logging;
pub mod rate_limit;
pub mod security;
