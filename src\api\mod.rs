// API模块总体组织和公共结构

use axum::{
    extract::{Path, State},
    response::IntoResponse,
    routing::{get, post},
    Json, Router,
};
use std::sync::Arc;
// 移除anyhow::Result，统一用ProxyResult
// use anyhow::Result;
use tower_http::trace::TraceLayer;
// 添加必要的导入
use crate::types::SimpleAppState;

// 导入各模块
mod auth; // API路由层认证 - 使用proxy-cache的认证服务
mod blacklist;
mod cert;
mod config;
// mod csrf_token; // 暂时注释掉，文件不存在
mod domain;
pub mod domain_group;
// pub mod domain_pool; // 暂时注释掉，文件不存在
mod perf;
pub mod recursive_management; // 递归代理管理API
mod security;
mod static_router;
mod status;

// 导入认证中间件
use crate::auth::jwt_auth_middleware;

/// API统一格式响应 - 使用统一的错误类型
#[derive(Debug, serde::Serialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub message: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data: Option<T>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub error: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub error_code: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub request_id: Option<String>,
}

impl<T> ApiResponse<T> {
    /// 创建成功响应
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            message: None,
            data: Some(data),
            error: None,
            error_code: None,
            request_id: None,
        }
    }

    /// 创建带消息的成功响应
    pub fn success_with_message(data: T, message: impl Into<String>) -> Self {
        Self {
            success: true,
            message: Some(message.into()),
            data: Some(data),
            error: None,
            error_code: None,
            request_id: None,
        }
    }

    /// 创建错误响应
    pub fn error_from_string(error: &str) -> ApiResponse<serde_json::Value> {
        ApiResponse {
            success: false,
            message: None,
            data: None,
            error: Some(error.to_string()),
            error_code: Some("GENERIC_ERROR".to_string()),
            request_id: None,
        }
    }

    /// 创建带有指定泛型类型的错误响应
    pub fn error_with_type<U>(error: &str) -> ApiResponse<U> {
        ApiResponse {
            success: false,
            message: None,
            data: None,
            error: Some(error.to_string()),
            error_code: Some("GENERIC_ERROR".to_string()),
            request_id: None,
        }
    }

    /// 创建自定义错误响应
    pub fn error(message: impl Into<String>) -> ApiResponse<serde_json::Value> {
        ApiResponse {
            success: false,
            message: None,
            data: None,
            error: Some(message.into()),
            error_code: Some("GENERIC_ERROR".to_string()),
            request_id: None,
        }
    }

    /// 添加请求ID
    pub fn with_request_id(mut self, request_id: impl Into<String>) -> Self {
        self.request_id = Some(request_id.into());
        self
    }

    /// 从代理错误创建错误响应
    pub fn error_from_proxy_error(error: &proxy_core::ProxyError) -> ApiResponse<T> {
        ApiResponse {
            success: false,
            data: None,
            message: Some(error.to_string()),
            error: Some(error.to_string()),
            error_code: Some("PROXY_ERROR".to_string()),
            request_id: None,
        }
    }

    /// 从本地代理错误创建错误响应
    pub fn error_from_local_proxy_error(error: &crate::types::ProxyError) -> ApiResponse<T> {
        ApiResponse {
            success: false,
            data: None,
            message: Some(error.to_string()),
            error: Some(error.to_string()),
            error_code: Some(format!("{:?}", error.kind)),
            request_id: None,
        }
    }

    /// 从任何错误类型创建错误响应
    pub fn error_any(error: impl std::fmt::Display) -> ApiResponse<T> {
        ApiResponse {
            success: false,
            data: None,
            message: Some(error.to_string()),
            error: Some(error.to_string()),
            error_code: Some("ERROR".to_string()),
            request_id: None,
        }
    }

    /// 兼容老版本的错误转换函数
    pub fn error_from_proxy_error_compat(error: &crate::types::ProxyError) -> ApiResponse<T> {
        Self::error_from_local_proxy_error(error)
    }
}

/// API结果类型 - 统一的API返回类型
pub type ApiResult<T> = std::result::Result<Json<ApiResponse<T>>, String>;

/// 便利函数 - 创建成功的API响应
pub fn api_success<T>(data: T) -> ApiResult<T> {
    Ok(Json(ApiResponse::success(data)))
}

/// 便利函数 - 创建成功的API响应（带消息）
pub fn api_success_with_message<T>(data: T, message: impl Into<String>) -> ApiResult<T> {
    Ok(Json(ApiResponse::success_with_message(data, message)))
}

/// 便利函数 - 创建错误的API响应
pub fn api_error<T>(error: String) -> ApiResult<T> {
    Err(error)
}

/// 健康检查响应
pub async fn health_check() -> impl IntoResponse {
    Json(ApiResponse::success("OK"))
}

/// API版本响应
pub async fn version() -> Json<ApiResponse<&'static str>> {
    Json(ApiResponse::success("Reverse Proxy API v1.0"))
}

// 注释掉复杂的路由器，使用简化版本
// pub fn create_api_router(state: Arc<AppState>) -> Router<()> { ... }

/// 简化的路由器创建函数（用于main.rs）
pub async fn create_router(
    database: Arc<dyn crate::types::Database>,
) -> crate::types::ProxyResult<Router> {
    create_router_with_services(database, None).await
}

/// 带服务的路由器创建函数
pub async fn create_router_with_services(
    database: Arc<dyn crate::types::Database>,
    domain_pool_service: Option<Arc<crate::domain_pool::DomainPoolService>>,
) -> crate::types::ProxyResult<Router> {
    use tower_http::services::ServeDir;

    // 创建简化的应用状态
    #[derive(Clone)]
    struct SimpleAppState {
        database: Arc<dyn crate::types::Database>,
        auth_service: crate::auth::AuthService,
        domain_pool_service: Option<Arc<crate::domain_pool::DomainPoolService>>,
    }

    // 创建认证服务
    let auth_service = crate::auth::AuthService::new(database.clone());

    // 确保默认管理员用户存在
    if let Err(e) = auth_service.ensure_admin_user().await {
        tracing::warn!("创建默认管理员用户失败: {}", e);
    }

    let app_state = crate::types::SimpleAppState {
        database,
        auth_service,
        domain_pool_service,
        user: None, // 初始化时设置为None
    };

    // 创建公开的API路由（无需认证）
    let public_routes = Router::new()
        .route("/api/health", get(simple_health_check))
        .route("/api/version", get(simple_version))
        .route("/api/auth/login", post(simple_login));

    // 创建需要认证的API路由
    let protected_routes = Router::new()
        .route("/api/auth/logout", post(simple_logout))
        .route("/api/auth/change-password", post(simple_change_password))
        // 域名管理
        .route(
            "/api/domains",
            get(simple_list_domains).post(simple_create_domain),
        )
        .route(
            "/api/domains/:id",
            get(simple_get_domain)
                .put(simple_update_domain)
                .delete(simple_delete_domain),
        )
        .route("/api/domains/batch", post(simple_batch_domains))
        // 域名组管理
        .route(
            "/api/domain-groups",
            get(simple_list_domain_groups).post(simple_create_domain_group),
        )
        .route(
            "/api/domain-groups/:id",
            get(simple_get_domain_group)
                .put(simple_update_domain_group)
                .delete(simple_delete_domain_group),
        )
        // 系统状态
        .route("/api/status", get(simple_system_status))
        .route("/api/stats", get(simple_system_stats))
        // 配置管理
        .route(
            "/api/config",
            get(simple_get_config).post(simple_update_config),
        )
        // 添加JWT认证中间件
        .layer(axum::middleware::from_fn_with_state(
            app_state.auth_service.clone(),
            jwt_auth_middleware,
        ));

    // 合并路由
    let api_routes = Router::new()
        .merge(public_routes)
        .merge(protected_routes)
        .with_state(app_state);

    // 创建完整的应用
    let app = Router::new()
        .merge(api_routes)
        // 静态文件服务
        .nest_service("/", ServeDir::new("frontend"))
        .layer(TraceLayer::new_for_http());

    Ok(app)
}

/// 简化的健康检查
async fn simple_health_check() -> axum::Json<crate::types::ApiResponse<serde_json::Value>> {
    axum::Json(crate::types::ApiResponse::success(serde_json::json!("OK")))
}

/// 简化的版本信息
async fn simple_version() -> axum::Json<crate::types::ApiResponse<serde_json::Value>> {
    axum::Json(crate::types::ApiResponse::success(serde_json::json!(
        "SM Proxy v1.0.0"
    )))
}

// ==================== 认证API ====================

/// 用户登录
async fn simple_login(
    State(state): State<SimpleAppState>,
    Json(payload): Json<crate::auth::LoginRequest>,
) -> Json<ApiResponse<crate::auth::LoginResponse>> {
    match state
        .auth_service
        .login(&payload.username, &payload.password)
        .await
    {
        Ok(token) => {
            let response = crate::auth::LoginResponse {
                token,
                username: payload.username,
                role: "admin".to_string(), // 简化实现
                expires_in: 86400,         // 24小时
            };
            Json(ApiResponse::success(response))
        }
        Err(e) => Json(ApiResponse::<crate::auth::LoginResponse>::error_with_type(
            &format!("登录失败: {}", e),
        )),
    }
}

/// 用户登出
async fn simple_logout() -> Json<ApiResponse<&'static str>> {
    Json(ApiResponse::success("已登出"))
}

/// 修改密码
async fn simple_change_password(
    State(state): State<SimpleAppState>,
    Json(payload): Json<crate::auth::ChangePasswordRequest>,
) -> Json<ApiResponse<&'static str>> {
    // 这里应该从JWT token中获取用户名，简化实现使用admin
    let username = "admin";

    match state
        .auth_service
        .change_password(username, &payload.old_password, &payload.new_password)
        .await
    {
        Ok(()) => Json(ApiResponse::success("密码修改成功")),
        Err(e) => Json(ApiResponse::<&'static str>::error_with_type(&format!(
            "密码修改失败: {}",
            e
        ))),
    }
}

// ==================== 域名管理API ====================

/// 简化的域名列表
async fn simple_list_domains() -> Json<ApiResponse<serde_json::Value>> {
    Json(ApiResponse::success(serde_json::json!({
        "domains": [],
        "total": 0,
        "page": 1,
        "per_page": 10
    })))
}

/// 简化的创建域名
async fn simple_create_domain(
    Json(payload): Json<serde_json::Value>,
) -> Json<ApiResponse<serde_json::Value>> {
    Json(ApiResponse::success(serde_json::json!({
        "id": "domain_123",
        "domain": payload.get("domain").unwrap_or(&serde_json::Value::String("example.com".to_string())),
        "message": "域名创建成功"
    })))
}

/// 简化的获取域名
async fn simple_get_domain(Path(id): Path<String>) -> Json<ApiResponse<serde_json::Value>> {
    Json(ApiResponse::success(serde_json::json!({
        "id": id,
        "domain": "example.com",
        "backend_url": "http://localhost:8080",
        "status": "active"
    })))
}

/// 简化的更新域名
async fn simple_update_domain(
    Path(id): Path<String>,
    Json(_payload): Json<serde_json::Value>,
) -> Json<ApiResponse<serde_json::Value>> {
    Json(ApiResponse::success(serde_json::json!({
        "id": id,
        "message": "域名更新成功"
    })))
}

/// 简化的删除域名
async fn simple_delete_domain(Path(id): Path<String>) -> Json<ApiResponse<serde_json::Value>> {
    Json(ApiResponse::success(serde_json::json!({
        "id": id,
        "message": "域名删除成功"
    })))
}

/// 简化的批量域名操作
async fn simple_batch_domains(
    Json(payload): Json<serde_json::Value>,
) -> Json<ApiResponse<serde_json::Value>> {
    let domains = payload
        .get("domains")
        .and_then(|d| d.as_array())
        .map(|d| d.len())
        .unwrap_or(0);
    Json(ApiResponse::success(serde_json::json!({
        "processed": domains,
        "message": format!("批量处理 {} 个域名成功", domains)
    })))
}

// ==================== 域名组管理API ====================

/// 简化的域名组列表
async fn simple_list_domain_groups() -> Json<ApiResponse<serde_json::Value>> {
    Json(ApiResponse::success(serde_json::json!({
        "groups": [],
        "total": 0,
        "page": 1,
        "per_page": 10
    })))
}

/// 简化的创建域名组
async fn simple_create_domain_group(
    Json(payload): Json<serde_json::Value>,
) -> Json<ApiResponse<serde_json::Value>> {
    Json(ApiResponse::success(serde_json::json!({
        "id": "group_123",
        "name": payload.get("name").unwrap_or(&serde_json::Value::String("默认组".to_string())),
        "message": "域名组创建成功"
    })))
}

/// 简化的获取域名组
async fn simple_get_domain_group(Path(id): Path<String>) -> Json<ApiResponse<serde_json::Value>> {
    Json(ApiResponse::success(serde_json::json!({
        "id": id,
        "name": "默认组",
        "domains": [],
        "status": "active"
    })))
}

/// 简化的更新域名组
async fn simple_update_domain_group(
    Path(id): Path<String>,
    Json(_payload): Json<serde_json::Value>,
) -> Json<ApiResponse<serde_json::Value>> {
    Json(ApiResponse::success(serde_json::json!({
        "id": id,
        "message": "域名组更新成功"
    })))
}

/// 简化的删除域名组
async fn simple_delete_domain_group(
    Path(id): Path<String>,
) -> Json<ApiResponse<serde_json::Value>> {
    Json(ApiResponse::success(serde_json::json!({
        "id": id,
        "message": "域名组删除成功"
    })))
}

// ==================== 系统状态API ====================

/// 简化的系统状态
async fn simple_system_status() -> Json<ApiResponse<serde_json::Value>> {
    Json(ApiResponse::success(serde_json::json!({
        "status": "running",
        "uptime": "1h 30m",
        "memory_usage": "45%",
        "cpu_usage": "12%",
        "active_connections": 0,
        "total_requests": 0
    })))
}

/// 简化的系统统计
async fn simple_system_stats() -> Json<ApiResponse<serde_json::Value>> {
    Json(ApiResponse::success(serde_json::json!({
        "domains": 0,
        "groups": 0,
        "requests_today": 0,
        "cache_hit_rate": "0%",
        "avg_response_time": "0ms"
    })))
}

// ==================== 配置管理API ====================

/// 简化的获取配置
async fn simple_get_config() -> Json<ApiResponse<serde_json::Value>> {
    Json(ApiResponse::success(serde_json::json!({
        "cache_enabled": true,
        "cache_ttl": 3600,
        "rate_limit": 1000,
        "ssl_enabled": false,
        "compression_enabled": true
    })))
}

/// 简化的更新配置
async fn simple_update_config(
    Json(_payload): Json<serde_json::Value>,
) -> Json<ApiResponse<&'static str>> {
    Json(ApiResponse::success("配置更新成功"))
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_api_response_success() {
        let response = ApiResponse::success("test data");
        assert!(response.success);
        assert_eq!(response.data, Some("test data"));
        assert!(response.error.is_none());
    }

    #[test]
    fn test_api_response_error() {
        let error_msg = "用户名或密码错误";
        let response = ApiResponse::error_from_string(error_msg);

        assert!(!response.success);
        assert!(response.data.is_none());
        assert_eq!(response.error, Some(error_msg.to_string()));
        assert_eq!(response.error_code, Some("GENERIC_ERROR".to_string()));
    }

    #[test]
    fn test_api_response_with_request_id() {
        let response = ApiResponse::success("test").with_request_id("req-123");

        assert_eq!(response.request_id, Some("req-123".to_string()));
    }

    #[tokio::test]
    async fn test_health_check_response() {
        let response = health_check().await;
        // 健康检查应该返回JSON响应
        let response = response.into_response();
        assert_eq!(response.status(), StatusCode::OK);
    }
}
