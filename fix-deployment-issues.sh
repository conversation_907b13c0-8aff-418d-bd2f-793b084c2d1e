#!/bin/bash
# ═══════════════════════════════════════════════════════════════
# 🔧 SM智能代理系统 - 部署问题一键修复脚本
# 🎯 自动检测和修复常见的部署问题
# ═══════════════════════════════════════════════════════════════

set -euo pipefail

# 颜色输出
RED='\033[1;31m'
GREEN='\033[1;32m'
YELLOW='\033[1;33m'
BLUE='\033[1;34m'
CYAN='\033[1;36m'
WHITE='\033[1;37m'
NC='\033[0m'

# 配置
PROJECT_NAME="sm"
SERVICE_USER="proxy"
DEPLOY_PATH="/opt/sm"

echo -e "${GREEN}🔧 SM智能代理系统 - 部署问题一键修复${NC}"
echo -e "${BLUE}═══════════════════════════════════════════════════════${NC}"
echo -e "${WHITE}🎯 自动检测和修复常见的部署问题${NC}"
echo ""

# 检查权限
if [ "$(whoami)" != "root" ]; then
    echo -e "${RED}❌ 需要root权限执行修复${NC}"
    echo -e "${YELLOW}💡 请使用: sudo $0${NC}"
    exit 1
fi

# 停止服务
echo -e "${BLUE}🛑 停止SM服务...${NC}"
systemctl stop sm 2>/dev/null || true

# 修复1: 权限问题
echo -e "${BLUE}🔧 修复1: 权限问题...${NC}"
if [ -d "$DEPLOY_PATH" ]; then
    # 创建必需目录
    mkdir -p "$DEPLOY_PATH"/{keys,logs,data,temp,backup}
    
    # 设置正确的所有权
    chown -R "$SERVICE_USER:$SERVICE_USER" "$DEPLOY_PATH"
    
    # 设置正确的权限
    chmod 755 "$DEPLOY_PATH"
    chmod 755 "$DEPLOY_PATH"/{config,frontend,logs,data}
    chmod 700 "$DEPLOY_PATH"/{keys,temp,backup}
    
    # 设置文件权限
    if [ -f "$DEPLOY_PATH/sm" ]; then
        chmod 755 "$DEPLOY_PATH/sm"
    fi
    
    if [ -f "$DEPLOY_PATH/config/config.yaml" ]; then
        chmod 644 "$DEPLOY_PATH/config/config.yaml"
    fi
    
    echo -e "${GREEN}  ✅ 权限修复完成${NC}"
else
    echo -e "${YELLOW}  ⚠️  部署目录不存在，跳过权限修复${NC}"
fi

# 修复2: 配置文件问题
echo -e "${BLUE}🔧 修复2: 配置文件问题...${NC}"
if [ -f "$DEPLOY_PATH/config/config.yaml" ]; then
    # 检查是否有上游服务器配置
    if ! grep -q "upstream_servers:" "$DEPLOY_PATH/config/config.yaml"; then
        echo -e "${YELLOW}  ⚠️  添加缺失的上游服务器配置...${NC}"
        
        # 备份原配置
        cp "$DEPLOY_PATH/config/config.yaml" "$DEPLOY_PATH/config/config.yaml.backup.$(date +%Y%m%d_%H%M%S)"
        
        # 添加上游服务器配置
        cat >> "$DEPLOY_PATH/config/config.yaml" << 'EOF'

# 默认上游服务器配置
upstream_servers:
  - name: "default"
    address: "httpbin.org"
    port: 80
    protocol: "http"
    weight: 1
    health_check: true
    health_check_path: "/status"
    timeout: 30
EOF
        echo -e "${GREEN}  ✅ 上游服务器配置已添加${NC}"
    fi
    
    # 修复后端地址为仅本地访问
    if grep -q 'backend_addr: "0.0.0.0:1911"' "$DEPLOY_PATH/config/config.yaml"; then
        sed -i 's/backend_addr: "0.0.0.0:1911"/backend_addr: "127.0.0.1:1911"/' "$DEPLOY_PATH/config/config.yaml"
        echo -e "${GREEN}  ✅ 后端地址已修复为仅本地访问${NC}"
    fi
    
    echo -e "${GREEN}  ✅ 配置文件修复完成${NC}"
else
    echo -e "${YELLOW}  ⚠️  配置文件不存在，跳过配置修复${NC}"
fi

# 修复3: 环境变量问题
echo -e "${BLUE}🔧 修复3: 环境变量问题...${NC}"
if [ -f "$DEPLOY_PATH/.env" ]; then
    # 检查关键环境变量
    if ! grep -q "JWT_SECRET=" "$DEPLOY_PATH/.env"; then
        echo "JWT_SECRET=$(openssl rand -base64 64)" >> "$DEPLOY_PATH/.env"
        echo -e "${GREEN}  ✅ JWT_SECRET已添加${NC}"
    fi
    
    if ! grep -q "MONGODB_URI=" "$DEPLOY_PATH/.env"; then
        echo "MONGODB_URI=mongodb://localhost:27017/sm_proxy" >> "$DEPLOY_PATH/.env"
        echo -e "${GREEN}  ✅ MONGODB_URI已添加${NC}"
    fi
    
    # 设置正确权限
    chown "$SERVICE_USER:$SERVICE_USER" "$DEPLOY_PATH/.env"
    chmod 600 "$DEPLOY_PATH/.env"
    
    echo -e "${GREEN}  ✅ 环境变量修复完成${NC}"
else
    echo -e "${YELLOW}  ⚠️  环境变量文件不存在，跳过环境变量修复${NC}"
fi

# 修复4: MongoDB依赖
echo -e "${BLUE}🔧 修复4: MongoDB依赖...${NC}"
if ! systemctl is-active mongod >/dev/null 2>&1 && ! systemctl is-active mongodb >/dev/null 2>&1; then
    echo -e "${YELLOW}  ⚠️  MongoDB未运行，尝试启动...${NC}"
    
    # 尝试启动MongoDB
    if systemctl start mongod 2>/dev/null || systemctl start mongodb 2>/dev/null; then
        echo -e "${GREEN}  ✅ MongoDB启动成功${NC}"
    else
        echo -e "${RED}  ❌ MongoDB启动失败${NC}"
        echo -e "${YELLOW}  💡 请检查MongoDB安装状态${NC}"
    fi
else
    echo -e "${GREEN}  ✅ MongoDB运行正常${NC}"
fi

# 修复5: 服务文件问题
echo -e "${BLUE}🔧 修复5: 服务文件问题...${NC}"
if [ -f "/etc/systemd/system/sm.service" ]; then
    # 检查服务文件中的环境变量文件路径
    if ! grep -q "EnvironmentFile=-$DEPLOY_PATH/.env" "/etc/systemd/system/sm.service"; then
        echo -e "${YELLOW}  ⚠️  修复服务文件中的环境变量路径...${NC}"
        sed -i "s|EnvironmentFile=.*|EnvironmentFile=-$DEPLOY_PATH/.env|" "/etc/systemd/system/sm.service"
        systemctl daemon-reload
        echo -e "${GREEN}  ✅ 服务文件已修复${NC}"
    fi
else
    echo -e "${YELLOW}  ⚠️  服务文件不存在，跳过服务文件修复${NC}"
fi

# 验证修复结果
echo -e "${BLUE}🔍 验证修复结果...${NC}"

# 检查关键文件和目录
local issues_found=0

if [ ! -f "$DEPLOY_PATH/sm" ]; then
    echo -e "${RED}  ❌ 可执行文件不存在${NC}"
    issues_found=$((issues_found + 1))
fi

if [ ! -f "$DEPLOY_PATH/config/config.yaml" ]; then
    echo -e "${RED}  ❌ 配置文件不存在${NC}"
    issues_found=$((issues_found + 1))
fi

if [ ! -f "$DEPLOY_PATH/.env" ]; then
    echo -e "${RED}  ❌ 环境变量文件不存在${NC}"
    issues_found=$((issues_found + 1))
fi

if [ ! -d "$DEPLOY_PATH/keys" ] || [ ! -w "$DEPLOY_PATH/keys" ]; then
    echo -e "${RED}  ❌ 密钥目录问题${NC}"
    issues_found=$((issues_found + 1))
fi

if [ ! -d "$DEPLOY_PATH/logs" ] || [ ! -w "$DEPLOY_PATH/logs" ]; then
    echo -e "${RED}  ❌ 日志目录问题${NC}"
    issues_found=$((issues_found + 1))
fi

if [ $issues_found -eq 0 ]; then
    echo -e "${GREEN}✅ 所有问题已修复${NC}"
    
    # 尝试启动服务
    echo -e "${BLUE}🚀 尝试启动服务...${NC}"
    systemctl start sm
    sleep 3
    
    if systemctl is-active sm >/dev/null 2>&1; then
        echo -e "${GREEN}✅ 服务启动成功${NC}"
        echo -e "${WHITE}🌐 访问地址: http://your-server:1319${NC}"
        echo -e "${WHITE}👤 默认账户: admin / admin888${NC}"
    else
        echo -e "${YELLOW}⚠️  服务启动失败，查看日志:${NC}"
        echo -e "${CYAN}sudo journalctl -u sm -n 20${NC}"
    fi
else
    echo -e "${RED}❌ 仍有 $issues_found 个问题需要手动处理${NC}"
    echo -e "${YELLOW}💡 建议重新运行完整部署脚本${NC}"
fi

echo ""
echo -e "${WHITE}🎯 修复完成！${NC}"
echo -e "${CYAN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo -e "${WHITE}📋 如果问题仍然存在，请运行:${NC}"
echo -e "${CYAN}  sudo journalctl -u sm -f    # 查看实时日志${NC}"
echo -e "${CYAN}  sudo systemctl status sm    # 查看服务状态${NC}"
echo -e "${CYAN}  sudo ./setup.sh            # 重新完整部署${NC}"
