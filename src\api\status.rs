use crate::api::ApiResponse;
use crate::types::AppState;
use axum::{extract::Extension, response::IntoResponse, Json};
use chrono::Utc;
use serde_json::json;
use std::sync::Arc;

// 条件导入系统信息依赖
#[cfg(feature = "system-info")]
use hostname;
#[cfg(feature = "system-info")]
use local_ipaddress;
#[cfg(feature = "system-info")]
use num_cpus;
#[cfg(feature = "system-info")]
use sys_info;

pub async fn get_status(Extension(state): Extension<Arc<AppState>>) -> impl IntoResponse {
    // mock 数据
    let metrics = serde_json::json!({
        "cpu_usage_percent": 12.3,
        "memory_usage_mb": 512,
        "active_connections": 42
    });
    // 系统信息 - 条件编译
    #[cfg(feature = "system-info")]
    let hostname = hostname::get()
        .ok()
        .and_then(|h| h.into_string().ok())
        .unwrap_or_else(|| "unknown".to_string());
    #[cfg(not(feature = "system-info"))]
    let hostname = "unknown".to_string();

    let os_info = format!("{} {}", std::env::consts::OS, std::env::consts::ARCH);

    #[cfg(feature = "system-info")]
    let cpu_cores = num_cpus::get();
    #[cfg(not(feature = "system-info"))]
    let cpu_cores = 0;

    let uptime = get_uptime();

    #[cfg(feature = "system-info")]
    let ip = local_ipaddress::get().unwrap_or_else(|| "127.0.0.1".to_string());
    #[cfg(not(feature = "system-info"))]
    let ip = "127.0.0.1".to_string();

    let total_memory = "unknown";
    let version = env!("CARGO_PKG_VERSION");
    let uptime_fmt = format_uptime(uptime);
    Json(ApiResponse {
        success: true,
        message: Some("获取系统状态成功".to_string()),
        data: Some(json!({
            "cpu": format!("{:.2}%", metrics["cpu_usage_percent"].as_f64().unwrap_or(0.0)),
            "memory": format!("{} MB", metrics["memory_usage_mb"].as_i64().unwrap_or(0)),
            "disk": "计算中...",
            "connections": metrics["active_connections"].as_i64().unwrap_or(0),
            "version": version,
            "uptime": uptime_fmt,
            "hostname": hostname,
            "ip": ip,
            "os": os_info,
            "cpu_cores": cpu_cores,
            "total_memory": total_memory
        })),
        error_code: None,
        request_id: None,
        error: None,
    })
}

// 新增简化的系统统计API，只返回CPU、内存、磁盘使用率
pub async fn get_system_stats(Extension(state): Extension<Arc<AppState>>) -> impl IntoResponse {
    let metrics = state.perf.get_current_metrics().await;

    // 获取磁盘使用率
    let disk_usage = get_disk_usage();

    Json(ApiResponse {
        success: true,
        message: Some("获取系统统计成功".to_string()),
        data: Some(json!({
            "cpu_usage": metrics.cpu_usage_percent,
            "memory_usage": get_memory_usage_percentage(),
            "disk_usage": disk_usage
        })),
        error_code: None,
        request_id: None,
        error: None,
    })
}

fn get_memory_usage_percentage() -> f32 {
    #[cfg(feature = "system-info")]
    {
        match sys_info::mem_info() {
            Ok(mem) => {
                if mem.total > 0 {
                    ((mem.total - mem.free) as f32 / mem.total as f32) * 100.0
                } else {
                    0.0
                }
            }
            Err(_) => 0.0,
        }
    }
    #[cfg(not(feature = "system-info"))]
    {
        0.0 // 返回默认值
    }
}

fn get_disk_usage() -> f32 {
    #[cfg(feature = "system-info")]
    {
        match sys_info::disk_info() {
            Ok(disk) => {
                if disk.total > 0 {
                    ((disk.total - disk.free) as f32 / disk.total as f32) * 100.0
                } else {
                    0.0
                }
            }
            Err(_) => 0.0,
        }
    }
    #[cfg(not(feature = "system-info"))]
    {
        0.0 // 返回默认值
    }
}

fn format_uptime(seconds: u64) -> String {
    let days = seconds / 86400;
    let hours = (seconds % 86400) / 3600;
    let minutes = (seconds % 3600) / 60;
    if days > 0 {
        format!("{}天{}小时{}分钟", days, hours, minutes)
    } else if hours > 0 {
        format!("{}小时{}分钟", hours, minutes)
    } else {
        format!("{}分钟", minutes)
    }
}

// 获取系统运行时间 - Linux实现
fn get_uptime() -> u64 {
    // 读取 /proc/uptime 获取系统运行时间
    if let Ok(uptime_str) = std::fs::read_to_string("/proc/uptime") {
        if let Some(uptime_part) = uptime_str.split_whitespace().next() {
            if let Ok(uptime_seconds) = uptime_part.parse::<f64>() {
                return uptime_seconds as u64;
            }
        }
    }
    0
}

pub async fn get_services_status(ext: Extension<Arc<AppState>>) -> impl IntoResponse {
    let state = ext.0;
    let now = Utc::now();
    let uptime = now.signed_duration_since(state.service_start_time);
    let uptime_fmt = format_uptime(uptime.num_seconds() as u64);
    let cache_status = "运行中"; // 原 state.cache.is_running() 替换为常量
    let services = vec![
        serde_json::json!({
            "name": "代理服务", "status": "运行中", "uptime": uptime_fmt
        }),
        serde_json::json!({
            "name": "缓存服务", "status": cache_status, "uptime": uptime_fmt
        }),
        serde_json::json!({
            "name": "监控服务", "status": "运行中", "uptime": uptime_fmt
        }),
    ];
    Json(ApiResponse {
        success: true,
        message: Some("获取服务状态成功".to_string()),
        data: Some(services),
        error: None,
        error_code: None,
        request_id: None,
    })
}

pub async fn get_recent_events(ext: Extension<Arc<AppState>>) -> impl IntoResponse {
    let state = ext.0;
    let events = state.events.lock().await;
    let data: Vec<_> = events
        .iter()
        .rev()
        .take(20)
        .map(|e| {
            serde_json::json!({
                "time": e.timestamp.format("%Y-%m-%d %H:%M:%S").to_string(),
                "severity": &e.severity,
                "message": &e.message
            })
        })
        .collect();
    Json(ApiResponse {
        success: true,
        message: Some("获取最近事件成功".to_string()),
        data: Some(serde_json::Value::Array(data)),
        error_code: None,
        request_id: None,
        error: None,
    })
}

// 路由注册函数修正 AppState 路径
pub fn routes() -> axum::Router<Arc<AppState>> {
    axum::Router::new()
        .route("/api/status", axum::routing::get(get_status))
        .route("/api/system/stats", axum::routing::get(get_system_stats))
        .route(
            "/api/status/services",
            axum::routing::get(get_services_status),
        )
        .route("/api/events/recent", axum::routing::get(get_recent_events))
}
