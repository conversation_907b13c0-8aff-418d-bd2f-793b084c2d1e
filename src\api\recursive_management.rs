//! 递归代理管理API

use crate::recursive_proxy::{RecursiveProxyConfig, RecursiveProxyService};
use axum::{
    extract::ws::{Message, WebSocket, WebSocketUpgrade},
    extract::{Path, Query, State},
    http::StatusCode,
    response::{Json, Response},
    routing::{delete, get, post, put},
    Router,
};
use futures_util::{SinkExt, StreamExt};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::broadcast;

/// 递归代理状态
#[derive(Debug, Serialize, Deserialize)]
pub struct RecursiveProxyStatus {
    pub enabled: bool,
    pub active_sessions: usize,
    pub total_discovered_domains: u64,
    pub cache_hit_rate: f64,
    pub avg_response_time_ms: f64,
}

/// 机器学习状态
#[derive(Debug, Serialize, Deserialize)]
pub struct MLStatus {
    pub url_patterns_learned: usize,
    pub content_analysis_cache_size: usize,
    pub adaptive_strategies_active: usize,
    pub learning_enabled: bool,
}

/// 安全对抗状态
#[derive(Debug, Serialize, Deserialize)]
pub struct SecurityEvasionStatus {
    pub waf_bypass_enabled: bool,
    pub captcha_solver_enabled: bool,
    pub anti_bot_enabled: bool,
    pub detected_wafs: Vec<String>,
    pub bypass_success_rate: f64,
}

/// Webhook配置请求
#[derive(Debug, Deserialize)]
pub struct WebhookConfigRequest {
    pub url: String,
    pub enabled: bool,
    pub subscribed_events: Vec<String>,
    pub headers: HashMap<String, String>,
    pub timeout_seconds: u64,
    pub retry_count: u32,
    pub secret: Option<String>,
}

/// 查询参数
#[derive(Debug, Deserialize)]
pub struct QueryParams {
    pub limit: Option<usize>,
    pub offset: Option<usize>,
}

/// 应用状态（扩展版）
#[derive(Clone)]
pub struct ExtendedAppState {
    pub recursive_proxy: Arc<RecursiveProxyService>,
    pub event_sender: broadcast::Sender<WebSocketEvent>,
}

/// 创建递归代理管理路由
pub fn create_recursive_management_routes() -> Router<ExtendedAppState> {
    Router::new()
        // 递归代理管理
        .route(
            "/api/recursive-proxy/status",
            get(get_recursive_proxy_status),
        )
        .route("/api/recursive-proxy/start", post(start_recursive_proxy))
        .route("/api/recursive-proxy/stop", post(stop_recursive_proxy))
        .route(
            "/api/recursive-proxy/config",
            get(get_recursive_proxy_config),
        )
        .route(
            "/api/recursive-proxy/config",
            put(update_recursive_proxy_config),
        )
        .route("/api/recursive-proxy/sessions", get(get_active_sessions))
        .route(
            "/api/recursive-proxy/sessions/:session_id",
            get(get_session_details),
        )
        .route("/api/recursive-proxy/stats", get(get_recursive_proxy_stats))
        // 机器学习管理（占位符）
        .route("/api/ml/status", get(get_ml_status))
        .route("/api/ml/url-patterns", get(get_url_patterns))
        .route(
            "/api/ml/url-patterns/:pattern_id",
            get(get_url_pattern_details),
        )
        .route("/api/ml/content-analysis", get(get_content_analysis_stats))
        .route("/api/ml/adaptive-strategies", get(get_adaptive_strategies))
        // 安全对抗管理（占位符）
        .route("/api/security/status", get(get_security_evasion_status))
        .route("/api/security/waf-bypass", get(get_waf_bypass_config))
        .route("/api/security/waf-bypass", put(update_waf_bypass_config))
        .route(
            "/api/security/captcha-solver",
            get(get_captcha_solver_config),
        )
        .route("/api/security/anti-bot", get(get_anti_bot_config))
        // 监控和通知管理（占位符）
        .route("/api/monitoring/dashboard", get(get_dashboard_data))
        .route("/api/monitoring/events", get(get_system_events))
        .route("/api/monitoring/webhooks", get(get_webhooks))
        .route("/api/monitoring/webhooks", post(add_webhook))
        .route("/api/monitoring/webhooks/:webhook_id", put(update_webhook))
        .route(
            "/api/monitoring/webhooks/:webhook_id",
            delete(remove_webhook),
        )
        .route(
            "/api/monitoring/webhooks/:webhook_id/test",
            post(test_webhook),
        )
        .route(
            "/api/monitoring/notifications/stats",
            get(get_notification_stats),
        )
        // 实时数据流
        .route("/api/realtime/stats", get(get_realtime_stats))
        .route("/api/realtime/events", get(get_realtime_events))
        .route("/api/realtime/ws", get(websocket_handler))
}

// 递归代理管理API实现
async fn get_recursive_proxy_status(
    State(state): State<ExtendedAppState>,
) -> Result<Json<RecursiveProxyStatus>, StatusCode> {
    let stats = state.recursive_proxy.get_stats().await;
    let sessions = state.recursive_proxy.get_active_sessions().await;

    Ok(Json(RecursiveProxyStatus {
        enabled: true,
        active_sessions: sessions.len(),
        total_discovered_domains: 0,
        cache_hit_rate: stats.cache_hit_rate,
        avg_response_time_ms: 0.0,
    }))
}

async fn get_recursive_proxy_config(
    State(_state): State<ExtendedAppState>,
) -> Result<Json<RecursiveProxyConfig>, StatusCode> {
    Ok(Json(RecursiveProxyConfig::default()))
}

async fn update_recursive_proxy_config(
    State(state): State<ExtendedAppState>,
    Json(config): Json<RecursiveProxyConfig>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    state.recursive_proxy.update_config(config).await;
    Ok(Json(
        serde_json::json!({"status": "success", "message": "配置已更新"}),
    ))
}

async fn get_active_sessions(
    State(state): State<ExtendedAppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    let sessions = state.recursive_proxy.get_active_sessions().await;
    Ok(Json(serde_json::json!({"sessions": sessions})))
}

async fn get_session_details(
    State(state): State<ExtendedAppState>,
    Path(session_id): Path<String>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    let sessions = state.recursive_proxy.get_active_sessions().await;
    if let Some(session) = sessions.get(&session_id) {
        Ok(Json(serde_json::json!(session)))
    } else {
        Err(StatusCode::NOT_FOUND)
    }
}

async fn get_recursive_proxy_stats(
    State(state): State<ExtendedAppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    let stats = state.recursive_proxy.get_stats().await;
    Ok(Json(serde_json::json!(stats)))
}

// 机器学习管理API实现（占位符）
async fn get_ml_status(
    State(_state): State<ExtendedAppState>,
) -> Result<Json<MLStatus>, StatusCode> {
    Ok(Json(MLStatus {
        url_patterns_learned: 0,
        content_analysis_cache_size: 0,
        adaptive_strategies_active: 0,
        learning_enabled: false,
    }))
}

async fn get_url_patterns(
    State(_state): State<ExtendedAppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    Ok(Json(serde_json::json!({"patterns": []})))
}

async fn get_url_pattern_details(
    State(_state): State<ExtendedAppState>,
    Path(_pattern_id): Path<String>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    Ok(Json(serde_json::json!({"pattern": null})))
}

async fn get_content_analysis_stats(
    State(_state): State<ExtendedAppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    Ok(Json(
        serde_json::json!({"cache_size": 0, "analysis_count": 0}),
    ))
}

async fn get_adaptive_strategies(
    State(_state): State<ExtendedAppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    Ok(Json(serde_json::json!({"strategies": []})))
}

// 安全对抗管理API实现（占位符）
async fn get_security_evasion_status(
    State(_state): State<ExtendedAppState>,
) -> Result<Json<SecurityEvasionStatus>, StatusCode> {
    Ok(Json(SecurityEvasionStatus {
        waf_bypass_enabled: false,
        captcha_solver_enabled: false,
        anti_bot_enabled: false,
        detected_wafs: vec![],
        bypass_success_rate: 0.0,
    }))
}

async fn get_waf_bypass_config(
    State(_state): State<ExtendedAppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    Ok(Json(
        serde_json::json!({"enabled": false, "strategies": []}),
    ))
}

async fn update_waf_bypass_config(
    State(_state): State<ExtendedAppState>,
    Json(_config): Json<serde_json::Value>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    Ok(Json(serde_json::json!({"status": "success"})))
}

async fn get_captcha_solver_config(
    State(_state): State<ExtendedAppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    Ok(Json(serde_json::json!({"enabled": false, "solvers": []})))
}

async fn get_anti_bot_config(
    State(_state): State<ExtendedAppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    Ok(Json(
        serde_json::json!({"enabled": false, "techniques": []}),
    ))
}

// 监控和通知管理API实现（占位符）
async fn get_dashboard_data(
    State(_state): State<ExtendedAppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    Ok(Json(serde_json::json!({"data": "dashboard"})))
}

async fn get_system_events(
    State(_state): State<ExtendedAppState>,
    Query(_params): Query<QueryParams>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    Ok(Json(serde_json::json!({"events": []})))
}

async fn get_webhooks(
    State(_state): State<ExtendedAppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    Ok(Json(serde_json::json!({"webhooks": []})))
}

async fn add_webhook(
    State(_state): State<ExtendedAppState>,
    Json(_request): Json<WebhookConfigRequest>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    Ok(Json(
        serde_json::json!({"status": "success", "message": "Webhook已添加"}),
    ))
}

async fn update_webhook(
    State(_state): State<ExtendedAppState>,
    Path(_webhook_id): Path<String>,
    Json(_request): Json<WebhookConfigRequest>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    Ok(Json(
        serde_json::json!({"status": "success", "message": "Webhook已更新"}),
    ))
}

async fn remove_webhook(
    State(_state): State<ExtendedAppState>,
    Path(_webhook_id): Path<String>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    Ok(Json(
        serde_json::json!({"status": "success", "message": "Webhook已删除"}),
    ))
}

async fn test_webhook(
    State(_state): State<ExtendedAppState>,
    Path(_webhook_id): Path<String>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    Ok(Json(serde_json::json!({"success": true})))
}

async fn get_notification_stats(
    State(_state): State<ExtendedAppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    Ok(Json(serde_json::json!({"stats": {}})))
}

// 实时数据API实现
async fn get_realtime_stats(
    State(_state): State<ExtendedAppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    Ok(Json(serde_json::json!({
        "real_time_stats": {},
        "performance_metrics": {},
        "timestamp": chrono::Utc::now()
    })))
}

async fn get_realtime_events(
    State(_state): State<ExtendedAppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    Ok(Json(
        serde_json::json!({"events": [], "timestamp": chrono::Utc::now()}),
    ))
}

// 递归代理启动/停止API
async fn start_recursive_proxy(
    State(state): State<ExtendedAppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    match state.recursive_proxy.start().await {
        Ok(_) => {
            let _ = state.event_sender.send(WebSocketEvent {
                event_type: "proxy_started".to_string(),
                data: serde_json::json!({
                    "timestamp": chrono::Utc::now(),
                    "message": "递归代理已启动"
                }),
            });

            Ok(Json(serde_json::json!({
                "success": true,
                "message": "递归代理已启动",
                "timestamp": chrono::Utc::now()
            })))
        }
        Err(e) => {
            tracing::error!("启动递归代理失败: {}", e);
            Ok(Json(serde_json::json!({
                "success": false,
                "error": "启动失败",
                "details": e.to_string()
            })))
        }
    }
}

async fn stop_recursive_proxy(
    State(state): State<ExtendedAppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    match state.recursive_proxy.stop().await {
        Ok(_) => {
            let _ = state.event_sender.send(WebSocketEvent {
                event_type: "proxy_stopped".to_string(),
                data: serde_json::json!({
                    "timestamp": chrono::Utc::now(),
                    "message": "递归代理已停止"
                }),
            });

            Ok(Json(serde_json::json!({
                "success": true,
                "message": "递归代理已停止",
                "timestamp": chrono::Utc::now()
            })))
        }
        Err(e) => {
            tracing::error!("停止递归代理失败: {}", e);
            Ok(Json(serde_json::json!({
                "success": false,
                "error": "停止失败",
                "details": e.to_string()
            })))
        }
    }
}

// WebSocket事件结构
#[derive(Debug, Clone)]
pub struct WebSocketEvent {
    pub event_type: String,
    pub data: serde_json::Value,
}

// WebSocket处理器
async fn websocket_handler(
    ws: WebSocketUpgrade,
    State(state): State<ExtendedAppState>,
) -> Response {
    ws.on_upgrade(|socket| handle_websocket(socket, state))
}

async fn handle_websocket(socket: WebSocket, state: ExtendedAppState) {
    let (mut sender, mut receiver) = socket.split();
    let event_receiver = state.event_sender.subscribe();

    let initial_data = serde_json::json!({
        "type": "connection_established",
        "timestamp": chrono::Utc::now(),
        "data": {
            "message": "WebSocket连接已建立"
        }
    });

    if sender
        .send(Message::Text(initial_data.to_string()))
        .await
        .is_err()
    {
        return;
    }

    let mut sender_clone = sender;
    // 只用 sender_clone，不再 tokio::spawn 事件推送，所有消息发送都在主循环处理，避免 clone SplitSink

    while let Some(msg) = receiver.next().await {
        match msg {
            Ok(Message::Text(text)) => {
                if let Ok(request) = serde_json::from_str::<serde_json::Value>(&text) {
                    if let Some(action) = request.get("action").and_then(|v| v.as_str()) {
                        match action {
                            "get_stats" => {
                                let response = serde_json::json!({
                                    "type": "stats_update",
                                    "data": {"stats": "placeholder"}
                                });
                                let _ =
                                    sender_clone.send(Message::Text(response.to_string())).await;
                            }
                            "ping" => {
                                let response = serde_json::json!({
                                    "type": "pong",
                                    "timestamp": chrono::Utc::now()
                                });
                                let _ =
                                    sender_clone.send(Message::Text(response.to_string())).await;
                            }
                            _ => {
                                let response = serde_json::json!({
                                    "type": "error",
                                    "message": "未知的操作"
                                });
                                let _ =
                                    sender_clone.send(Message::Text(response.to_string())).await;
                            }
                        }
                    }
                }
            }
            Ok(Message::Close(_)) => {
                break;
            }
            _ => {}
        }
    }
}
