//! URL提取器模块

use anyhow::{anyhow, Result};
use regex::Regex;
use serde_json::Value;
use std::collections::{HashMap, HashSet};
use url::Url;

/// URL提取器
pub struct UrlExtractor {
    /// HTML链接提取正则
    html_regex: Regex,
    /// URL模式正则
    url_regex: Regex,
}

impl UrlExtractor {
    /// 创建新的URL提取器
    pub fn new() -> Result<Self> {
        let html_regex = Regex::new(r#"href=["']([^"']+)["']"#)?;
        let url_regex = Regex::new(r#"https?://[^\s<>"']+[^\s<>"'.,;!?]"#)?;

        Ok(Self {
            html_regex,
            url_regex,
        })
    }

    /// 从响应内容中提取所有URL
    pub fn extract_urls(
        &self,
        content: &str,
        content_type: &str,
        base_url: &str,
    ) -> Result<Vec<String>> {
        let mut urls = HashSet::new();

        let content_lower = content_type.to_lowercase();
        if content_lower.contains("text/html") {
            urls.extend(self.extract_from_html(content, base_url)?);
        } else if content_lower.contains("application/json") {
            urls.extend(self.extract_from_json(content)?);
        } else {
            // 通用URL提取
            urls.extend(self.extract_generic_urls(content)?);
        }

        Ok(urls.into_iter().collect())
    }

    /// 从HTML内容中提取URL
    fn extract_from_html(&self, content: &str, base_url: &str) -> Result<Vec<String>> {
        let mut urls = Vec::new();
        let base = Url::parse(base_url)?;

        // 提取HTML标签中的URL
        for cap in self.html_regex.captures_iter(content) {
            if let Some(url_str) = cap.get(1) {
                let url_str = url_str.as_str();
                if let Ok(absolute_url) = self.resolve_url(url_str, &base) {
                    urls.push(absolute_url);
                }
            }
        }

        // 提取文本中的完整URL
        for cap in self.url_regex.captures_iter(content) {
            if let Some(url_match) = cap.get(0) {
                let url_str = url_match.as_str();
                if self.is_valid_url(url_str) {
                    urls.push(url_str.to_string());
                }
            }
        }

        Ok(urls)
    }

    /// 从JSON内容中提取URL
    fn extract_from_json(&self, content: &str) -> Result<Vec<String>> {
        let mut urls = Vec::new();

        if let Ok(json) = serde_json::from_str::<Value>(content) {
            self.extract_urls_from_json_value(&json, &mut urls);
        }

        Ok(urls)
    }

    /// 递归提取JSON值中的URL
    fn extract_urls_from_json_value(&self, value: &Value, urls: &mut Vec<String>) {
        match value {
            Value::String(s) => {
                if self.is_valid_url(s) {
                    urls.push(s.clone());
                } else {
                    // 检查字符串中是否包含URL
                    for cap in self.url_regex.captures_iter(s) {
                        if let Some(url_match) = cap.get(0) {
                            urls.push(url_match.as_str().to_string());
                        }
                    }
                }
            }
            Value::Array(arr) => {
                for item in arr {
                    self.extract_urls_from_json_value(item, urls);
                }
            }
            Value::Object(obj) => {
                for (_, val) in obj {
                    self.extract_urls_from_json_value(val, urls);
                }
            }
            _ => {}
        }
    }

    /// 通用URL提取
    fn extract_generic_urls(&self, content: &str) -> Result<Vec<String>> {
        let mut urls = Vec::new();

        for cap in self.url_regex.captures_iter(content) {
            if let Some(url_match) = cap.get(0) {
                let url_str = url_match.as_str();
                if self.is_valid_url(url_str) {
                    urls.push(url_str.to_string());
                }
            }
        }

        Ok(urls)
    }

    /// 解析相对URL为绝对URL
    fn resolve_url(&self, url_str: &str, base: &Url) -> Result<String> {
        if url_str.starts_with("http://") || url_str.starts_with("https://") {
            Ok(url_str.to_string())
        } else if url_str.starts_with("//") {
            Ok(format!("{}:{}", base.scheme(), url_str))
        } else {
            let resolved = base.join(url_str)?;
            Ok(resolved.to_string())
        }
    }

    /// 验证URL是否有效
    fn is_valid_url(&self, url_str: &str) -> bool {
        if let Ok(url) = Url::parse(url_str) {
            matches!(url.scheme(), "http" | "https")
        } else {
            false
        }
    }

    /// 提取域名
    pub fn extract_domain(&self, url_str: &str) -> Result<String> {
        let url = Url::parse(url_str)?;
        url.host_str()
            .ok_or_else(|| anyhow!("无法提取域名"))
            .map(|s| s.to_string())
    }

    /// 提取唯一域名列表
    pub fn extract_unique_domains(&self, urls: &[String]) -> Vec<String> {
        let mut domains = HashSet::new();

        for url in urls {
            if let Ok(domain) = self.extract_domain(url) {
                domains.insert(domain);
            }
        }

        domains.into_iter().collect()
    }

    /// 替换URL中的域名
    pub fn replace_domain_in_content(
        &self,
        content: &str,
        domain_mapping: &HashMap<String, String>,
    ) -> String {
        let mut result = content.to_string();

        for (original_domain, replacement_domain) in domain_mapping {
            // 替换完整URL中的域名
            let pattern = format!(r"https?://{}", regex::escape(original_domain));
            let replacement = format!("http://{}", replacement_domain);

            if let Ok(regex) = Regex::new(&pattern) {
                result = regex.replace_all(&result, replacement.as_str()).to_string();
            }
        }

        result
    }
}

impl Default for UrlExtractor {
    fn default() -> Self {
        Self::new().expect("Failed to create UrlExtractor")
    }
}
