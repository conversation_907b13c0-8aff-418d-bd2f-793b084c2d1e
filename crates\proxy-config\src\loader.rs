//! 配置加载器 - 支持多种格式的配置文件加载

use crate::{unified::UnifiedConfig, ConfigError};
use serde::{Deserialize, Serialize};
use std::path::{Path, PathBuf};
use tracing::info;

/// 支持的配置格式
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ConfigFormat {
    Yaml,
    Json,
    Toml,
}

impl ConfigFormat {
    /// 从文件扩展名推断格式
    pub fn from_extension(ext: &str) -> Option<Self> {
        match ext.to_lowercase().as_str() {
            "yaml" | "yml" => Some(ConfigFormat::Yaml),
            "json" => Some(ConfigFormat::Json),
            "toml" => Some(ConfigFormat::Toml),
            _ => None,
        }
    }

    /// 获取格式的字符串表示
    pub fn as_str(&self) -> &'static str {
        match self {
            ConfigFormat::Yaml => "yaml",
            ConfigFormat::Json => "json",
            ConfigFormat::Toml => "toml",
        }
    }
}

impl std::fmt::Display for ConfigFormat {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.as_str())
    }
}

/// 配置加载器
pub struct ConfigLoader;

impl ConfigLoader {
    /// 从文件加载配置
    pub fn load_from_file<P: AsRef<Path>>(path: P) -> Result<UnifiedConfig, ConfigError> {
        let path = path.as_ref();
        let content = std::fs::read_to_string(path)
            .map_err(|e| ConfigError::FileError(format!("读取配置文件失败: {}", e)))?;

        let format = validate_config_format(path)?;
        Self::load_from_content(&content, format)
    }

    /// 从字符串内容加载配置
    pub fn load_from_content(
        content: &str,
        format: ConfigFormat,
    ) -> Result<UnifiedConfig, ConfigError> {
        let config = match format {
            ConfigFormat::Yaml => serde_yaml::from_str(content)
                .map_err(|e| ConfigError::ParseError(format!("YAML解析失败: {}", e)))?,
            ConfigFormat::Json => serde_json::from_str(content)
                .map_err(|e| ConfigError::ParseError(format!("JSON解析失败: {}", e)))?,
            ConfigFormat::Toml => toml::from_str(content)
                .map_err(|e| ConfigError::ParseError(format!("TOML解析失败: {}", e)))?,
        };

        // 验证配置
        crate::validation::ConfigValidator::validate_config(&config)
            .map_err(|e_vec| ConfigError::ValidationError(e_vec.join("; ")))?;

        Ok(config)
    }

    /// 保存配置到文件
    pub fn save_to_file<P: AsRef<Path>>(
        config: &UnifiedConfig,
        path: P,
        format: ConfigFormat,
    ) -> Result<(), ConfigError> {
        let content = match format {
            ConfigFormat::Yaml => serde_yaml::to_string(config)
                .map_err(|e| ConfigError::ConversionError(format!("YAML序列化失败: {}", e)))?,
            ConfigFormat::Json => serde_json::to_string_pretty(config)
                .map_err(|e| ConfigError::ConversionError(format!("JSON序列化失败: {}", e)))?,
            ConfigFormat::Toml => toml::to_string_pretty(config)
                .map_err(|e| ConfigError::ConversionError(format!("TOML序列化失败: {}", e)))?,
        };

        std::fs::write(path, content)
            .map_err(|e| ConfigError::FileError(format!("写入配置文件失败: {}", e)))?;

        Ok(())
    }

    /// 从多个文件加载并合并配置
    pub fn load_from_files<P: AsRef<Path>>(paths: &[P]) -> Result<UnifiedConfig, ConfigError> {
        if paths.is_empty() {
            return Err(ConfigError::ValidationError(
                "至少需要指定一个配置文件".to_string(),
            ));
        }

        let mut merged_config = None;

        for path in paths {
            let config = Self::load_from_file(path)?;

            if let Some(base_config) = merged_config {
                merged_config = Some(crate::unified::ConfigConverter::merge_configs(
                    &base_config,
                    &config,
                )?);
            } else {
                merged_config = Some(config);
            }
        }

        merged_config.ok_or_else(|| {
            ConfigError::ValidationError("配置合并失败: 没有有效的配置文件".to_string())
        })
    }

    /// 验证配置文件
    pub fn validate_file<P: AsRef<Path>>(path: P) -> Result<(), ConfigError> {
        let config = Self::load_from_file(path)?;
        crate::validation::ConfigValidator::validate_config(&config)
            .map_err(|e_vec| ConfigError::ValidationError(e_vec.join("; ")))?;
        Ok(())
    }
}

/// 验证配置文件格式
pub fn validate_config_format<P: AsRef<Path>>(path: P) -> Result<ConfigFormat, ConfigError> {
    let path = path.as_ref();

    if !path.exists() {
        return Err(ConfigError::FileError(format!(
            "配置文件不存在: {:?}",
            path
        )));
    }

    if !path.is_file() {
        return Err(ConfigError::FileError(format!("路径不是文件: {:?}", path)));
    }

    let extension = path
        .extension()
        .and_then(|ext| ext.to_str())
        .ok_or_else(|| ConfigError::ValidationError("配置文件缺少扩展名".to_string()))?;

    ConfigFormat::from_extension(extension)
        .ok_or_else(|| ConfigError::UnsupportedFormat(format!("不支持的文件格式: {}", extension)))
}

/// 备份配置文件
pub fn backup_config<P: AsRef<Path>>(config_path: P) -> Result<PathBuf, ConfigError> {
    let path = config_path.as_ref();
    let backup_path = path.with_extension(format!(
        "{}.backup",
        path.extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("yaml")
    ));

    std::fs::copy(path, &backup_path)
        .map_err(|e| ConfigError::FileError(format!("备份配置文件失败: {}", e)))?;

    info!("配置文件已备份到: {:?}", backup_path);
    Ok(backup_path)
}

/// 检查配置文件权限（仅Linux）
pub fn check_config_permissions<P: AsRef<Path>>(path: P) -> Result<(), ConfigError> {
    #[cfg(unix)]
    {
        use std::os::unix::fs::PermissionsExt;

        let path = path.as_ref();

        if !path.exists() {
            return Err(ConfigError::FileError(format!(
                "配置文件不存在: {:?}",
                path
            )));
        }

        let metadata = std::fs::metadata(path)
            .map_err(|e| ConfigError::FileError(format!("无法读取文件元数据: {}", e)))?;

        if metadata.is_dir() {
            return Err(ConfigError::FileError(format!(
                "路径是目录而不是文件: {:?}",
                path
            )));
        }

        let mode = metadata.permissions().mode();

        // 检查文件是否对其他用户可读
        if mode & 0o044 != 0 {
            info!("配置文件对其他用户可读，可能存在安全风险: {:?}", path);
        }

        // 检查文件是否对其他用户可写
        if mode & 0o022 != 0 {
            info!("配置文件对其他用户可写，存在安全风险: {:?}", path);
        }
    }

    #[cfg(not(unix))]
    {
        let path = path.as_ref();
        if !path.exists() {
            return Err(ConfigError::FileError(format!(
                "配置文件不存在: {:?}",
                path
            )));
        }

        let metadata = std::fs::metadata(path)
            .map_err(|e| ConfigError::FileError(format!("无法读取文件元数据: {}", e)))?;

        if metadata.is_dir() {
            return Err(ConfigError::FileError(format!(
                "路径是目录而不是文件: {:?}",
                path
            )));
        }

        info!("配置文件权限检查跳过 (非Unix系统): {:?}", path);
    }

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use tempfile::tempdir;

    #[test]
    fn test_config_format_detection() {
        assert_eq!(
            ConfigFormat::from_extension("yaml"),
            Some(ConfigFormat::Yaml)
        );
        assert_eq!(
            ConfigFormat::from_extension("yml"),
            Some(ConfigFormat::Yaml)
        );
        assert_eq!(
            ConfigFormat::from_extension("json"),
            Some(ConfigFormat::Json)
        );
        assert_eq!(
            ConfigFormat::from_extension("toml"),
            Some(ConfigFormat::Toml)
        );
        assert_eq!(ConfigFormat::from_extension("txt"), None);
    }

    #[test]
    fn test_load_yaml_config() {
        let dir = tempdir().unwrap();
        let config_path = dir.path().join("test.yaml");

        let config_content = r#"
server:
  listen: "0.0.0.0:8080"
  workers: 4
upstreams:
  - name: "backend"
    servers:
      - addr: "127.0.0.1:3000"
        weight: 1
        max_fails: 3
        fail_timeout: 30
    load_balance: "round_robin"
routes:
  - path: "/api"
    upstream: "backend"
    methods: ["GET", "POST"]
    headers: {}
security:
  rate_limit: null
  cors: null
"#;
        fs::write(&config_path, config_content).unwrap();

        let result = ConfigLoader::load_from_file(&config_path);
        assert!(result.is_ok());
    }

    #[test]
    fn test_validate_config_format() {
        let dir = tempdir().unwrap();
        let yaml_path = dir.path().join("test.yaml");
        fs::write(&yaml_path, "test: value").unwrap();

        let result = validate_config_format(&yaml_path);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), ConfigFormat::Yaml);
    }

    #[test]
    fn test_backup_config() {
        let dir = tempdir().unwrap();
        let config_path = dir.path().join("test.yaml");
        fs::write(&config_path, "test: value").unwrap();

        let result = backup_config(&config_path);
        assert!(result.is_ok());

        let backup_path = result.unwrap();
        assert!(backup_path.exists());
    }
}
