# SM代理系统 - 快速修复检查清单

## 🚨 紧急修复清单 (必须立即处理)

### ✅ 异步锁问题修复
- [ ] **文件**: `src/recursive_proxy/service.rs:94-96`
  - [ ] 将锁的作用域限制在代码块内
  - [ ] 确保锁在异步操作前释放
  - [ ] 测试：运行递归代理功能，确认不会死锁

- [ ] **文件**: `src/security/mod.rs:519-522`
  - [ ] 在 `detect_threats` 函数中克隆数据后释放锁
  - [ ] 测试：发送多个并发请求，确认安全检测正常

### ✅ 内存泄漏修复
- [ ] **文件**: `src/security/mod.rs:388-395`
  - [ ] 在 `log_event` 函数中添加事件数量限制
  - [ ] 设置最大事件数为 10,000，清理时保留 5,000
  - [ ] 测试：长时间运行，监控内存使用

- [ ] **文件**: `src/types.rs:1087-1094`
  - [ ] 在 `add_event` 函数中添加事件清理
  - [ ] 设置最大事件数为 1,000，清理时保留 900
  - [ ] 测试：检查事件列表大小是否受控

### ✅ Panic风险修复
- [ ] **全局搜索**: 查找所有 `.unwrap()` 调用
  ```bash
  grep -r "\.unwrap()" src/
  ```
- [ ] 将 `.unwrap()` 替换为适当的错误处理
- [ ] 特别检查：
  - [ ] 数据库连接相关的 unwrap
  - [ ] 网络请求相关的 unwrap
  - [ ] 配置解析相关的 unwrap

---

## ⚡ 性能优化清单 (影响用户体验)

### ✅ 字符串分配优化
- [ ] **文件**: `src/types.rs` 验证函数
  - [ ] 将返回类型从 `Result<(), String>` 改为 `Result<(), &'static str>`
  - [ ] 使用静态字符串替代 `format!()` 宏
  - [ ] 测试：验证功能正常，性能有提升

- [ ] **全局优化**: 减少不必要的 `.to_string()` 和 `.clone()`
  ```bash
  grep -r "\.to_string()" src/ | wc -l  # 统计数量
  grep -r "\.clone()" src/ | wc -l      # 统计数量
  ```

### ✅ 缓存优化
- [ ] **文件**: `src/recursive_proxy/cache.rs`
  - [ ] 简化缓存逻辑
  - [ ] 添加缓存命中率监控
  - [ ] 优化缓存清理策略

### ✅ 数据库查询优化
- [ ] **文件**: `src/services/database.rs`
  - [ ] 添加必要的数据库索引
  - [ ] 优化查询语句
  - [ ] 实现连接池监控

---

## 🔧 代码质量改进清单 (长期维护)

### ✅ 错误处理统一
- [ ] **创建**: `src/error.rs` 文件
  - [ ] 定义统一的 `AppError` 枚举
  - [ ] 实现 `From` trait 用于错误转换
  - [ ] 定义 `AppResult<T>` 类型别名

- [ ] **重构**: 现有错误处理
  - [ ] 替换 `ProxyError` 为 `AppError`
  - [ ] 统一错误信息格式
  - [ ] 添加错误链追踪

### ✅ 配置管理简化
- [ ] **创建**: `src/config.rs` 文件
  - [ ] 定义简化的配置结构
  - [ ] 实现环境变量支持
  - [ ] 添加配置验证

- [ ] **移除**: 复杂的配置系统
  - [ ] 简化 `crates/proxy-config/` 中的复杂逻辑
  - [ ] 保留核心配置功能

### ✅ 日志改进
- [ ] **添加**: 关键路径日志
  - [ ] 代理请求处理
  - [ ] 数据库操作
  - [ ] 错误发生时的上下文信息

- [ ] **优化**: 日志格式
  - [ ] 使用结构化日志
  - [ ] 添加请求ID追踪
  - [ ] 实现日志级别控制

---

## 🧪 测试验证清单

### ✅ 基本功能测试
- [ ] **启动测试**
  ```bash
  cargo build --release
  ./target/release/sm
  ```
- [ ] **Web界面测试**
  ```bash
  curl http://localhost:1319/api/health
  curl http://localhost:1319/api/version
  ```
- [ ] **代理功能测试**
  ```bash
  curl -x http://localhost:1911 http://httpbin.org/ip
  ```

### ✅ 稳定性测试
- [ ] **长时间运行测试**
  - [ ] 运行24小时，监控内存使用
  - [ ] 检查是否有内存泄漏
  - [ ] 验证系统响应正常

- [ ] **并发测试**
  ```bash
  # 使用 wrk 进行并发测试
  wrk -t4 -c100 -d30s http://localhost:1319/api/health
  ```

### ✅ 性能测试
- [ ] **响应时间测试**
  - [ ] 测试API响应时间 < 100ms
  - [ ] 测试代理响应时间 < 500ms
  - [ ] 监控CPU和内存使用率

- [ ] **内存使用测试**
  ```bash
  # 监控内存使用
  ps aux | grep sm
  top -p $(pgrep sm)
  ```

---

## 📊 修复进度跟踪

### 第一周目标 (稳定性)
- [ ] 完成所有异步锁问题修复
- [ ] 完成内存泄漏修复
- [ ] 完成Panic风险修复
- [ ] 通过24小时稳定性测试

### 第二周目标 (性能)
- [ ] 完成字符串分配优化
- [ ] 完成缓存优化
- [ ] 完成数据库查询优化
- [ ] 性能提升20%以上

### 第三周目标 (质量)
- [ ] 完成错误处理统一
- [ ] 完成配置管理简化
- [ ] 完成日志改进
- [ ] 代码质量评分提升到7/10

---

## 🚀 快速验证命令

### 检查修复效果
```bash
# 1. 编译检查
cargo check

# 2. 运行测试
cargo test

# 3. 启动服务
cargo run

# 4. 基本功能验证
curl http://localhost:1319/api/health
curl http://localhost:1319/api/version

# 5. 内存监控
ps aux | grep sm | awk '{print $6}' # 查看内存使用(KB)

# 6. 日志检查
tail -f logs/sm.log | grep -E "(ERROR|WARN|panic)"
```

### 性能基准测试
```bash
# API性能测试
time curl http://localhost:1319/api/health

# 代理性能测试  
time curl -x http://localhost:1911 http://httpbin.org/ip

# 并发测试
wrk -t2 -c10 -d10s http://localhost:1319/api/health
```

---

## ✅ 完成标准

### 稳定性标准
- [ ] 24小时连续运行无崩溃
- [ ] 内存使用稳定，无明显增长
- [ ] 并发请求处理正常

### 性能标准
- [ ] API响应时间 < 100ms
- [ ] 代理响应时间 < 500ms
- [ ] 内存使用 < 100MB (空闲时)

### 质量标准
- [ ] 无编译警告
- [ ] 错误信息清晰
- [ ] 日志信息完整

---

**检查清单版本**: v1.0  
**最后更新**: 2025年1月20日
