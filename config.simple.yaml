# SM代理系统 - 简化配置文件
# 这是一个简化的配置文件，保留核心功能，移除复杂的企业级配置

# 服务器配置
server:
  web_host: "127.0.0.1"      # Web管理界面地址
  web_port: 1319             # Web管理界面端口
  proxy_host: "127.0.0.1"    # 代理服务地址
  proxy_port: 1911           # 代理服务端口
  workers: 2                 # 工作线程数

# 数据库配置
database:
  mongodb_uri: "${MONGODB_URI:-mongodb://localhost:27017/sm}"
  redis_uri: "${REDIS_URI:-redis://localhost:6379/0}"
  connection_timeout: 30     # 连接超时（秒）
  max_connections: 100       # 最大连接数

# 安全配置
security:
  jwt_secret: "${JWT_SECRET:-change-me-in-production}"
  admin_username: "${ADMIN_USERNAME:-admin}"
  admin_password_hash: "${ADMIN_PASSWORD_HASH:-change-me-in-production}"
  session_timeout: 1800      # 会话超时（秒）
  max_login_attempts: 3      # 最大登录尝试次数
  lockout_duration: 3600     # 锁定时长（秒）
  rate_limit_enabled: true   # 是否启用速率限制
  rate_limit_per_minute: 100 # 每分钟请求限制

# 日志配置
logging:
  level: "${LOG_LEVEL:-info}"
  file_path: "./logs/app.log"
  max_size_mb: 100           # 日志文件最大大小（MB）
  max_files: 10              # 保留的日志文件数量
  compress: true             # 是否压缩旧日志

# 缓存配置
cache:
  max_size_mb: 256           # 缓存最大大小（MB）
  default_ttl_seconds: 1800  # 默认TTL（秒）
  cleanup_interval_seconds: 300  # 清理间隔（秒）

# 上游服务器配置（空配置，递归功能会自动添加）
upstream_servers: []

# 递归代理配置
recursive_proxy:
  enabled: true              # 启用递归代理
  auto_discovery: true       # 自动发现上游
  max_depth: 3              # 最大递归深度
  timeout_seconds: 30       # 请求超时

# 环境变量说明：
# MONGODB_URI: MongoDB连接字符串
# REDIS_URI: Redis连接字符串（可选）
# JWT_SECRET: JWT密钥（生产环境必须设置）
# ADMIN_USERNAME: 管理员用户名
# ADMIN_PASSWORD_HASH: 管理员密码哈希
# LOG_LEVEL: 日志级别 (trace, debug, info, warn, error)
