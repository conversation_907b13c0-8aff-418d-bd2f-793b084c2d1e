//! Security module - 安全模块
pub mod config;
pub mod header_security;
pub mod input_validation; // 完善的输入验证模块
                          // pub mod input_validator; // 文件不存在，暂时注释
pub mod jwt_manager; // JWT密钥管理模块
pub mod manager; // 安全管理器

// 重新导出新的输入验证功能，替换原有的简单实现
pub use input_validation::{UnifiedValidator, ValidationConfig};

// 重新导出JWT管理功能

// 重新导出安全管理器

// 添加根级别的函数导出 - 不重复定义SecurityMonitor
pub fn generate_new_jwt_secret() -> String {
    use rand::{thread_rng, Rng};
    const CHARSET: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZ\
                             abcdefghijklmnopqrstuvwxyz\
                             0123456789+";
    const SECRET_LEN: usize = 64;

    let mut rng = thread_rng();
    (0..SECRET_LEN)
        .map(|_| {
            let idx = rng.gen_range(0..CHARSET.len());
            CHARSET[idx] as char
        })
        .collect()
}

pub fn update_jwt_secret_everywhere(new_secret: &str) -> Result<(), String> {
    // 更新环境变量
    std::env::set_var("JWT_SECRET", new_secret);

    // 尝试写入到配置文件（如果存在）
    let config_path = std::path::Path::new("config/security.conf");
    if let Ok(mut file) = std::fs::OpenOptions::new()
        .create(true)
        .write(true)
        .truncate(true)
        .open(config_path)
    {
        use std::io::Write;
        if let Err(e) = writeln!(file, "JWT_SECRET={}", new_secret) {
            tracing::warn!("写入JWT密钥到配置文件失败: {}", e);
        }
    }

    tracing::info!("JWT密钥已更新");
    Ok(())
}

pub async fn get_security_config() -> crate::types::SecurityConfiguration {
    crate::types::SecurityConfiguration::default()
}

// 核心依赖
use once_cell::sync::Lazy;
use parking_lot::RwLock;
use regex::Regex;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::net::IpAddr;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use thiserror::Error;
use tracing::{error, info, warn};
use uuid::Uuid;

use proxy_config::SecurityConfig;

// 预编译的正则表达式 - 避免重复编译
static SENSITIVE_PATTERNS: Lazy<Vec<regex::Regex>> = Lazy::new(|| {
    vec![
        // 密码相关
        regex::Regex::new(r"(?i)(password|pwd|pass|secret|key|token)[:=]\s*[^\s]+").unwrap(),
        // 邮箱地址
        regex::Regex::new(r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}").unwrap(),
        // 信用卡号
        regex::Regex::new(r"\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b").unwrap(),
        // 手机号
        regex::Regex::new(r"\b1[3-9]\d{9}\b").unwrap(),
        // IP地址的部分遮蔽
        regex::Regex::new(r"\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b").unwrap(),
    ]
});

#[derive(Error, Debug)]
pub enum SecurityError {
    #[error("访问被拒绝: {reason}")]
    AccessDenied { reason: String },

    #[error("IP地址被封禁: {ip}")]
    IpBlocked { ip: IpAddr },

    #[error("请求被恶意检测拦截: {reason}")]
    MaliciousRequest { reason: String },

    #[error("CSRF令牌验证失败")]
    CsrfTokenInvalid,

    #[error("认证失败: {reason}")]
    AuthenticationFailed { reason: String },

    #[error("授权失败: 权限不足")]
    AuthorizationFailed,

    #[error("会话过期或无效")]
    SessionInvalid,

    #[error("速率限制: {message}")]
    RateLimit { message: String },

    #[error("资源限制: {message}")]
    Resource { message: String },
}

/// 安全事件类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SecurityEventType {
    MaliciousRequest,
    IpBlocked,
    RateLimitExceeded,
    AuthenticationFailed,
    AuthorizationFailed,
    ResourceLimitExceeded,
    CsrfAttack,
    SqlInjectionAttempt,
    XssAttempt,
    DirectoryTraversalAttempt,
    BruteForceAttempt,
    SuspiciousLogin,
    PathTraversal,
    UnauthorizedAccess,
    SensitiveFileAccess,
}

/// 安全事件严重程度
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum SecuritySeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// 安全事件
#[derive(Debug, Clone, Serialize)]
pub struct SecurityEvent {
    pub id: String,
    pub event_type: SecurityEventType,
    pub severity: SecuritySeverity,
    pub source_ip: Option<IpAddr>,
    pub user_agent: Option<String>,
    pub url: Option<String>,
    pub description: String,
    pub timestamp: SystemTime,
    pub metadata: HashMap<String, String>,

    // 扩展字段
    pub username: Option<String>,
    pub path: Option<String>,
    pub method: Option<String>,
    pub attempts: Option<usize>,
    pub file_path: Option<String>,
    pub blocked: Option<bool>,
    pub reason: Option<String>,
    pub endpoint: Option<String>,
}

impl SecurityEvent {
    /// 创建可疑登录事件
    pub fn suspicious_login(
        username: String,
        ip: String,
        user_agent: String,
        reason: String,
    ) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            event_type: SecurityEventType::SuspiciousLogin,
            severity: SecuritySeverity::Medium,
            source_ip: ip.parse().ok(),
            user_agent: Some(user_agent),
            url: None,
            description: format!("可疑登录尝试: {}", reason),
            timestamp: SystemTime::now(),
            metadata: HashMap::new(),
            username: Some(username),
            path: None,
            method: None,
            attempts: None,
            file_path: None,
            blocked: None,
            reason: Some(reason),
            endpoint: None,
        }
    }

    /// 创建路径遍历事件
    pub fn path_traversal(ip: String, path: String, user_agent: String) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            event_type: SecurityEventType::PathTraversal,
            severity: SecuritySeverity::High,
            source_ip: ip.parse().ok(),
            user_agent: Some(user_agent),
            url: None,
            description: format!("路径遍历攻击: {}", path),
            timestamp: SystemTime::now(),
            metadata: HashMap::new(),
            username: None,
            path: Some(path),
            method: None,
            attempts: None,
            file_path: None,
            blocked: None,
            reason: None,
            endpoint: None,
        }
    }

    /// 提取IP地址
    pub fn get_ip(&self) -> Option<String> {
        self.source_ip.map(|ip| ip.to_string())
    }
}

/// 威胁检测规则
#[derive(Debug, Clone)]
pub struct ThreatRule {
    pub id: String,
    pub name: String,
    pub pattern: regex::Regex,
    pub severity: SecuritySeverity,
    pub action: ThreatAction,
    pub enabled: bool,
}

/// 威胁响应动作
#[derive(Debug, Clone, Copy)]
pub enum ThreatAction {
    Log,
    Block,
    BanIp,
    RateLimit,
}

/// IP封禁信息
#[derive(Debug, Clone)]
pub struct IpBan {
    pub ip: IpAddr,
    pub reason: String,
    pub banned_at: SystemTime,
    pub expires_at: Option<SystemTime>,
    pub ban_count: u32,
}

/// IP信誉信息
#[derive(Debug, Clone)]
pub struct IpReputation {
    pub threat_score: u32,
    pub last_seen: Instant,
    pub violations: Vec<SecurityEvent>,
}

/// 安全统计信息
#[derive(Debug, Serialize)]
pub struct SecurityStats {
    pub total_banned_ips: usize,
    pub active_sessions: usize,
    pub total_security_events: usize,
    pub recent_events_24h: usize,
    pub event_type_counts: HashMap<String, usize>,
    pub threat_rules_count: usize,
    pub high_risk_ips: usize,
}

/// 统一安全监控器 - 合并之前重复的实现
pub struct SecurityMonitor {
    config: SecurityConfig,

    // 威胁检测
    threat_rules: Arc<RwLock<Vec<ThreatRule>>>,

    // IP管理
    banned_ips: Arc<RwLock<HashMap<IpAddr, IpBan>>>,
    ip_reputation: Arc<RwLock<HashMap<String, IpReputation>>>,

    // 事件存储
    security_events: Arc<RwLock<Vec<SecurityEvent>>>,

    // 会话管理
    active_sessions: Arc<RwLock<HashMap<String, SessionInfo>>>,
}

/// 会话信息
#[derive(Debug, Clone)]
struct SessionInfo {
    user_id: String,
    created_at: SystemTime,
    last_access: SystemTime,
    ip_address: IpAddr,
    user_agent: String,
    permissions: Vec<String>,
}

impl SecurityMonitor {
    /// 创建新的安全监控器
    pub fn new(config: SecurityConfig) -> Self {
        let monitor = Self {
            config,
            threat_rules: Arc::new(RwLock::new(Vec::new())),
            banned_ips: Arc::new(RwLock::new(HashMap::new())),
            ip_reputation: Arc::new(RwLock::new(HashMap::new())),
            security_events: Arc::new(RwLock::new(Vec::new())),
            active_sessions: Arc::new(RwLock::new(HashMap::new())),
        };

        // 初始化威胁检测规则
        let monitor_clone = monitor.clone();
        tokio::spawn(async move {
            monitor_clone.initialize_threat_rules().await;
        });

        monitor
    }

    /// 初始化威胁检测规则
    async fn initialize_threat_rules(&self) {
        let mut rules = Vec::new();

        // SQL注入检测规则
        if let Ok(pattern) = regex::Regex::new(
            r"(?i)(union|select|insert|update|delete|drop|create|alter|exec|script|javascript|vbscript)",
        ) {
            rules.push(ThreatRule {
                id: "sql_injection".to_string(),
                name: "SQL注入检测".to_string(),
                pattern,
                severity: SecuritySeverity::High,
                action: ThreatAction::Block,
                enabled: true,
            });
        }

        // XSS检测规则
        if let Ok(pattern) =
            regex::Regex::new(r"(?i)(<script|javascript:|vbscript:|onload=|onerror=|onclick=)")
        {
            rules.push(ThreatRule {
                id: "xss_attack".to_string(),
                name: "XSS攻击检测".to_string(),
                pattern,
                severity: SecuritySeverity::High,
                action: ThreatAction::Block,
                enabled: true,
            });
        }

        // 路径遍历检测规则
        if let Ok(pattern) = regex::Regex::new(r"(\.\./|\.\.\\|%2e%2e%2f|%2e%2e%5c)") {
            rules.push(ThreatRule {
                id: "directory_traversal".to_string(),
                name: "目录遍历检测".to_string(),
                pattern,
                severity: SecuritySeverity::Medium,
                action: ThreatAction::Block,
                enabled: true,
            });
        }

        let mut threat_rules = self.threat_rules.write();
        *threat_rules = rules;

        info!(
            "威胁检测规则初始化完成，共加载 {} 条规则",
            threat_rules.len()
        );
    }

    /// 记录安全事件 - 统一的事件记录方法
    pub async fn log_event(&self, event: SecurityEvent) {
        // 记录事件
        {
            let mut events = self.security_events.write();
            events.push(event.clone());

            // 保持最近10000个事件
            if events.len() > 10000 {
                events.drain(0..5000);
            }
        }

        // 更新IP信誉
        if let Some(ip) = event.get_ip() {
            self.update_ip_reputation(ip, event.clone()).await;
        }

        // 记录日志
        self.log_security_event(&event).await;
    }

    /// 验证请求安全性 - 统一的请求验证
    pub async fn validate_request(
        &self,
        ip: IpAddr,
        user_agent: Option<&str>,
        url: &str,
        headers: &axum::http::HeaderMap,
        body: Option<&str>,
    ) -> Result<(), SecurityError> {
        // 1. 检查IP是否被封禁
        self.check_ip_ban(ip).await?;

        // 2. 进行威胁检测
        self.detect_threats(ip, user_agent, url, headers, body)
            .await?;

        // 3. 验证HTTP头部
        self.validate_headers(headers).await?;

        Ok(())
    }

    /// 获取安全统计信息 - 统一的统计方法
    pub async fn get_security_stats(&self) -> SecurityStats {
        let banned_ips = self.banned_ips.read();
        let events = self.security_events.read();
        let sessions = self.active_sessions.read();
        let reputation = self.ip_reputation.read();
        let threat_rules = self.threat_rules.read();

        // 统计最近24小时的事件
        let day_ago = SystemTime::now() - Duration::from_secs(24 * 3600);
        let recent_events: Vec<_> = events.iter().filter(|e| e.timestamp > day_ago).collect();

        let mut event_type_counts = HashMap::new();
        for event in &recent_events {
            let count = event_type_counts
                .entry(format!("{:?}", event.event_type))
                .or_insert(0);
            *count += 1;
        }

        let high_risk_ips = reputation.values().filter(|r| r.threat_score > 75).count();

        SecurityStats {
            total_banned_ips: banned_ips.len(),
            active_sessions: sessions.len(),
            total_security_events: events.len(),
            recent_events_24h: recent_events.len(),
            event_type_counts,
            threat_rules_count: threat_rules.len(),
            high_risk_ips,
        }
    }

    /// 检查IP威胁状态
    pub async fn is_threat_ip(&self, ip: &str) -> bool {
        let reputation = self.ip_reputation.read();
        if let Some(rep) = reputation.get(ip) {
            rep.threat_score > 50
        } else {
            false
        }
    }

    /// 封禁IP地址
    pub async fn ban_ip(&self, ip: IpAddr, reason: String, duration: Option<Duration>) {
        let expires_at = duration.map(|d| SystemTime::now() + d);

        let ban_info = IpBan {
            ip,
            reason: reason.clone(),
            banned_at: SystemTime::now(),
            expires_at,
            ban_count: 1,
        };

        let mut banned_ips = self.banned_ips.write();
        if let Some(existing_ban) = banned_ips.get_mut(&ip) {
            existing_ban.ban_count += 1;
            existing_ban.reason = reason.clone();
            existing_ban.banned_at = SystemTime::now();
            existing_ban.expires_at = expires_at;
        } else {
            banned_ips.insert(ip, ban_info);
        }

        info!("IP地址 {} 被封禁: {}", ip, reason);
    }

    // 私有方法
    async fn check_ip_ban(&self, ip: IpAddr) -> Result<(), SecurityError> {
        let banned_ips = self.banned_ips.read();
        if let Some(ban_info) = banned_ips.get(&ip) {
            if let Some(expires_at) = ban_info.expires_at {
                if SystemTime::now() > expires_at {
                    return Ok(()); // 封禁已过期
                }
            }
            return Err(SecurityError::IpBlocked { ip });
        }
        Ok(())
    }

    async fn detect_threats(
        &self,
        ip: IpAddr,
        user_agent: Option<&str>,
        url: &str,
        headers: &axum::http::HeaderMap,
        body: Option<&str>,
    ) -> Result<(), SecurityError> {
        // 先获取威胁规则的副本，避免跨 await 持有锁
        let threat_rules = {
            let rules = self.threat_rules.read();
            rules.clone()
        };

        // 构建检测目标
        let mut detection_targets = vec![url.to_string()];
        if let Some(ua) = user_agent {
            detection_targets.push(ua.to_string());
        }
        if let Some(body_content) = body {
            detection_targets.push(body_content.to_string());
        }

        // 应用威胁检测规则
        for rule in threat_rules.iter() {
            if !rule.enabled {
                continue;
            }

            for target in &detection_targets {
                if rule.pattern.is_match(target) {
                    match rule.action {
                        ThreatAction::Block => {
                            return Err(SecurityError::MaliciousRequest {
                                reason: format!("威胁检测规则触发: {}", rule.name),
                            });
                        }
                        ThreatAction::BanIp => {
                            self.ban_ip(ip, format!("威胁检测规则触发: {}", rule.name), None)
                                .await;
                            return Err(SecurityError::IpBlocked { ip });
                        }
                        _ => {}
                    }
                }
            }
        }

        Ok(())
    }

    async fn validate_headers(&self, headers: &axum::http::HeaderMap) -> Result<(), SecurityError> {
        // 检查Host头部注入
        if let Some(host) = headers.get("host") {
            if let Ok(host_str) = host.to_str() {
                if host_str.contains('\n') || host_str.contains('\r') {
                    return Err(SecurityError::MaliciousRequest {
                        reason: "Host头部包含非法字符".to_string(),
                    });
                }
            }
        }
        Ok(())
    }

    async fn update_ip_reputation(&self, ip: String, event: SecurityEvent) {
        let mut reputation = self.ip_reputation.write();
        let entry = reputation.entry(ip).or_insert_with(|| IpReputation {
            threat_score: 0,
            last_seen: Instant::now(),
            violations: Vec::new(),
        });

        entry.last_seen = Instant::now();
        entry.violations.push(event.clone());

        // 根据事件类型增加威胁分数
        let score_increase = match event.event_type {
            SecurityEventType::PathTraversal => 30,
            SecurityEventType::SensitiveFileAccess => 25,
            SecurityEventType::SuspiciousLogin => 15,
            SecurityEventType::RateLimitExceeded => 10,
            SecurityEventType::UnauthorizedAccess => 5,
            _ => 5,
        };

        entry.threat_score = (entry.threat_score + score_increase).min(100);

        if entry.violations.len() > 50 {
            entry.violations.remove(0);
        }
    }

    async fn log_security_event(&self, event: &SecurityEvent) {
        match &event.event_type {
            SecurityEventType::SuspiciousLogin => {
                warn!(
                    "🚨 可疑登录: 用户={:?}, IP={:?}",
                    event.username,
                    event.get_ip()
                );
            }
            SecurityEventType::PathTraversal => {
                error!(
                    "🚨 路径遍历攻击: IP={:?}, 路径={:?}",
                    event.get_ip(),
                    event.path
                );
            }
            _ => {
                warn!("🚨 安全事件: {:?}", event.event_type);
            }
        }
    }
}

impl Clone for SecurityMonitor {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            threat_rules: Arc::clone(&self.threat_rules),
            banned_ips: Arc::clone(&self.banned_ips),
            ip_reputation: Arc::clone(&self.ip_reputation),
            security_events: Arc::clone(&self.security_events),
            active_sessions: Arc::clone(&self.active_sessions),
        }
    }
}

/// 安全日志处理器 - 简化版，移除重复功能
pub struct SecureLogger {
    sensitive_patterns: Vec<Regex>,
}

impl SecureLogger {
    pub fn new() -> Self {
        Self {
            sensitive_patterns: SENSITIVE_PATTERNS.clone(),
        }
    }

    /// 清理敏感数据
    pub fn sanitize_log_input(&self, input: &str, max_len: usize) -> String {
        let mut sanitized = input.to_string();

        if sanitized.len() > max_len {
            sanitized.truncate(max_len);
            sanitized.push_str("...");
        }

        for pattern in &self.sensitive_patterns {
            sanitized = pattern.replace_all(&sanitized, "[REDACTED]").to_string();
        }

        sanitized
    }
}

impl Default for SecureLogger {
    fn default() -> Self {
        Self::new()
    }
}

/// 全局安全监控器实例
static SECURITY_MONITOR: tokio::sync::OnceCell<Arc<SecurityMonitor>> =
    tokio::sync::OnceCell::const_new();

/// 获取安全监控器
pub async fn get_security_monitor() -> Arc<SecurityMonitor> {
    SECURITY_MONITOR
        .get_or_init(|| async {
            let config = SecurityConfig::default(); // 或从配置文件加载
            Arc::new(SecurityMonitor::new(config))
        })
        .await
        .clone()
}

/// 记录安全事件的便捷函数
pub async fn log_security_event(event: SecurityEvent) {
    let monitor = get_security_monitor().await;
    monitor.log_event(event).await;
}

/// 检查IP威胁状态的便捷函数
pub async fn is_threat_ip(ip: &str) -> bool {
    let monitor = get_security_monitor().await;
    monitor.is_threat_ip(ip).await
}

// SecurityManager已从manager模块导入，删除重复定义

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sanitize_sensitive_data() {
        let logger = SecureLogger::new();
        let input = "password=mysecret123";
        let result = logger.sanitize_log_input(input, 100);
        assert!(!result.contains("mysecret123"));
    }
}
