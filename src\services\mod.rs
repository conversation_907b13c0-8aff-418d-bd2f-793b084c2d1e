pub mod database;
// pub mod security; // 文件不存在，暂时注释
pub mod events;
// 移除健康检查模块

use async_trait::async_trait;
use hyper::{Request, Response};
use std::sync::Arc;

use crate::types::{ProxyResult, User};
use anyhow::Result;

// 重新导出proxy-core中的缓存功能，移除重复的CacheService定义
// pub use proxy_cache::{Cache, ExtensionManager}; // 模块不存在，暂时注释

/// 基础服务 trait
#[async_trait]
pub trait Service {
    async fn start(&self) -> ProxyResult<()>;
    async fn stop(&self) -> ProxyResult<()>;
    fn name(&self) -> &'static str;
}

/// 数据库服务 trait
#[async_trait]
pub trait DatabaseService: Send + Sync {
    /// 连接数据库
    async fn connect(&self) -> ProxyResult<()>;

    /// 健康检查
    async fn health_check(&self) -> ProxyResult<bool>;

    /// 获取指定集合
    async fn get_collection<T>(&self, name: &str) -> ProxyResult<mongodb::Collection<T>>;

    /// 执行SQL查询（用于关系型数据库兼容）
    async fn execute(&self, query: &str, params: &[&str]) -> ProxyResult<u64>;

    /// 查询单条记录（用于关系型数据库兼容）
    async fn query_one(&self, query: &str, params: &[&str]) -> ProxyResult<Option<Vec<String>>>;

    /// 查询多条记录（用于关系型数据库兼容）
    async fn query_all(&self, query: &str, params: &[&str]) -> ProxyResult<Vec<Vec<String>>>;

    /// 开始事务（用于关系型数据库兼容）
    async fn begin_transaction(&self) -> ProxyResult<Box<dyn DatabaseTransaction + Send + Sync>>;
}

/// 数据库事务 trait
#[async_trait]
pub trait DatabaseTransaction: Send + Sync {
    async fn execute(&mut self, query: &str, params: &[&str]) -> ProxyResult<u64>;
    async fn commit(self: Box<Self>) -> ProxyResult<()>;
    async fn rollback(self: Box<Self>) -> ProxyResult<()>;
}

/// 安全服务 trait
#[async_trait]
pub trait SecurityService: Service {
    async fn authenticate(&self, token: &str) -> ProxyResult<User>;
    async fn authorize(&self, user: &User, resource: &str, action: &str) -> ProxyResult<bool>;
    async fn rate_limit_check(&self, ip: &str, endpoint: &str) -> ProxyResult<bool>;
    async fn is_ip_blocked(&self, ip: &str) -> ProxyResult<bool>;
}

/// 代理服务 trait
#[async_trait]
pub trait ProxyService: Service {
    async fn forward_request(
        &self,
        req: Request<hyper::body::Incoming>,
        client_ip: &str,
    ) -> ProxyResult<Response<hyper::body::Incoming>>;
    async fn health_check(&self) -> ProxyResult<bool>;
}

/// 缓存统计信息 - 简化版本，与proxy-cache兼容
#[derive(Debug, Clone)]
pub struct CacheStats {
    pub hit_rate: f64,
    pub miss_rate: f64,
    pub total_requests: u64,
    pub memory_usage: u64,
}

/// 事件服务 trait
#[async_trait]
pub trait EventService: Service {
    async fn publish(&self, topic: &str, payload: &[u8]) -> ProxyResult<()>;
    async fn subscribe(&self, topic: &str) -> ProxyResult<Box<dyn EventSubscriber + Send + Sync>>;
}

/// 事件订阅者 trait
#[async_trait]
pub trait EventSubscriber: Send + Sync {
    async fn next_event(&mut self) -> ProxyResult<Option<Event>>;
}

/// 事件类型
#[derive(Debug, Clone)]
pub struct Event {
    pub topic: String,
    pub payload: Vec<u8>,
    pub timestamp: std::time::SystemTime,
}

/// 后端服务器定义（简化版，移除负载均衡）
#[derive(Debug, Clone)]
pub struct Backend {
    pub id: String,
    pub host: String,
    pub port: u16,
    pub health: bool,
}

/// 服务管理器
pub struct ServiceManager {
    services: Vec<Arc<dyn Service + Send + Sync>>,
}

impl ServiceManager {
    pub fn new() -> Self {
        Self {
            services: Vec::new(),
        }
    }

    pub fn add_service(&mut self, service: Arc<dyn Service + Send + Sync>) {
        self.services.push(service);
    }

    pub async fn start_all(&self) -> Result<()> {
        for service in &self.services {
            service.start().await?;
            tracing::info!("服务 {} 启动成功", service.name());
        }
        Ok(())
    }

    pub async fn stop_all(&self) -> Result<()> {
        for service in self.services.iter().rev() {
            service.stop().await?;
            tracing::info!("服务 {} 已停止", service.name());
        }
        Ok(())
    }
}

// 重新导出具体实现 - 移除重复的缓存实现
// 移除健康检查器导出
