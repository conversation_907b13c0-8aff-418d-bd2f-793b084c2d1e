/**
 * 仪表板管理器
 * 负责仪表板数据的获取、显示和实时更新
 */
class DashboardManager {
    constructor() {
        this.updateInterval = null;
        this.isAuthenticated = false;
        this.refreshRate = 30000; // 30秒刷新一次

        this.init();
    }

    init() {
        // 监听认证状态变化
        document.addEventListener('authenticated', () => {
            this.isAuthenticated = true;
            this.startAutoUpdate();
            this.loadDashboardData();
        });

        document.addEventListener('logout', () => {
            this.isAuthenticated = false;
            this.stopAutoUpdate();
            this.resetDashboard();
        });

        // 监听标签切换
        document.addEventListener('tab-change', (e) => {
            if (e.detail.tab === 'dashboard') {
                this.refreshDashboard();
            }
        });

        // 绑定事件
        this.bindEvents();

        // 如果已经认证，立即加载数据
        if (window.authManager && window.authManager.isAuthenticated()) {
            this.isAuthenticated = true;
            this.loadDashboardData();
            this.startAutoUpdate();
        }
    }

    bindEvents() {
        // 刷新按钮
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshDashboard());
        }

        // 仪表板设置
        const dashboardSettings = document.getElementById('dashboard-settings');
        if (dashboardSettings) {
            dashboardSettings.addEventListener('click', () => this.showDashboardSettings());
        }

        // 快速操作按钮
        this.bindQuickActions();
    }

    bindQuickActions() {
        const quickActions = document.querySelectorAll('[data-action]');

        quickActions.forEach(btn => {
            btn.addEventListener('click', () => {
                const action = btn.dataset.action;
                this.handleQuickAction(action);
            });
        });
    }

    handleQuickAction(action) {
        switch (action) {
            case 'add-domain':
                this.showAddDomainModal();
                break;
            case 'start-task':
                this.showStartTaskModal();
                break;
            case 'batch-add':
                this.showBatchAddModal();
                break;
            case 'view-logs':
                this.switchToLogs();
                break;
            default:
                console.log(`未处理的快速操作: ${action}`);
        }
    }

    async loadDashboardData() {
        if (!this.isAuthenticated) return;

        try {
            // 并行加载各种数据
            const [statsData, healthData, metricsData] = await Promise.all([
                this.fetchStats(),
                this.fetchHealthStatus(),
                this.fetchMetrics()
            ]);

            // 更新各个面板
            this.updateMetrics(statsData);
            this.updateHealthStatus(healthData);
            this.updatePerformanceMetrics(metricsData);

            // 更新侧边栏状态
            if (window.sidebarManager) {
                window.sidebarManager.updateStatus(statsData);
            }

        } catch (error) {
            console.error('加载仪表板数据失败:', error);
            this.showError('加载仪表板数据失败，请检查网络连接');
        }
    }

    async fetchStats() {
        const response = await fetch('/api/stats', {
            headers: {
                'Authorization': `Bearer ${window.authManager.getToken()}`
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        return await response.json();
    }

    async fetchHealthStatus() {
        const response = await fetch('/api/public/health');

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        return await response.json();
    }

    async fetchMetrics() {
        const response = await fetch('/api/perf/metrics', {
            headers: {
                'Authorization': `Bearer ${window.authManager.getToken()}`
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        return await response.json();
    }

    updateMetrics(data) {
        // 更新关键指标卡片
        this.updateElement('total-domains', data.domains?.total || 0);
        this.updateElement('active-domains', data.domains?.active || 0);
        this.updateElement('running-tasks', data.tasks?.running || 0);
        this.updateElement('total-requests', data.requests?.total || 0);

        // 更新变化指标
        this.updateMetricChange('total-domains', data.domains?.today_added || 0, '今日新增');
        this.updateMetricChange('active-domains', data.domains?.health_rate || 100, '健康率', '%');
        this.updateMetricChange('running-tasks', data.tasks?.queued || 0, '排队中');
        this.updateMetricChange('total-requests', data.requests?.per_hour || 0, '/小时');
    }

    updateMetricChange(elementId, value, label, suffix = '') {
        const changeElement = document.querySelector(`#${elementId}`).closest('.metric-card').querySelector('.metric-change');
        if (changeElement) {
            changeElement.textContent = `${value}${suffix} ${label}`;
        }
    }

    updateHealthStatus(data) {
        const healthContainer = document.getElementById('health-status');
        if (!healthContainer) return;

        const isHealthy = data.success && data.data === 'OK';

        healthContainer.innerHTML = `
            <div class="health-status ${isHealthy ? 'healthy' : 'error'}">
                <div class="health-indicator">
                    <span class="status-dot ${isHealthy ? 'healthy' : 'error'}"></span>
                    <span class="status-text">${isHealthy ? '系统正常' : '系统异常'}</span>
                </div>
                <div class="health-details">
                    <div class="health-item">
                        <span class="label">运行时间:</span>
                        <span class="value">${this.formatUptime(data.uptime || 0)}</span>
                    </div>
                    <div class="health-item">
                        <span class="label">版本:</span>
                        <span class="value">v2.0.0</span>
                    </div>
                    <div class="health-item">
                        <span class="label">最后检查:</span>
                        <span class="value">${new Date().toLocaleTimeString()}</span>
                    </div>
                </div>
            </div>
        `;
    }

    updatePerformanceMetrics(data) {
        const perfContainer = document.querySelector('.perf-metrics');
        if (!perfContainer) return;

        if (!data || !data.success) {
            perfContainer.innerHTML = '<div class="error-message">性能数据加载失败</div>';
            return;
        }

        const metrics = data.data || {};

        perfContainer.innerHTML = `
            <div class="perf-grid">
                <div class="perf-item">
                    <div class="perf-label">CPU使用率</div>
                    <div class="perf-value">${metrics.cpu_usage || 0}%</div>
                    <div class="perf-bar">
                        <div class="perf-fill" style="width: ${metrics.cpu_usage || 0}%"></div>
                    </div>
                </div>
                <div class="perf-item">
                    <div class="perf-label">内存使用率</div>
                    <div class="perf-value">${metrics.memory_usage || 0}%</div>
                    <div class="perf-bar">
                        <div class="perf-fill" style="width: ${metrics.memory_usage || 0}%"></div>
                    </div>
                </div>
                <div class="perf-item">
                    <div class="perf-label">网络吞吐</div>
                    <div class="perf-value">${this.formatBytes(metrics.network_io || 0)}/s</div>
                </div>
                <div class="perf-item">
                    <div class="perf-label">响应时间</div>
                    <div class="perf-value">${metrics.response_time || 0}ms</div>
                </div>
            </div>
        `;
    }

    updateElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
            // 添加动画效果
            element.style.transform = 'scale(1.1)';
            element.textContent = this.formatNumber(value);

            setTimeout(() => {
                element.style.transform = 'scale(1)';
            }, 200);
        }
    }

    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 B';

        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    formatUptime(seconds) {
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);

        if (days > 0) {
            return `${days}天 ${hours}小时`;
        } else if (hours > 0) {
            return `${hours}小时 ${minutes}分钟`;
        } else {
            return `${minutes}分钟`;
        }
    }

    startAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }

        this.updateInterval = setInterval(() => {
            if (this.isAuthenticated && document.querySelector('#dashboard.active')) {
                this.loadDashboardData();
            }
        }, this.refreshRate);
    }

    stopAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    refreshDashboard() {
        // 显示刷新动画
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.style.transform = 'rotate(360deg)';
            setTimeout(() => {
                refreshBtn.style.transform = 'rotate(0deg)';
            }, 500);
        }

        this.loadDashboardData();
    }

    resetDashboard() {
        // 重置所有指标为初始状态
        this.updateElement('total-domains', 0);
        this.updateElement('active-domains', 0);
        this.updateElement('running-tasks', 0);
        this.updateElement('total-requests', 0);

        // 清空健康状态
        const healthContainer = document.getElementById('health-status');
        if (healthContainer) {
            healthContainer.innerHTML = '<div class="auth-required">需要认证后查看</div>';
        }

        // 清空性能指标
        const perfContainer = document.querySelector('.perf-metrics');
        if (perfContainer) {
            perfContainer.innerHTML = '<div class="auth-required">需要认证后查看</div>';
        }
    }

    showError(message) {
        // 显示错误通知
        if (window.showNotification) {
            window.showNotification(message, 'error');
        } else {
            console.error(message);
        }
    }

    // 快速操作方法
    showAddDomainModal() {
        window.dispatchEvent(new CustomEvent('show-modal', {
            detail: { modalType: 'add-domain' }
        }));
    }

    showStartTaskModal() {
        window.dispatchEvent(new CustomEvent('show-modal', {
            detail: { modalType: 'start-task' }
        }));
    }

    showBatchAddModal() {
        window.dispatchEvent(new CustomEvent('show-modal', {
            detail: { modalType: 'batch-add' }
        }));
    }

    switchToLogs() {
        // 切换到日志标签
        window.location.hash = 'logs';
    }

    showDashboardSettings() {
        console.log('显示仪表板设置');
        // TODO: 实现仪表板设置功能
    }

    // 添加实时活动日志
    addActivityLog(message, type = 'info') {
        const activityList = document.getElementById('recent-activity');
        if (!activityList) return;

        const activityItem = document.createElement('div');
        activityItem.className = 'activity-item';
        activityItem.innerHTML = `
            <span class="activity-time">${new Date().toLocaleTimeString()}</span>
            <span class="activity-text">${message}</span>
        `;

        // 添加到顶部
        activityList.insertBefore(activityItem, activityList.firstChild);

        // 限制显示数量
        const items = activityList.querySelectorAll('.activity-item');
        if (items.length > 10) {
            items[items.length - 1].remove();
        }
    }
}

// 初始化仪表板管理器
document.addEventListener('DOMContentLoaded', () => {
    window.dashboardManager = new DashboardManager();
});

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DashboardManager;
}