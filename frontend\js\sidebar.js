// SM镜像系统 - 侧边栏控制器 v2.0
// 负责侧边栏的显示/隐藏、导航切换、状态管理等功能

class SidebarController {
  constructor() {
    this.isCollapsed = false;
    this.activeNavItem = 'dashboard'; // 默认激活仪表板
    this.sidebar = null;
    this.sidebarToggle = null;
    this.navItems = [];

    this.init();
  }

  init() {
    console.log('🚀 初始化侧边栏控制器...');

    this.sidebar = document.querySelector('.sidebar');
    this.sidebarToggle = document.getElementById('sidebar-toggle');
    this.navItems = document.querySelectorAll('.nav-item');

    console.log(`📋 侧边栏元素: ${this.sidebar ? '✅' : '❌'}`);
    console.log(`🔘 切换按钮: ${this.sidebarToggle ? '✅' : '❌'}`);
    console.log(`📋 导航项数量: ${this.navItems.length}`);

    // 从本地存储加载侧边栏状态
    this.loadSidebarState();

    // 绑定事件
    this.bindEvents();

    // 应用初始状态
    this.applySidebarState();

    // 延迟设置默认激活项，确保DOM完全加载
    setTimeout(() => {
      console.log(`🎯 设置默认激活项: ${this.activeNavItem}`);
      this.setActiveNavItem(this.activeNavItem);

      console.log(`🎯 显示默认内容区域: ${this.activeNavItem}`);
      this.showContentSection(this.activeNavItem);

      console.log(`✅ 侧边栏控制器初始化完成，默认显示: ${this.activeNavItem}`);
    }, 100);
  }

  bindEvents() {
    console.log('🔗 绑定侧边栏事件...');

    // 侧边栏切换按钮 - 修复事件绑定
    if (this.sidebarToggle) {
      // 移除可能存在的旧事件监听器
      this.sidebarToggle.removeEventListener('click', this.toggleSidebar);

      // 绑定新的事件监听器
      this.sidebarToggle.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        console.log('🔘 侧边栏切换按钮被点击');
        this.toggleSidebar();
      });
      console.log('✅ 侧边栏切换按钮事件已绑定');
    } else {
      console.error('❌ 未找到侧边栏切换按钮');
    }

    // 导航项点击事件 - 直接绑定（备用方案）
    console.log(`📋 找到 ${this.navItems.length} 个导航项`);
    this.navItems.forEach((item, index) => {
      const target = item.dataset.tab;
      console.log(`🔗 绑定导航项 ${index + 1}: ${target}`);

      item.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        console.log(`🖱️ 直接点击导航项: ${target}`);

        if (target) {
          this.navigateTo(target);
        } else {
          console.error('❌ 导航项缺少 data-tab 属性');
        }
      });
    });

    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
      this.handleKeyboardShortcuts(e);
    });

    // 窗口大小变化时自动调整
    window.addEventListener('resize', () => {
      this.handleWindowResize();
    });

    // 监听点击外部区域（移动端）
    document.addEventListener('click', (e) => {
      this.handleOutsideClick(e);
    });

    console.log('✅ 所有侧边栏事件绑定完成');
  }

  toggleSidebar() {
    console.log(`🔄 切换侧边栏状态: ${this.isCollapsed ? '展开' : '收缩'}`);

    this.isCollapsed = !this.isCollapsed;
    this.applySidebarState();
    this.saveSidebarState();

    console.log(`✅ 侧边栏状态已更新: ${this.isCollapsed ? '收缩' : '展开'}`);

    // 触发侧边栏状态变化事件
    const event = new CustomEvent('sidebar-toggle', {
      detail: { collapsed: this.isCollapsed }
    });
    document.dispatchEvent(event);
  }

  applySidebarState() {
    if (!this.sidebar) {
      console.error('❌ 侧边栏元素不存在');
      return;
    }

    console.log(`🎯 应用侧边栏状态: ${this.isCollapsed ? '收缩' : '展开'}`);

    if (this.isCollapsed) {
      this.sidebar.classList.add('collapsed');
      document.body.classList.add('sidebar-collapsed');
    } else {
      this.sidebar.classList.remove('collapsed');
      document.body.classList.remove('sidebar-collapsed');
    }

    // 更新切换按钮图标和状态
    if (this.sidebarToggle) {
      this.sidebarToggle.innerHTML = this.isCollapsed ? '▶️' : '◀️';
      this.sidebarToggle.title = this.isCollapsed ? '展开侧边栏' : '收起侧边栏';
      this.sidebarToggle.setAttribute('aria-expanded', (!this.isCollapsed).toString());
    }

    console.log(`✅ 侧边栏状态应用完成: ${this.isCollapsed ? '收缩' : '展开'}`);
  }

  navigateTo(target) {
    console.log(`🔄 导航到: ${target}`);

    // 设置激活状态
    this.setActiveNavItem(target);

    // 显示对应的内容区域
    this.showContentSection(target);

    // 在移动端自动收起侧边栏
    if (window.innerWidth <= 768) {
      this.isCollapsed = true;
      this.applySidebarState();
    }

    // 触发导航事件
    const event = new CustomEvent('navigation', {
      detail: { target, previous: this.activeNavItem }
    });
    document.dispatchEvent(event);

    // 触发标签切换事件（用于其他模块监听）
    const tabEvent = new CustomEvent('tab-change', {
      detail: { tab: target }
    });
    document.dispatchEvent(tabEvent);

    // 更新当前激活项
    this.activeNavItem = target;

    // 保存状态
    this.saveSidebarState();

    console.log(`✅ 导航完成: ${target}`);
  }

  setActiveNavItem(target) {
    // 移除所有激活状态
    this.navItems.forEach(item => {
      item.classList.remove('active');
    });

    // 设置新的激活状态
    const activeItem = document.querySelector(`[data-tab="${target}"]`); // 修改为 data-tab
    if (activeItem) {
      activeItem.classList.add('active');
    }
  }

  showContentSection(target, retryCount = 0) {
    console.log(`🔍 查找内容区域: ${target} (尝试 ${retryCount + 1})`);

    // 隐藏所有内容区域
    const contentSections = document.querySelectorAll('.content-section');
    console.log(`📋 找到 ${contentSections.length} 个内容区域`);

    contentSections.forEach(section => {
      section.classList.remove('active');
      console.log(`❌ 隐藏区域: ${section.id}`);
    });

    // 显示目标内容区域
    const targetSection = document.getElementById(target);
    console.log(`🔍 查找目标区域 "${target}":`, targetSection);

    if (targetSection) {
      targetSection.classList.add('active');
      console.log(`✅ 显示区域: ${target}`);
      console.log(`📋 目标区域类名:`, targetSection.className);

      // 如果是仪表板，刷新数据
      if (target === 'dashboard' && window.dashboardManager) {
        window.dashboardManager.refreshDashboard();
      }

      // 如果是域名管理，刷新域名列表
      if (target === 'domains' && window.domainManager) {
        window.domainManager.refreshDomains();
      }

      // 如果是递归代理，刷新数据
      if (target === 'recursive-proxy' && window.recursiveProxyManager) {
        window.recursiveProxyManager.refreshData();
      }

      // 如果是机器学习分析，加载数据
      if (target === 'ml-analysis' && window.recursiveProxyManager) {
        window.recursiveProxyManager.loadMLAnalysis();
      }

      // 如果是安全对抗，加载状态
      if (target === 'security-evasion' && window.recursiveProxyManager) {
        window.recursiveProxyManager.loadSecurityStatus();
      }
    } else {
      console.error(`❌ 未找到内容区域: ${target}`);

      // 调试：列出所有可用的内容区域
      const allSections = document.querySelectorAll('.content-section');
      console.log('📋 所有可用的内容区域:');
      allSections.forEach(section => {
        console.log(`  - ID: ${section.id}, 类名: ${section.className}`);
      });

      // 如果是第一次尝试失败，延迟重试
      if (retryCount < 3) {
        console.log(`🔄 延迟重试显示内容区域: ${target}`);
        setTimeout(() => {
          this.showContentSection(target, retryCount + 1);
        }, 200 * (retryCount + 1));
      } else {
        console.error(`❌ 重试 ${retryCount + 1} 次后仍未找到内容区域: ${target}`);
      }
    }
  }

  handleKeyboardShortcuts(e) {
    // Ctrl/Cmd + B: 切换侧边栏
    if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
      e.preventDefault();
      this.toggleSidebar();
      return;
    }

    // Alt + 数字键: 快速导航
    if (e.altKey && !e.ctrlKey && !e.metaKey) {
      const shortcuts = {
        '1': 'dashboard',
        '2': 'domains',
        '3': 'domain-pool',  // 修复：从 'pool' 改为 'domain-pool'
        '4': 'tasks',
        '5': 'recursive-proxy',
        '6': 'ml-analysis',
        '7': 'security-evasion',
        '8': 'monitoring',
        '9': 'blacklist',
        '0': 'config'
      };

      const target = shortcuts[e.key];
      if (target) {
        e.preventDefault();
        this.navigateTo(target);
      }
    }
  }

  handleWindowResize() {
    const width = window.innerWidth;

    // 在移动端自动收起侧边栏（但不覆盖用户的手动设置）
    if (width <= 768 && !this.isCollapsed) {
      // 只有在之前是桌面端展开状态时才自动收起
      const wasDesktop = localStorage.getItem('sm_was_desktop') === 'true';
      if (wasDesktop) {
        this.isCollapsed = true;
        this.applySidebarState();
        this.saveSidebarState();
      }
    }

    // 在桌面端恢复之前的状态（不强制展开）
    if (width > 1024) {
      localStorage.setItem('sm_was_desktop', 'true');
      // 不自动展开，让用户自己控制
    } else {
      localStorage.setItem('sm_was_desktop', 'false');
    }
  }

  handleOutsideClick(e) {
    // 只在移动端处理外部点击
    if (window.innerWidth > 768) return;

    // 如果侧边栏已收起，不处理
    if (this.isCollapsed) return;

    // 如果点击的是侧边栏内部或切换按钮，不处理
    if (this.sidebar && (this.sidebar.contains(e.target) || e.target === this.sidebarToggle)) {
      return;
    }

    // 收起侧边栏
    this.isCollapsed = true;
    this.applySidebarState();
  }

  loadSidebarState() {
    // 修复状态加载逻辑
    const savedState = localStorage.getItem('sm_sidebar_collapsed');
    const isDesktop = window.innerWidth > 768;

    console.log(`📖 加载侧边栏状态 - 保存状态: ${savedState}, 屏幕宽度: ${window.innerWidth}px`);

    if (savedState !== null) {
      this.isCollapsed = savedState === 'true';
      console.log(`📋 使用保存的状态: ${this.isCollapsed ? '收缩' : '展开'}`);
    } else {
      // 默认状态：桌面端展开，移动端收缩
      this.isCollapsed = !isDesktop;
      console.log(`🆕 使用默认状态: ${this.isCollapsed ? '收缩' : '展开'}`);
    }

    // 加载上次访问的页面
    const savedPage = localStorage.getItem('sm_active_page');
    if (savedPage) {
      this.activeNavItem = savedPage;
      console.log(`📖 加载激活页面: ${savedPage}`);
    }
  }

  saveSidebarState() {
    try {
      localStorage.setItem('sm_sidebar_collapsed', this.isCollapsed.toString());
      localStorage.setItem('sm_active_page', this.activeNavItem);
      console.log(`💾 保存侧边栏状态: ${this.isCollapsed ? '收缩' : '展开'}, 激活页面: ${this.activeNavItem}`);
    } catch (error) {
      console.error('❌ 保存侧边栏状态失败:', error);
    }
  }

  // 公共方法：设置徽章数量
  setBadge(navTarget, count) {
    const navItem = document.querySelector(`[data-tab="${navTarget}"]`); // 修改为 data-tab
    if (!navItem) return;

    let badge = navItem.querySelector('.nav-badge');

    if (count > 0) {
      if (!badge) {
        badge = document.createElement('span');
        badge.className = 'nav-badge';
        navItem.appendChild(badge);
      }
      badge.textContent = count > 99 ? '99+' : count.toString();
      badge.style.display = 'inline-block';
    } else {
      if (badge) {
        badge.style.display = 'none';
      }
    }
  }

  // 公共方法：更新导航项状态
  updateNavStatus(navTarget, status) {
    const navItem = document.querySelector(`[data-tab="${navTarget}"]`); // 修改为 data-tab
    if (!navItem) return;

    // 移除所有状态类
    navItem.classList.remove('nav-success', 'nav-warning', 'nav-error');

    // 添加新状态类
    if (status) {
      navItem.classList.add(`nav-${status}`);
    }
  }

  // 公共方法：添加导航项
  addNavItem(config) {
    const { target, icon, text, position = 'bottom' } = config;

    const navItem = document.createElement('div');
    navItem.className = 'nav-item';
    navItem.dataset.tab = target; // 修改为 data-tab
    navItem.innerHTML = `
            <span class="nav-icon">${icon}</span>
            <span class="nav-text">${text}</span>
        `;

    // 绑定点击事件
    navItem.addEventListener('click', (e) => {
      e.preventDefault();
      this.navigateTo(target);
    });

    // 插入到合适位置
    const navList = this.sidebar.querySelector('.nav-list');
    if (position === 'top') {
      navList.insertBefore(navItem, navList.firstChild);
    } else {
      navList.appendChild(navItem);
    }

    // 更新导航项列表
    this.navItems = document.querySelectorAll('.nav-item');
  }

  // 公共方法：移除导航项
  removeNavItem(target) {
    const navItem = document.querySelector(`[data-tab="${target}"]`); // 修改为 data-tab
    if (navItem) {
      navItem.remove();
      this.navItems = document.querySelectorAll('.nav-item');
    }
  }

  // 公共方法：获取当前激活的导航项
  getActiveNavItem() {
    return this.activeNavItem;
  }

  // 公共方法：强制收起/展开侧边栏
  collapseSidebar(collapse = true) {
    this.isCollapsed = collapse;
    this.applySidebarState();
    this.saveSidebarState();
  }

  // 修复初始化方法
  initSidebarState() {
    console.log('🔧 重新初始化侧边栏状态...');

    // 重新获取DOM元素
    this.sidebar = document.querySelector('.sidebar');
    this.sidebarToggle = document.getElementById('sidebar-toggle');

    if (!this.sidebar || !this.sidebarToggle) {
      console.error('❌ 关键DOM元素不存在，无法初始化侧边栏');
      return;
    }

    // 重新绑定事件
    this.bindEvents();

    // 重新加载状态
    this.loadSidebarState();

    // 应用状态
    this.applySidebarState();

    console.log(`✅ 侧边栏重新初始化完成: ${this.isCollapsed ? '收缩' : '展开'}`);
  }

  // 添加强制重置方法
  resetSidebarState() {
    console.log('🔄 重置侧边栏状态...');
    localStorage.removeItem('sm_sidebar_collapsed');

    // 重置状态
    this.isCollapsed = window.innerWidth <= 768;
    this.activeNavItem = 'dashboard';

    // 重新初始化
    this.initSidebarState();

    console.log('✅ 侧边栏状态已重置');
  }
}

// 用户菜单控制器
// 此处已移除 UserMenuController，避免与 user-auth.js 冲突

// 简单的导航测试函数
function testNavigation() {
  console.log('🧪 开始导航测试...');

  const navItems = document.querySelectorAll('.nav-item[data-tab]');
  const contentSections = document.querySelectorAll('.content-section');

  console.log(`📋 找到 ${navItems.length} 个导航项`);
  console.log(`📄 找到 ${contentSections.length} 个内容区域`);

  navItems.forEach((item, index) => {
    const target = item.dataset.tab;
    const section = document.getElementById(target);
    console.log(`${index + 1}. 导航项: ${target} -> 内容区域: ${section ? '✅' : '❌'}`);
  });

  // 手动测试导航
  window.manualNavigate = function (target) {
    console.log(`🔄 手动导航到: ${target}`);

    // 隐藏所有内容区域
    contentSections.forEach(section => {
      section.classList.remove('active');
    });

    // 显示目标区域
    const targetSection = document.getElementById(target);
    if (targetSection) {
      targetSection.classList.add('active');
      console.log(`✅ 成功显示: ${target}`);
    } else {
      console.error(`❌ 未找到区域: ${target}`);
    }

    // 更新导航项状态
    navItems.forEach(item => {
      item.classList.remove('active');
    });

    const activeItem = document.querySelector(`[data-tab="${target}"]`);
    if (activeItem) {
      activeItem.classList.add('active');
      console.log(`✅ 激活导航项: ${target}`);
    }
  };

}

// 初始化侧边栏控制器 - 修复初始化逻辑
document.addEventListener('DOMContentLoaded', () => {
  console.log('🌐 DOM加载完成，开始初始化侧边栏...');

  // 检查是否需要重置状态
  if (window.location.search.includes('reset=true')) {
    localStorage.removeItem('sm_sidebar_collapsed');
    localStorage.removeItem('sm_active_page');
    localStorage.removeItem('sm_was_desktop');
    console.log('🔄 已清除侧边栏本地存储');
  }

  // 确保DOM完全加载后再初始化
  const initializeSidebar = () => {
    // 检查关键元素是否存在
    const sidebar = document.querySelector('.sidebar');
    const sidebarToggle = document.getElementById('sidebar-toggle');

    if (!sidebar || !sidebarToggle) {
      console.warn('⚠️ 关键元素未找到，延迟初始化...');
      setTimeout(initializeSidebar, 100);
      return;
    }

    console.log('✅ 关键元素已找到，开始初始化侧边栏控制器');

    // 初始化控制器
    window.sidebarController = new SidebarController();


  };

  // 立即尝试初始化，如果失败则延迟重试
  initializeSidebar();
});

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    SidebarController
  };
}
