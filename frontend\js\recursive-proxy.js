/**
 * 递归代理管理模块
 */

class RecursiveProxyManager {
    constructor() {
        this.isRunning = false;
        this.websocket = null;
        this.stats = {
            activeSessions: 0,
            discoveredDomains: 0,
            successRate: 0,
            avgResponseTime: 0
        };
        this.discoveries = [];
        this.maxDiscoveries = 100;

        this.init();
    }

    init() {
        this.bindEvents();
        this.loadInitialData();
        this.setupWebSocket();
    }

    bindEvents() {
        // 启动/停止按钮
        document.getElementById('start-recursive-proxy')?.addEventListener('click', () => {
            this.startRecursiveProxy();
        });

        document.getElementById('stop-recursive-proxy')?.addEventListener('click', () => {
            this.stopRecursiveProxy();
        });

        // 刷新按钮
        document.getElementById('refresh-recursive')?.addEventListener('click', () => {
            this.refreshData();
        });

        // 清空发现列表
        document.getElementById('clear-discoveries')?.addEventListener('click', () => {
            this.clearDiscoveries();
        });

        // 导出发现列表
        document.getElementById('export-discoveries')?.addEventListener('click', () => {
            this.exportDiscoveries();
        });

        // 分析标签切换
        document.querySelectorAll('[data-analysis]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchAnalysisTab(e.target.dataset.analysis);
            });
        });

        // ML标签切换
        document.querySelectorAll('[data-ml-tab]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchMLTab(e.target.dataset.mlTab);
            });
        });

        // 安全标签切换
        document.querySelectorAll('[data-security-tab]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchSecurityTab(e.target.dataset.securityTab);
            });
        });
    }

    async loadInitialData() {
        try {
            // 加载递归代理状态
            await this.loadRecursiveStatus();

            // 加载ML分析数据
            await this.loadMLAnalysis();

            // 加载安全对抗状态
            await this.loadSecurityStatus();

        } catch (error) {
            console.error('加载初始数据失败:', error);
            showNotification('加载数据失败', 'error');
        }
    }

    async loadRecursiveStatus() {
        try {
            const response = await fetch('/api/recursive-proxy/status', {
                headers: {
                    'Authorization': `Bearer ${getAuthToken()}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.updateStats(data);
                this.updateStatus(data.enabled);
            }
        } catch (error) {
            console.error('加载递归状态失败:', error);
        }
    }

    async loadMLAnalysis() {
        try {
            const response = await fetch('/api/ml/url-patterns', {
                headers: {
                    'Authorization': `Bearer ${getAuthToken()}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.updateMLStats(data);
                this.updatePatternList(data.patterns);
            }
        } catch (error) {
            console.error('加载ML分析失败:', error);
        }
    }

    async loadSecurityStatus() {
        try {
            const response = await fetch('/api/security/status', {
                headers: {
                    'Authorization': `Bearer ${getAuthToken()}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.updateSecurityStats(data);
            }
        } catch (error) {
            console.error('加载安全状态失败:', error);
        }
    }

    async startRecursiveProxy() {
        try {
            const response = await fetch('/api/recursive-proxy/start', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${getAuthToken()}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.updateStatus(true);
                    showNotification('递归代理已启动', 'success');
                    this.connectWebSocket();
                } else {
                    throw new Error(data.error || '启动失败');
                }
            } else {
                throw new Error('启动失败');
            }
        } catch (error) {
            console.error('启动递归代理失败:', error);
            showNotification('启动递归代理失败', 'error');
        }
    }

    async stopRecursiveProxy() {
        try {
            const response = await fetch('/api/recursive-proxy/stop', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${getAuthToken()}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.updateStatus(false);
                    showNotification('递归代理已停止', 'info');
                    this.disconnectWebSocket();
                } else {
                    throw new Error(data.error || '停止失败');
                }
            } else {
                throw new Error('停止失败');
            }
        } catch (error) {
            console.error('停止递归代理失败:', error);
            showNotification('停止递归代理失败', 'error');
        }
    }

    updateStatus(isRunning) {
        this.isRunning = isRunning;

        const statusText = document.getElementById('recursive-status-text');
        const statusDot = document.getElementById('recursive-status-dot');
        const startBtn = document.getElementById('start-recursive-proxy');
        const stopBtn = document.getElementById('stop-recursive-proxy');

        if (statusText) {
            statusText.textContent = isRunning ? '运行中' : '未启动';
        }

        if (statusDot) {
            statusDot.className = `status-indicator ${isRunning ? 'healthy' : 'offline'}`;
        }

        if (startBtn) {
            startBtn.disabled = isRunning;
        }

        if (stopBtn) {
            stopBtn.disabled = !isRunning;
        }
    }

    updateStats(data) {
        this.stats = { ...this.stats, ...data };

        // 更新状态卡片
        const elements = {
            'active-sessions': data.active_sessions || 0,
            'discovered-domains': data.total_discovered_domains || 0,
            'success-rate': `${Math.round((data.success_rate || 0) * 100)}%`,
            'avg-response-time': `${Math.round(data.avg_response_time_ms || 0)}ms`
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });

        // 更新导航徽章
        const badge = document.getElementById('recursive-badge');
        if (badge) {
            badge.textContent = data.active_sessions || 0;
        }
    }

    addDiscovery(discovery) {
        this.discoveries.unshift(discovery);

        // 限制列表长度
        if (this.discoveries.length > this.maxDiscoveries) {
            this.discoveries = this.discoveries.slice(0, this.maxDiscoveries);
        }

        this.updateDiscoveryFeed();
    }

    updateDiscoveryFeed() {
        const feed = document.getElementById('discovery-feed');
        if (!feed) return;

        if (this.discoveries.length === 0) {
            feed.innerHTML = `
                <div class="discovery-item placeholder">
                    <div class="discovery-time">等待发现...</div>
                    <div class="discovery-domain">递归代理未启动</div>
                    <div class="discovery-source">-</div>
                </div>
            `;
            return;
        }

        feed.innerHTML = this.discoveries.map(discovery => `
            <div class="discovery-item">
                <div class="discovery-time">${this.formatTime(discovery.timestamp)}</div>
                <div class="discovery-domain">${discovery.domain}</div>
                <div class="discovery-source">${discovery.source_url}</div>
            </div>
        `).join('');
    }

    clearDiscoveries() {
        this.discoveries = [];
        this.updateDiscoveryFeed();
        showNotification('发现列表已清空', 'info');
    }

    exportDiscoveries() {
        if (this.discoveries.length === 0) {
            showNotification('没有可导出的数据', 'warning');
            return;
        }

        const data = this.discoveries.map(d => ({
            时间: new Date(d.timestamp).toLocaleString(),
            域名: d.domain,
            来源: d.source_url
        }));

        const csv = this.arrayToCSV(data);
        this.downloadCSV(csv, 'recursive-discoveries.csv');
        showNotification('发现列表已导出', 'success');
    }

    switchAnalysisTab(tab) {
        // 切换按钮状态
        document.querySelectorAll('[data-analysis]').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-analysis="${tab}"]`).classList.add('active');

        // 切换内容
        document.querySelectorAll('.analysis-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tab}-analysis`).classList.add('active');
    }

    switchMLTab(tab) {
        // 切换按钮状态
        document.querySelectorAll('[data-ml-tab]').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-ml-tab="${tab}"]`).classList.add('active');

        // 切换内容
        document.querySelectorAll('.ml-tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tab).classList.add('active');
    }

    switchSecurityTab(tab) {
        // 切换按钮状态
        document.querySelectorAll('[data-security-tab]').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-security-tab="${tab}"]`).classList.add('active');

        // 切换内容
        document.querySelectorAll('.security-tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tab).classList.add('active');
    }

    setupWebSocket() {
        if (this.isRunning) {
            this.connectWebSocket();
        }
    }

    connectWebSocket() {
        if (this.websocket) {
            this.websocket.close();
        }

        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/api/realtime/ws`;

        this.websocket = new WebSocket(wsUrl);

        this.websocket.onopen = () => {
            console.log('WebSocket连接已建立');
        };

        this.websocket.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleWebSocketMessage(data);
            } catch (error) {
                console.error('解析WebSocket消息失败:', error);
            }
        };

        this.websocket.onclose = () => {
            console.log('WebSocket连接已关闭');
            // 如果递归代理仍在运行，尝试重连
            if (this.isRunning) {
                setTimeout(() => this.connectWebSocket(), 5000);
            }
        };

        this.websocket.onerror = (error) => {
            console.error('WebSocket错误:', error);
        };
    }

    disconnectWebSocket() {
        if (this.websocket) {
            this.websocket.close();
            this.websocket = null;
        }
    }

    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'stats_update':
                this.updateStats(data.data);
                break;
            case 'domain_discovered':
                this.addDiscovery(data.data);
                break;
            case 'session_start':
                this.handleSessionStart(data.data);
                break;
            case 'session_complete':
                this.handleSessionComplete(data.data);
                break;
            case 'threat_detected':
                this.handleThreatDetected(data.data);
                break;
        }
    }

    handleSessionStart(data) {
        showNotification(`新会话开始: ${data.original_url}`, 'info');
    }

    handleSessionComplete(data) {
        showNotification(`会话完成，发现 ${data.discovered_domains} 个域名`, 'success');
    }

    handleThreatDetected(data) {
        showNotification(`检测到威胁: ${data.threat_type}`, 'warning');
    }

    // 工具方法
    formatTime(timestamp) {
        return new Date(timestamp).toLocaleTimeString();
    }

    arrayToCSV(data) {
        if (data.length === 0) return '';

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => `"${row[header]}"`).join(','))
        ].join('\n');

        return csvContent;
    }

    downloadCSV(csv, filename) {
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    async refreshData() {
        await this.loadInitialData();
        showNotification('数据已刷新', 'success');
    }

    updateMLStats(data) {
        const elements = {
            'total-patterns': data.total || 0,
            'high-confidence-patterns': data.high_confidence || 0,
            'pattern-accuracy': `${Math.round((data.accuracy || 0) * 100)}%`,
            'learning-progress': `${Math.round((data.progress || 0) * 100)}%`
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }

    updatePatternList(patterns) {
        const list = document.getElementById('pattern-list');
        if (!list || !patterns) return;

        if (patterns.length === 0) {
            list.innerHTML = '<div class="pattern-item">暂无学习模式</div>';
            return;
        }

        list.innerHTML = patterns.map(pattern => `
            <div class="pattern-item">
                <div class="pattern-type">${this.getPatternTypeName(pattern.pattern_type)}</div>
                <div class="pattern-regex">${pattern.regex_pattern}</div>
                <div class="pattern-confidence">${Math.round(pattern.confidence * 100)}%</div>
                <div class="pattern-action ${pattern.should_recurse ? 'recurse' : 'skip'}">
                    ${pattern.should_recurse ? '递归' : '跳过'}
                </div>
            </div>
        `).join('');
    }

    updateSecurityStats(data) {
        const securityItems = {
            'waf-bypass-status': data.waf_bypass?.enabled ? '启用' : '禁用',
            'waf-bypass-rate': `${Math.round((data.waf_bypass?.success_rate || 0) * 100)}%`,
            'captcha-status': data.captcha_solver?.enabled ? '启用' : '禁用',
            'captcha-rate': `${Math.round((data.captcha_solver?.success_rate || 0) * 100)}%`,
            'antibot-status': data.anti_bot?.enabled ? '启用' : '禁用',
            'antibot-rate': `${Math.round((data.anti_bot?.success_rate || 0) * 100)}%`,
            'behavior-status': data.behavior_simulation?.enabled ? '启用' : '禁用',
            'behavior-rate': `${Math.round((data.behavior_simulation?.success_rate || 0) * 100)}%`
        };

        Object.entries(securityItems).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
                if (id.includes('status')) {
                    element.className = `security-status ${value === '启用' ? 'enabled' : 'disabled'}`;
                }
            }
        });
    }

    getPatternTypeName(type) {
        const names = {
            'StaticResource': '静态资源',
            'ApiEndpoint': 'API接口',
            'PageNavigation': '页面导航',
            'Pagination': '分页',
            'SearchResult': '搜索结果',
            'UserContent': '用户内容',
            'AdminPanel': '管理后台',
            'Authentication': '登录认证'
        };
        return names[type] || type;
    }
}

// 初始化递归代理管理器
let recursiveProxyManager;

document.addEventListener('DOMContentLoaded', () => {
    recursiveProxyManager = new RecursiveProxyManager();
});

// 导出给其他模块使用
window.RecursiveProxyManager = RecursiveProxyManager;
