# 📦 SM智能代理系统 - 分发指南

## 🎯 **必需文件清单**

### **方案1: 最小分发包 (推荐)**
```
📁 sm-minimal/
├── sm                    # 二进制文件 (~20MB)
├── config/
│   └── config.yaml      # 配置文件
├── frontend/            # 前端文件目录
│   ├── index.html
│   ├── css/
│   ├── js/
│   └── assets/
└── quick-deploy.sh      # 快速部署脚本
```

### **方案2: 完整分发包**
```
📁 sm-complete/
├── sm                    # 二进制文件
├── config/
│   └── config.yaml      # 配置文件
├── frontend/            # 前端文件
├── install.sh           # 万能安装脚本
├── setup.sh             # 主部署脚本
├── optimize-server.sh   # 性能优化脚本
├── fix-env.sh          # 环境修复脚本
├── README.md           # 使用说明
└── RELEASE_NOTES.md    # 发布说明
```

## 🚀 **创建分发包**

### **自动创建 (推荐)**
```bash
# 编译项目
cargo build --release

# 创建发布包
chmod +x create-release.sh
./create-release.sh

# 生成的文件
releases/sm-release-YYYYMMDD_HHMMSS.tar.gz      # 压缩包
releases/sm-release-YYYYMMDD_HHMMSS.tar.gz.sha256  # 校验和
```

### **手动创建**
```bash
# 1. 创建目录
mkdir -p sm-release

# 2. 复制核心文件
cp target/release/sm sm-release/
cp -r config sm-release/
cp -r frontend sm-release/

# 3. 复制部署脚本
cp install.sh setup.sh sm-release/
chmod +x sm-release/*.sh

# 4. 创建压缩包
tar -czf sm-release.tar.gz sm-release/
```

## 📋 **接收方部署指南**

### **快速部署 (一键安装)**
```bash
# 1. 解压文件
tar -xzf sm-release-*.tar.gz
cd sm-release-*

# 2. 运行快速部署
sudo ./quick-deploy.sh

# 3. 访问系统
# http://your-server:1319
# 账户: admin / admin888
```

### **手动部署**
```bash
# 1. 解压文件
tar -xzf sm-release-*.tar.gz
cd sm-release-*

# 2. 运行万能命令
sudo ./install.sh

# 3. 或者手动部署
sudo mkdir -p /opt/sm
sudo cp sm /opt/sm/
sudo cp -r config /opt/sm/
sudo cp -r frontend /opt/sm/
sudo chmod +x /opt/sm/sm
```

## 🎯 **不同场景的分发策略**

### **场景1: 内网部署**
**分发内容**: 完整包 + 离线依赖
```bash
# 包含所有文件 + 系统依赖包
sm-complete.tar.gz
offline-deps.tar.gz    # 系统依赖包
```

### **场景2: 云服务器部署**
**分发内容**: 最小包
```bash
# 只需核心文件，依赖在线安装
sm-minimal.tar.gz
```

### **场景3: 容器化部署**
**分发内容**: 二进制 + 配置
```bash
# Docker镜像或二进制文件
sm                     # 二进制文件
config.yaml           # 配置文件
Dockerfile            # 容器配置
```

### **场景4: 开发环境**
**分发内容**: 源码 + 编译脚本
```bash
# 完整源码包
src/                   # 源代码
Cargo.toml            # 依赖配置
build.sh              # 编译脚本
```

## 📊 **文件大小参考**

| 文件/目录 | 大小 | 说明 |
|-----------|------|------|
| `sm` | ~20MB | 主程序二进制 |
| `config/` | ~50KB | 配置文件 |
| `frontend/` | ~2MB | Web界面文件 |
| `*.sh` | ~100KB | 部署脚本 |
| **总计** | ~22MB | 完整分发包 |
| **压缩后** | ~8MB | tar.gz压缩包 |

## 🔐 **安全建议**

### **文件完整性验证**
```bash
# 生成校验和
sha256sum sm-release.tar.gz > sm-release.tar.gz.sha256

# 验证完整性
sha256sum -c sm-release.tar.gz.sha256
```

### **权限设置**
```bash
# 二进制文件
chmod +x sm

# 配置文件
chmod 644 config/config.yaml

# 部署脚本
chmod +x *.sh
```

## 🌐 **网络传输**

### **小文件传输**
```bash
# SCP传输
scp sm-release.tar.gz user@server:/tmp/

# 或使用rsync
rsync -avz sm-release.tar.gz user@server:/tmp/
```

### **大文件传输**
```bash
# 分片传输
split -b 10M sm-release.tar.gz sm-release.part.
# 接收方合并: cat sm-release.part.* > sm-release.tar.gz
```

## 📞 **技术支持**

### **常见问题**
1. **权限问题**: 确保以root权限运行部署脚本
2. **端口冲突**: 检查1319和1911端口是否被占用
3. **依赖缺失**: 运行install.sh自动安装依赖

### **故障排除**
```bash
# 检查服务状态
sudo systemctl status sm

# 查看日志
sudo journalctl -u sm -f

# 重新部署
sudo ./fix-env.sh
```

---

💡 **建议**: 使用自动创建脚本 `./create-release.sh` 来生成标准化的分发包，确保包含所有必需文件。
