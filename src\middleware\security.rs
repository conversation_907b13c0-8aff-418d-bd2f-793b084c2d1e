use super::{Middleware, Next};
use crate::security::{get_validator, SecureLogger, SecuritySeverity};
// 导入统一的输入验证模块
use crate::security::input_validation::{UnifiedValidator, ValidationConfig};
use async_trait::async_trait;
use axum::{
    extract::{Request, State},
    http::{HeaderMap, HeaderName, HeaderValue, StatusCode},
    middleware::Next,
    response::Response,
};
use chrono::{Duration, Utc};
use hyper::{Body, Request, Response, StatusCode};
use once_cell::sync::Lazy;
use parking_lot::RwLock;
use regex::Regex;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use thiserror::Error;
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// 安全检测模式 - 合并重复的正则表达式
static SECURITY_PATTERNS: Lazy<SecurityPatterns> = Lazy::new(|| SecurityPatterns::new());

struct SecurityPatterns {
    sql_injection: Vec<Regex>,
    xss: Vec<Regex>,
    // 移除简单的路径遍历正则，使用统一验证器
}

impl SecurityPatterns {
    fn new() -> Self {
        Self {
            sql_injection: vec![
                Regex::new(r"(?i)\b(union\s+select|drop\s+table|delete\s+from|insert\s+into|update\s+set)\b").unwrap(),
                Regex::new(r"(?i)\b(exec\s*\(|sp_|xp_)\b").unwrap(),
                Regex::new(r"(?i)(\'\s*;\s*--|/\*.*\*/)").unwrap(),
            ],
            xss: vec![
                Regex::new(r"(?i)<(script|iframe|object|embed)[^>]*>").unwrap(),
                Regex::new(r"(?i)(javascript:|on\w+\s*=|eval\s*\()").unwrap(),
            ],
        }
    }
}

/// CSRF 令牌管理器
pub struct CsrfTokenManager {
    tokens: Arc<RwLock<HashMap<String, CsrfToken>>>,

    /// 令牌有效期
    token_lifetime: Duration,
}

#[derive(Debug, Clone)]
struct CsrfToken {
    token: String,

    /// 令牌创建时间
    created_at: chrono::DateTime<Utc>,

    /// 关联的用户会话ID
    user_session: Option<String>,
}

impl CsrfTokenManager {
    pub fn new() -> Self {
        Self {
            tokens: Arc::new(RwLock::new(HashMap::new())),
            token_lifetime: Duration::minutes(30), // 30分钟有效期
        }
    }

    /// 生成 CSRF 令牌
    pub async fn generate_token(&self, session_id: Option<String>) -> String {
        let token = Uuid::new_v4().to_string();
        let csrf_token = CsrfToken {
            token: token.clone(),
            created_at: Utc::now(),
            user_session: session_id,
        };

        let mut tokens = self.tokens.write().await;
        tokens.insert(token.clone(), csrf_token);

        // 清理过期令牌
        self.cleanup_expired_tokens(&mut tokens).await;

        token
    }

    /// 验证 CSRF 令牌
    pub async fn verify_token(
        &self,
        token: &str,
        session_id: Option<&str>,
    ) -> Result<(), SecurityError> {
        let tokens = self.tokens.read().await;

        if let Some(csrf_token) = tokens.get(token) {
            // 检查令牌是否过期
            let now = Utc::now();
            if now > csrf_token.created_at + self.token_lifetime {
                return Err(SecurityError::ExpiredCsrfToken);
            }

            // 如果有会话ID，验证会话匹配
            if let (Some(token_session), Some(current_session)) =
                (&csrf_token.user_session, session_id)
            {
                if token_session != current_session {
                    return Err(SecurityError::InvalidCsrfToken);
                }
            }

            Ok(())
        } else {
            Err(SecurityError::InvalidCsrfToken)
        }
    }

    /// 使用并删除令牌（一次性使用）
    pub async fn consume_token(
        &self,
        token: &str,
        session_id: Option<&str>,
    ) -> Result<(), SecurityError> {
        self.verify_token(token, session_id).await?;

        let mut tokens = self.tokens.write().await;
        tokens.remove(token);

        Ok(())
    }

    /// 清理过期令牌
    async fn cleanup_expired_tokens(&self, tokens: &mut HashMap<String, CsrfToken>) {
        let now = Utc::now();
        tokens.retain(|_, csrf_token| now <= csrf_token.created_at + self.token_lifetime);
    }
}

/// HTTP 头部安全验证器
pub struct HeaderSecurityValidator;

impl HeaderSecurityValidator {
    /// 验证头部值是否安全
    pub fn validate_header_value(name: &str, value: &str) -> Result<(), SecurityError> {
        // 检查CRLF注入
        if value.contains('\r') || value.contains('\n') {
            return Err(SecurityError::HeaderInjection(format!(
                "头部 {} 包含CRLF字符",
                name
            )));
        }

        // 检查空字节注入
        if value.contains('\0') {
            return Err(SecurityError::HeaderInjection(format!(
                "头部 {} 包含空字节",
                name
            )));
        }

        // 检查特定头部的安全性
        match name.to_lowercase().as_str() {
            "location" => Self::validate_redirect_url(value)?,
            "content-type" => Self::validate_content_type(value)?,
            "x-frame-options" | "content-security-policy" => {
                // 这些安全头部不应该被用户控制
                return Err(SecurityError::HeaderInjection(format!(
                    "不允许设置安全头部: {}",
                    name
                )));
            }
            _ => {}
        }

        Ok(())
    }

    /// 验证重定向URL安全性
    fn validate_redirect_url(url: &str) -> Result<(), SecurityError> {
        // 防止开放重定向攻击
        if url.starts_with("http://") || url.starts_with("https://") {
            // 检查是否为外部URL
            if !Self::is_safe_redirect_domain(url) {
                return Err(SecurityError::UnsafeRedirect(url.to_string()));
            }
        }

        // 检查协议混乱攻击
        let dangerous_schemes = ["javascript:", "data:", "vbscript:", "file:", "ftp:"];
        for scheme in &dangerous_schemes {
            if url.to_lowercase().starts_with(scheme) {
                return Err(SecurityError::UnsafeRedirect(format!(
                    "危险的协议: {}",
                    scheme
                )));
            }
        }

        Ok(())
    }

    /// 检查是否为安全的重定向域名
    fn is_safe_redirect_domain(url: &str) -> bool {
        // 简化实现 - 实际应用中应该维护一个允许的域名白名单
        let allowed_domains = ["localhost", "127.0.0.1", "::1"];

        if let Ok(parsed_url) = url::Url::parse(url) {
            if let Some(host) = parsed_url.host_str() {
                return allowed_domains.contains(&host);
            }
        }

        false
    }

    /// 验证内容类型
    fn validate_content_type(content_type: &str) -> Result<(), SecurityError> {
        // 防止内容类型嗅探攻击
        let safe_content_types = [
            "text/html",
            "text/plain",
            "application/json",
            "application/xml",
            "text/css",
            "application/javascript",
            "image/",
        ];

        let ct_lower = content_type.to_lowercase();
        let is_safe = safe_content_types
            .iter()
            .any(|&safe_type| ct_lower.starts_with(safe_type));

        if !is_safe {
            return Err(SecurityError::ContentTypeSniffing);
        }

        Ok(())
    }
}

/// 安全中间件配置
#[derive(Debug, Clone)]
pub struct SecurityMiddlewareConfig {
    pub enable_csrf_protection: bool,
    pub enable_clickjack_protection: bool,
    pub enable_content_type_options: bool,
    pub enable_xss_protection: bool,
    pub enable_hsts: bool,
    pub hsts_max_age: u32,
    pub enable_referrer_policy: bool,
    pub csp_policy: Option<String>,
}

impl Default for SecurityMiddlewareConfig {
    fn default() -> Self {
        Self {
            enable_csrf_protection: true,
            enable_clickjack_protection: true,
            enable_content_type_options: true,
            enable_xss_protection: true,
            enable_hsts: true,
            hsts_max_age: 31536000, // 1年
            enable_referrer_policy: true,
            csp_policy: Some("default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';".to_string()),
        }
    }
}

/// 安全中间件层 - 统一安全防护
pub struct SecurityMiddleware {
    config: SecurityConfig,
    rate_limiter: Arc<RwLock<HashMap<String, RateLimitInfo>>>,

    /// 被阻止的IP地址
    blocked_ips: Arc<RwLock<HashSet<String>>>,

    /// 安全事件记录
    security_events: Arc<RwLock<Vec<SecurityEvent>>>,
}

#[derive(Debug, Clone)]
pub struct SecurityConfig {
    /// 最大请求体积
    pub max_request_size: usize,

    /// 最大头部大小
    pub max_header_size: usize,

    /// 是否启用CSRF保护
    pub enable_csrf_protection: bool,

    /// 是否启用XSS保护
    pub enable_xss_protection: bool,

    /// 是否启用SQL注入检测
    pub enable_sql_injection_detection: bool,

    /// 每个IP的速率限制
    pub rate_limit_per_ip: u32,

    /// 速率限制窗口
    pub rate_limit_window: Duration,

    /// 自动阻止阈值
    pub auto_block_threshold: u32,

    /// 阻止持续时间
    pub block_duration: Duration,
}

#[derive(Debug, Clone)]
pub struct RateLimitInfo {
    /// 当前请求计数
    pub count: u32,

    /// 窗口开始时间
    pub window_start: SystemTime,

    /// 阻止到期时间
    pub blocked_until: Option<SystemTime>,
}

#[derive(Debug, Clone)]
pub struct SecurityEvent {
    /// 事件类型
    pub event_type: SecurityEventType,

    /// IP地址
    pub ip_address: String,

    /// 用户代理
    pub user_agent: Option<String>,

    /// 请求路径
    pub request_path: String,

    /// 时间戳
    pub timestamp: SystemTime,

    /// 事件详情
    pub details: String,
}

#[derive(Debug, Clone)]
pub enum SecurityEventType {
    RateLimitExceeded,
    SqlInjectionAttempt,
    XssAttempt,
    CsrfTokenMissing,
    InvalidInput,
    SuspiciousRequest,
    BruteForceAttempt,
}

impl SecurityMiddleware {
    pub fn new(config: SecurityConfig) -> Self {
        Self {
            config,
            rate_limiter: Arc::new(RwLock::new(HashMap::new())),
            blocked_ips: Arc::new(RwLock::new(HashSet::new())),
            security_events: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// 主要安全检查中间件 - 完全禁用版本
    pub async fn security_check(
        &self,
        request: Request<Body>,
        remote_addr: Option<SocketAddr>,
    ) -> Result<Request<Body>, SecurityError> {
        // 🚨 安全中间件已完全禁用 - 开发模式
        warn!("⚠️ 安全中间件已完全禁用 - 仅用于开发和调试");

        let client_ip = remote_addr
            .map(|addr| addr.ip().to_string())
            .unwrap_or_else(|| "unknown".to_string());

        // 仅记录请求信息，不进行任何安全检查
        tracing::debug!(
            "Security check bypassed for IP: {}, path: {}",
            client_ip,
            request.uri().path()
        );

        // 临时注释掉的严格安全检查
        /*
        // 1. IP黑名单检查
        if self.is_ip_blocked(&client_ip).await {
            self.record_security_event(SecurityEvent {
                event_type: SecurityEventType::SuspiciousRequest,
                ip_address: client_ip.clone(),
                user_agent: None,
                request_path: request.uri().path().to_string(),
                timestamp: start_time,
                details: "Blocked IP attempted access".to_string(),
            })
            .await;
            return Err(SecurityError::IpBlocked("IP地址已被阻止".to_string()));
        }

        // 2. 速率限制检查
        if !self.check_rate_limit(&client_ip).await? {
            self.record_security_event(SecurityEvent {
                event_type: SecurityEventType::RateLimitExceeded,
                ip_address: client_ip.clone(),
                user_agent: None,
                request_path: request.uri().path().to_string(),
                timestamp: start_time,
                details: "Rate limit exceeded".to_string(),
            })
            .await;
            return Err(SecurityError::RateLimitExceeded("请求频率超限".to_string()));
        }

        // 4. 头部安全检查
        self.validate_headers(request.headers())?;

        // 5. URL路径安全检查
        self.validate_request_path(request.uri().path())?;

        // 6. User-Agent检查
        let user_agent = request
            .headers()
            .get("user-agent")
            .and_then(|ua| ua.to_str().ok())
            .map(|ua| ua.to_string());

        if let Some(ua) = &user_agent {
            self.validate_user_agent(ua)?;
        }
        */

        tracing::debug!(
            "Security check passed (DEBUG MODE) for IP: {}, path: {}, duration: {:?}",
            client_ip,
            request.uri().path(),
            start_time.elapsed().unwrap_or_default()
        );

        Ok(request)
    }

    /// 检查IP是否被阻止
    async fn is_ip_blocked(&self, ip: &str) -> bool {
        let blocked_ips = self.blocked_ips.read().await;
        blocked_ips.contains(ip)
    }

    /// 速率限制检查
    async fn check_rate_limit(&self, ip: &str) -> Result<bool, SecurityError> {
        let mut rate_limiter = self.rate_limiter.write().await;
        let now = SystemTime::now();

        let rate_info = rate_limiter.entry(ip.to_string()).or_insert(RateLimitInfo {
            count: 0,
            window_start: now,
            blocked_until: None,
        });

        // 检查是否仍在阻止期间
        if let Some(blocked_until) = rate_info.blocked_until {
            if now < blocked_until {
                return Ok(false);
            } else {
                // 阻止期间结束，重置
                rate_info.blocked_until = None;
                rate_info.count = 0;
                rate_info.window_start = now;
            }
        }

        // 检查是否需要重置窗口
        if now
            .duration_since(rate_info.window_start)
            .unwrap_or_default()
            > self.config.rate_limit_window
        {
            rate_info.count = 0;
            rate_info.window_start = now;
        }

        rate_info.count += 1;

        // 检查是否超过限制
        if rate_info.count > self.config.rate_limit_per_ip {
            // 如果超过阻止阈值，加入黑名单
            if rate_info.count > self.config.auto_block_threshold {
                let mut blocked_ips = self.blocked_ips.write().await;
                blocked_ips.insert(ip.to_string());
                tracing::warn!("IP {} has been blocked due to excessive requests", ip);
            }

            rate_info.blocked_until = Some(now + self.config.block_duration);
            return Ok(false);
        }

        Ok(true)
    }

    /// 验证HTTP头部
    fn validate_headers(&self, headers: &HeaderMap) -> Result<(), SecurityError> {
        for (name, value) in headers.iter() {
            let header_name = name.as_str();
            let header_value = value
                .to_str()
                .map_err(|_| SecurityError::HeaderInjection("头部包含非UTF-8字符".to_string()))?;

            // 检查头部大小
            if header_value.len() > self.config.max_header_size {
                return Err(SecurityError::HeaderInjection("头部值过长".to_string()));
            }

            // 检查CRLF注入
            if header_value.contains('\r') || header_value.contains('\n') {
                return Err(SecurityError::HeaderInjection(format!(
                    "头部 {} 包含CRLF字符",
                    header_name
                )));
            }

            // 检查空字节注入
            if header_value.contains('\0') {
                return Err(SecurityError::HeaderInjection(format!(
                    "头部 {} 包含空字节",
                    header_name
                )));
            }

            // 检查特定头部的安全性
            match header_name.to_lowercase().as_str() {
                "host" => self.validate_host_header(header_value)?,
                "referer" => self.validate_referer_header(header_value)?,
                "origin" => self.validate_origin_header(header_value)?,
                "x-forwarded-for" | "x-real-ip" => self.validate_forwarded_header(header_value)?,
                _ => {}
            }
        }

        Ok(())
    }

    /// 验证Host头部
    fn validate_host_header(&self, host: &str) -> Result<(), SecurityError> {
        // 检查Host头部注入
        if host.contains('@') || host.contains('/') || host.contains('\\') {
            return Err(SecurityError::HeaderInjection(
                "Host头部包含危险字符".to_string(),
            ));
        }

        // 验证主机名格式
        if host.len() > 253 {
            return Err(SecurityError::HeaderInjection("Host头部过长".to_string()));
        }

        Ok(())
    }

    /// 验证Referer头部
    fn validate_referer_header(&self, referer: &str) -> Result<(), SecurityError> {
        if referer.len() > 2048 {
            return Err(SecurityError::HeaderInjection(
                "Referer头部过长".to_string(),
            ));
        }

        // 检查危险协议
        let lower_referer = referer.to_lowercase();
        const DANGEROUS_PROTOCOLS: &[&str] = &["javascript:", "data:", "vbscript:"];

        for protocol in DANGEROUS_PROTOCOLS {
            if lower_referer.starts_with(protocol) {
                return Err(SecurityError::HeaderInjection(
                    "Referer包含危险协议".to_string(),
                ));
            }
        }

        Ok(())
    }

    /// 验证Origin头部
    fn validate_origin_header(&self, origin: &str) -> Result<(), SecurityError> {
        if origin != "null" && !origin.starts_with("http://") && !origin.starts_with("https://") {
            return Err(SecurityError::HeaderInjection(
                "Origin头部格式无效".to_string(),
            ));
        }
        Ok(())
    }

    /// 验证转发头部
    fn validate_forwarded_header(&self, forwarded: &str) -> Result<(), SecurityError> {
        // 基本IP地址格式检查
        for ip in forwarded.split(',') {
            let ip = ip.trim();
            if !ip.is_empty() && ip.parse::<std::net::IpAddr>().is_err() {
                return Err(SecurityError::HeaderInjection(
                    "转发头部包含无效IP".to_string(),
                ));
            }
        }
        Ok(())
    }

    /// 验证请求路径安全性 - 使用统一的输入验证器
    fn validate_request_path(&self, path: &str) -> Result<(), SecurityError> {
        // 使用统一验证器进行完整的路径安全检查
        let validator = UnifiedValidator::new(ValidationConfig {
            strict_mode: true,
            max_path_length: 500,
            ..Default::default()
        });

        // 完整的路径验证，替代原有的简单字符串检查
        if let Err(_) = validator.validate_path(path, "请求路径") {
            return Err(SecurityError::PathTraversal(format!(
                "路径安全验证失败: {}",
                path
            )));
        }

        // 防止部署脚本被访问
        let forbidden_files = [
            "setup.sh",
            "deploy.sh",
            "install.sh",
            "build.sh",
            "start.sh",
            "stop.sh",
            "restart.sh",
            "update.sh",
            "backup.sh",
            "migrate.sh",
            "Cargo.toml",
            "Cargo.lock",
            ".env",
            ".env.local",
            ".env.production",
            "config.yaml",
            "security.conf",
            "sm.service",
            "docker-compose.yml",
            "Dockerfile",
        ];

        let dangerous_extensions = [
            ".sh", ".py", ".pl", ".rb", ".php", ".asp", ".aspx", ".jsp", ".bat", ".cmd", ".ps1",
            ".toml", ".yaml", ".yml", ".conf", ".ini", ".env", ".key", ".pem", ".crt",
        ];

        let dangerous_directories = [
            "/src/",
            "/crates/",
            "/target/",
            "/logs/",
            "/.git/",
            "/config/",
            "/documentation/",
            "/scripts/",
            "/deploy/",
            "/backup/",
            "/tmp/",
            "/temp/",
            "/cache/",
        ];

        // 检查是否访问禁止的文件
        let path_lower = path.to_lowercase();
        for forbidden in &forbidden_files {
            if path_lower.contains(&forbidden.to_lowercase()) {
                warn!("尝试访问禁止的系统文件: {} from path: {}", forbidden, path);
                return Err(SecurityError::ForbiddenFile(format!(
                    "禁止访问系统文件: {}",
                    forbidden
                )));
            }
        }

        // 检查危险的文件扩展名
        for ext in &dangerous_extensions {
            if path_lower.ends_with(ext) {
                warn!("尝试访问危险文件类型: {} from path: {}", ext, path);
                return Err(SecurityError::ForbiddenFile(format!(
                    "禁止访问此类型文件: {}",
                    ext
                )));
            }
        }

        // 检查危险的目录
        for dir in &dangerous_directories {
            if path_lower.contains(dir) {
                warn!("尝试访问受保护目录: {} from path: {}", dir, path);
                return Err(SecurityError::ForbiddenDirectory(format!(
                    "禁止访问目录: {}",
                    dir
                )));
            }
        }

        // 只允许访问 frontend 目录下的特定文件
        if !path.starts_with("/frontend/") && !path.starts_with("/api/") && path != "/" {
            return Err(SecurityError::ForbiddenDirectory(
                "只允许访问前端资源和API".to_string(),
            ));
        }

        Ok(())
    }

    /// 检测路径中的SQL注入
    fn detect_sql_injection_in_path(&self, path: &str) -> Result<(), SecurityError> {
        let lower_path = path.to_lowercase();
        const SQL_PATTERNS: &[&str] = &[
            "union select",
            "drop table",
            "delete from",
            "insert into",
            "update set",
            "exec ",
            "execute ",
            "--",
            "/*",
            "*/",
            "information_schema",
            "sys.",
            "mysql.",
            "pg_catalog",
        ];

        for pattern in SQL_PATTERNS {
            if lower_path.contains(pattern) {
                return Err(SecurityError::SqlInjectionAttempt(format!(
                    "路径包含SQL注入模式: {}",
                    pattern
                )));
            }
        }

        Ok(())
    }

    /// 检测路径中的XSS
    fn detect_xss_in_path(&self, path: &str) -> Result<(), SecurityError> {
        let lower_path = path.to_lowercase();
        const XSS_PATTERNS: &[&str] = &[
            "<script",
            "javascript:",
            "onload=",
            "onerror=",
            "onclick=",
            "onmouseover=",
            "onfocus=",
            "onblur=",
            "eval(",
            "alert(",
            "prompt(",
            "confirm(",
            "document.cookie",
            "window.location",
        ];

        for pattern in XSS_PATTERNS {
            if lower_path.contains(pattern) {
                return Err(SecurityError::XssAttempt(format!(
                    "路径包含XSS模式: {}",
                    pattern
                )));
            }
        }

        Ok(())
    }

    /// 验证User-Agent
    fn validate_user_agent(&self, user_agent: &str) -> Result<(), SecurityError> {
        if user_agent.len() > 512 {
            return Err(SecurityError::InvalidInput("User-Agent过长".to_string()));
        }

        // 检查恶意User-Agent模式
        let lower_ua = user_agent.to_lowercase();
        const MALICIOUS_PATTERNS: &[&str] = &[
            "sqlmap",
            "nikto",
            "nessus",
            "openvas",
            "burp",
            "nmap",
            "dirb",
            "dirbuster",
            "gobuster",
            "ffuf",
            "wfuzz",
            "<script",
            "javascript:",
            "eval(",
            "alert(",
        ];

        for pattern in MALICIOUS_PATTERNS {
            if lower_ua.contains(pattern) {
                return Err(SecurityError::SuspiciousUserAgent(
                    "检测到可疑的User-Agent".to_string(),
                ));
            }
        }

        Ok(())
    }

    /// 记录安全事件
    async fn record_security_event(&self, event: SecurityEvent) {
        let mut events = self.security_events.write().await;

        // 限制事件存储数量，防止内存泄露
        if events.len() >= 1000 {
            events.drain(0..500); // 保留最新的500个事件
        }

        events.push(event.clone());

        // 记录到日志
        tracing::warn!(
            "Security event: {:?} from IP: {} on path: {}",
            event.event_type,
            event.ip_address,
            event.request_path
        );
    }

    /// 获取安全统计信息
    pub async fn get_security_stats(&self) -> SecurityStats {
        let events = self.security_events.read().await;
        let rate_limiter = self.rate_limiter.read().await;
        let blocked_ips = self.blocked_ips.read().await;

        let now = SystemTime::now();
        let one_hour_ago = now - Duration::from_secs(3600);

        let recent_events: Vec<_> = events
            .iter()
            .filter(|event| event.timestamp > one_hour_ago)
            .collect();

        SecurityStats {
            total_events: events.len(),
            recent_events: recent_events.len(),
            blocked_ips: blocked_ips.len(),
            active_rate_limits: rate_limiter.len(),
            event_types: Self::count_event_types(&recent_events),
        }
    }

    /// 统计事件类型
    fn count_event_types(events: &[&SecurityEvent]) -> HashMap<String, usize> {
        let mut counts = HashMap::new();

        for event in events {
            let event_type = format!("{:?}", event.event_type);
            *counts.entry(event_type).or_insert(0) += 1;
        }

        counts
    }

    /// 清理过期数据
    pub async fn cleanup_expired_data(&self) {
        let now = SystemTime::now();
        let cleanup_threshold = now - Duration::from_secs(3600); // 1小时前

        // 清理速率限制记录
        {
            let mut rate_limiter = self.rate_limiter.write().await;
            rate_limiter.retain(|_, info| {
                if let Some(blocked_until) = info.blocked_until {
                    blocked_until > now
                } else {
                    info.window_start > cleanup_threshold
                }
            });
        }

        // 清理旧的安全事件
        {
            let mut events = self.security_events.write().await;
            events.retain(|event| event.timestamp > cleanup_threshold);
        }

        tracing::debug!("Security middleware cleanup completed");
    }
}

#[derive(Debug)]
pub struct SecurityStats {
    pub total_events: usize,
    pub recent_events: usize,
    pub blocked_ips: usize,
    pub active_rate_limits: usize,
    pub event_types: HashMap<String, usize>,
}

impl Default for SecurityConfig {
    fn default() -> Self {
        Self {
            max_request_size: 1024 * 1024, // 1MB
            max_header_size: 8192,         // 8KB
            enable_csrf_protection: true,
            enable_xss_protection: true,
            enable_sql_injection_detection: true,
            rate_limit_per_ip: 60, // 每分钟60请求
            rate_limit_window: Duration::from_secs(60),
            auto_block_threshold: 300, // 5分钟内300请求自动阻止
            block_duration: Duration::from_secs(3600), // 阻止1小时
        }
    }
}
