//! 统一认证模块
//! 提供JWT认证、密码哈希、会话管理等功能

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime};

use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use tracing::{info, warn};

#[cfg(feature = "auth")]
use argon2::password_hash::{rand_core::OsRng, SaltString};
#[cfg(feature = "auth")]
use argon2::{Argon2, PasswordHash, PasswordHasher, PasswordVerifier};
#[cfg(feature = "auth")]
use jsonwebtoken::{decode, encode, Algorithm, Decoding<PERSON>ey, Encoding<PERSON><PERSON>, Header, Validation};

use chrono::{DateTime, Utc};
use tokio::sync::RwLock;
use uuid::Uuid;

// 使用统一的错误系统
use proxy_core::error::{ProxyError, Result};

// 导入用户类型 - 使用本地定义以避免循环依赖
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct User {
    pub id: String,
    pub username: String,
    pub email: String,
    pub roles: Vec<String>,
    pub created_at: DateTime<Utc>,
    pub last_login: Option<DateTime<Utc>>,
    pub is_active: bool,
}

/// JWT Claims 结构
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Claims {
    pub sub: String,             // Subject (user ID)
    pub exp: usize,              // Expiration time (as UTC timestamp)
    pub iat: usize,              // Issued at (as UTC timestamp)
    pub nbf: usize,              // Not before (as UTC timestamp)
    pub jti: String,             // JWT ID
    pub roles: Vec<String>,      // User roles
    pub session_id: String,      // Session ID
    pub ip_addr: Option<String>, // Client IP address
}

/// 认证配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthConfig {
    pub jwt_secret: String,
    pub token_duration: u64,         // 令牌有效期（秒）
    pub session_timeout: u64,        // 会话超时（秒）
    pub max_login_attempts: u32,     // 最大登录尝试次数
    pub lockout_duration: u64,       // 锁定持续时间（秒）
    pub password_min_length: usize,  // 密码最小长度
    pub require_special_chars: bool, // 是否要求特殊字符
}

impl Default for AuthConfig {
    fn default() -> Self {
        Self {
            jwt_secret: "default_secret_please_change_in_production".to_string(),
            token_duration: 3600 * 24, // 24小时
            session_timeout: 3600,     // 1小时
            max_login_attempts: 5,
            lockout_duration: 300, // 5分钟
            password_min_length: 8,
            require_special_chars: true,
        }
    }
}

/// 用户会话信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserSession {
    pub user_id: String,
    pub session_id: String,
    pub created_at: SystemTime,
    pub last_activity: SystemTime,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
}

/// 认证服务特征 - 使用统一的Result类型
#[async_trait]
pub trait AuthService: Send + Sync {
    /// 验证用户凭据
    async fn authenticate_user(&self, username: &str, password: &str) -> Result<User>;

    /// 生成JWT令牌
    async fn generate_jwt(&self, user: &User, session_id: &str) -> Result<String>;

    /// 验证JWT令牌
    async fn verify_jwt(&self, token: &str) -> Result<Claims>;

    /// 创建用户会话
    async fn create_session(&self, user: &User, client_ip: Option<&str>) -> Result<UserSession>;

    /// 验证会话
    async fn verify_session(
        &self,
        session_id: &str,
        client_ip: Option<&str>,
    ) -> Result<UserSession>;

    /// 撤销会话
    async fn revoke_session(&self, session_id: &str) -> Result<()>;

    /// 撤销用户的所有会话
    async fn revoke_all_user_sessions(&self, user_id: &str) -> Result<()>;

    /// 记录登录尝试
    async fn record_login_attempt(
        &self,
        identifier: &str,
        success: bool,
        client_ip: Option<&str>,
    ) -> Result<()>;

    /// 检查用户是否被锁定
    async fn is_user_locked(&self, identifier: &str) -> Result<bool>;

    /// 解锁用户
    async fn unlock_user(&self, identifier: &str) -> Result<()>;

    /// 清理过期会话和令牌
    async fn cleanup_expired(&self) -> Result<u32>;
}

/// 安全认证管理器实现
pub struct SecureAuthManager {
    encoding_key: EncodingKey,
    decoding_key: DecodingKey,
    active_sessions: Arc<RwLock<HashMap<String, UserSession>>>,
    login_attempts: Arc<RwLock<HashMap<String, (u32, SystemTime)>>>,
    locked_users: Arc<RwLock<HashMap<String, SystemTime>>>,
    revoked_tokens: Arc<RwLock<HashMap<String, SystemTime>>>,
    config: AuthConfig,
}

impl SecureAuthManager {
    /// 创建新的认证管理器
    pub fn new(config: AuthConfig) -> Result<Self> {
        if config.jwt_secret.len() < 32 {
            return Err(ProxyError::authentication(
                "JWT密钥长度不足，至少需要32个字符",
            ));
        }

        let encoding_key = EncodingKey::from_secret(config.jwt_secret.as_bytes());
        let decoding_key = DecodingKey::from_secret(config.jwt_secret.as_bytes());

        Ok(Self {
            encoding_key,
            decoding_key,
            active_sessions: Arc::new(RwLock::new(HashMap::new())),
            login_attempts: Arc::new(RwLock::new(HashMap::new())),
            locked_users: Arc::new(RwLock::new(HashMap::new())),
            revoked_tokens: Arc::new(RwLock::new(HashMap::new())),
            config,
        })
    }

    /// 验证密码强度
    #[allow(dead_code)]
    fn validate_password(&self, password: &str) -> Result<()> {
        if password.len() < self.config.password_min_length {
            return Err(ProxyError::invalid_input("密码长度不足"));
        }

        if self.config.require_special_chars {
            let has_upper = password.chars().any(|c| c.is_uppercase());
            let has_lower = password.chars().any(|c| c.is_lowercase());
            let has_digit = password.chars().any(|c| c.is_numeric());
            let has_special = password.chars().any(|c| !c.is_alphanumeric());

            if !(has_upper && has_lower && has_digit && has_special) {
                return Err(ProxyError::invalid_input(
                    "密码必须包含大小写字母、数字和特殊字符",
                ));
            }
        }

        Ok(())
    }

    /// 哈希密码
    #[cfg(feature = "auth")]
    pub fn hash_password(password: &str) -> Result<String> {
        let salt = SaltString::generate(&mut OsRng);
        let argon2 = Argon2::default();

        argon2
            .hash_password(password.as_bytes(), &salt)
            .map_err(|e| ProxyError::internal(format!("密码哈希失败: {}", e)))
            .map(|hash| hash.to_string())
    }

    /// 验证密码
    #[cfg(feature = "auth")]
    pub fn verify_password(password: &str, hash: &str) -> Result<bool> {
        let parsed_hash = PasswordHash::new(hash)
            .map_err(|e| ProxyError::authentication(format!("密码哈希解析失败: {}", e)))?;

        let argon2 = Argon2::default();
        Ok(argon2
            .verify_password(password.as_bytes(), &parsed_hash)
            .is_ok())
    }

    /// 检查登录限制
    async fn check_login_limit(&self, identifier: &str) -> Result<()> {
        let now = SystemTime::now();

        // 检查用户是否被锁定
        {
            let locked = self.locked_users.read().await;
            if let Some(&lock_time) = locked.get(identifier) {
                let unlock_time = lock_time + Duration::from_secs(self.config.lockout_duration);
                if now < unlock_time {
                    return Err(ProxyError::authentication("用户账户已被锁定，请稍后重试"));
                }
            }
        }

        // 清理过期的登录尝试记录
        {
            let mut attempts = self.login_attempts.write().await;
            let cutoff = now - Duration::from_secs(3600); // 1小时前
            attempts.retain(|_, &mut (_, time)| time > cutoff);
        }

        Ok(())
    }

    /// 记录失败的登录尝试
    async fn record_failed_attempt(&self, identifier: &str) -> Result<()> {
        let now = SystemTime::now();
        let mut attempts = self.login_attempts.write().await;

        let (count, last_attempt) = attempts.get(identifier).copied().unwrap_or((0, now));

        if last_attempt + Duration::from_secs(3600) < now {
            // 如果上次尝试超过1小时，重置计数
            attempts.insert(identifier.to_string(), (1, now));
        } else {
            let new_count = count + 1;
            attempts.insert(identifier.to_string(), (new_count, now));

            // 检查是否需要锁定用户
            if new_count >= self.config.max_login_attempts {
                let mut locked = self.locked_users.write().await;
                locked.insert(identifier.to_string(), now);
                warn!("用户 {} 因登录失败次数过多被锁定", identifier);
            }
        }

        Ok(())
    }

    /// 重置登录尝试计数
    async fn reset_login_attempts(&self, identifier: &str) -> Result<()> {
        let mut attempts = self.login_attempts.write().await;
        attempts.remove(identifier);

        // 如果用户被锁定，也解除锁定
        let mut locked = self.locked_users.write().await;
        locked.remove(identifier);

        Ok(())
    }

    /// 清理过期数据
    async fn cleanup_internal(&self) -> Result<()> {
        let now = SystemTime::now();
        let week_ago = now - Duration::from_secs(7 * 24 * 3600);
        let day_ago = now - Duration::from_secs(24 * 3600);

        // 清理过期的撤销令牌
        {
            let mut revoked = self.revoked_tokens.write().await;
            revoked.retain(|_, &mut time| time > week_ago);
        }

        // 清理过期的登录尝试
        {
            let mut attempts = self.login_attempts.write().await;
            attempts.retain(|_, &mut (_, time)| time > day_ago);
        }

        Ok(())
    }
}

#[async_trait]
impl AuthService for SecureAuthManager {
    async fn authenticate_user(&self, username: &str, password: &str) -> Result<User> {
        // 验证输入安全性
        if username.is_empty() || password.is_empty() {
            return Err(ProxyError::authentication("用户名或密码不能为空"));
        }

        // 检查登录限制
        self.check_login_limit(username).await?;

        // 这里应该连接到实际的用户数据库
        // 目前返回一个示例用户进行测试
        #[cfg(feature = "auth")]
        {
            // 验证密码（这里需要从数据库获取实际的哈希密码）
            let stored_hash = "$argon2id$v=19$m=65536,t=3,p=4$example$hash"; // 示例哈希
            if Self::verify_password(password, stored_hash).unwrap_or(false) {
                self.reset_login_attempts(username).await?;

                Ok(User {
                    id: "user123".to_string(),
                    username: username.to_string(),
                    email: format!("{}@example.com", username),
                    roles: vec!["user".to_string()],
                    created_at: Utc::now(),
                    last_login: Some(Utc::now()),
                    is_active: true,
                })
            } else {
                self.record_failed_attempt(username).await?;
                Err(ProxyError::authentication("用户名或密码错误"))
            }
        }

        #[cfg(not(feature = "auth"))]
        {
            // 如果未启用认证功能，返回错误
            Err(ProxyError::service_unavailable("认证功能"))
        }
    }

    async fn generate_jwt(&self, user: &User, session_id: &str) -> Result<String> {
        let now = Utc::now();
        let exp = now + chrono::Duration::seconds(self.config.token_duration as i64);

        let claims = Claims {
            sub: user.id.clone(),
            iat: now.timestamp() as usize,
            exp: exp.timestamp() as usize,
            nbf: now.timestamp() as usize,
            jti: Uuid::new_v4().to_string(),
            roles: user.roles.clone(),
            session_id: session_id.to_string(),
            ip_addr: None,
        };

        encode(&Header::default(), &claims, &self.encoding_key)
            .map_err(|e| ProxyError::internal(format!("JWT生成失败: {}", e)))
    }

    async fn verify_jwt(&self, token: &str) -> Result<Claims> {
        let mut validation = Validation::new(Algorithm::HS256);
        validation.validate_exp = true;
        validation.validate_nbf = true;

        let token_data = decode::<Claims>(token, &self.decoding_key, &validation)
            .map_err(|e| ProxyError::authentication(format!("令牌无效: {}", e)))?;

        let claims = token_data.claims;

        // 检查令牌是否被撤销
        {
            let revoked = self.revoked_tokens.read().await;
            if revoked.contains_key(&claims.jti) {
                return Err(ProxyError::session("令牌已被撤销"));
            }
        }

        // 验证会话是否仍然有效
        {
            let sessions = self.active_sessions.read().await;
            if let Some(session) = sessions.get(&claims.session_id) {
                let session_timeout = Duration::from_secs(self.config.session_timeout);
                if session.last_activity + session_timeout < SystemTime::now() {
                    return Err(ProxyError::session("会话已过期"));
                }
            } else {
                return Err(ProxyError::session("会话不存在"));
            }
        }

        Ok(claims)
    }

    async fn create_session(&self, user: &User, client_ip: Option<&str>) -> Result<UserSession> {
        let session_id = Uuid::new_v4().to_string();
        let now = SystemTime::now();

        let session_data = UserSession {
            user_id: user.id.clone(),
            session_id: session_id.clone(),
            created_at: now,
            last_activity: now,
            ip_address: client_ip.map(|ip| ip.to_string()),
            user_agent: None,
        };

        {
            let mut sessions = self.active_sessions.write().await;
            sessions.insert(session_id.clone(), session_data.clone());
        }

        info!("为用户 {} 创建新会话: {}", user.id, session_id);
        Ok(session_data)
    }

    async fn verify_session(
        &self,
        session_id: &str,
        client_ip: Option<&str>,
    ) -> Result<UserSession> {
        let mut sessions = self.active_sessions.write().await;

        if let Some(session) = sessions.get_mut(session_id) {
            let session_timeout = Duration::from_secs(self.config.session_timeout);

            if session.last_activity + session_timeout < SystemTime::now() {
                // 会话已过期，移除它
                sessions.remove(session_id);
                return Err(ProxyError::session("会话已过期"));
            }

            // 可选：验证IP地址（如果需要严格的安全性）
            let should_remove =
                if let (Some(session_ip), Some(current_ip)) = (&session.ip_address, client_ip) {
                    if session_ip != current_ip {
                        warn!(
                            "会话 {} 的IP地址不匹配: {} vs {}",
                            session_id, session_ip, current_ip
                        );
                        true
                    } else {
                        false
                    }
                } else {
                    false
                };

            if should_remove {
                sessions.remove(session_id);
                return Err(ProxyError::security_violation("会话IP地址不匹配"));
            }

            // 更新最后活动时间
            session.last_activity = SystemTime::now();
            Ok(session.clone())
        } else {
            Err(ProxyError::session("会话不存在"))
        }
    }

    async fn revoke_session(&self, session_id: &str) -> Result<()> {
        let mut sessions = self.active_sessions.write().await;
        if sessions.remove(session_id).is_some() {
            info!("会话已撤销: {}", session_id);
        }
        Ok(())
    }

    async fn revoke_all_user_sessions(&self, user_id: &str) -> Result<()> {
        let mut sessions = self.active_sessions.write().await;
        let initial_count = sessions.len();

        sessions.retain(|_, session| session.user_id != user_id);

        let removed_count = initial_count - sessions.len();
        if removed_count > 0 {
            info!("为用户 {} 撤销了 {} 个会话", user_id, removed_count);
        }

        Ok(())
    }

    async fn record_login_attempt(
        &self,
        identifier: &str,
        success: bool,
        client_ip: Option<&str>,
    ) -> Result<()> {
        if success {
            self.reset_login_attempts(identifier).await?;
        } else {
            self.record_failed_attempt(identifier).await?;
        }

        info!(
            "登录尝试记录 - 用户: {}, 成功: {}, IP: {:?}",
            identifier, success, client_ip
        );

        Ok(())
    }

    async fn is_user_locked(&self, identifier: &str) -> Result<bool> {
        let locked = self.locked_users.read().await;
        if let Some(&lock_time) = locked.get(identifier) {
            let unlock_time = lock_time + Duration::from_secs(self.config.lockout_duration);
            Ok(SystemTime::now() < unlock_time)
        } else {
            Ok(false)
        }
    }

    async fn unlock_user(&self, identifier: &str) -> Result<()> {
        let mut locked = self.locked_users.write().await;
        locked.remove(identifier);

        let mut attempts = self.login_attempts.write().await;
        attempts.remove(identifier);

        info!("用户 {} 已解锁", identifier);
        Ok(())
    }

    async fn cleanup_expired(&self) -> Result<u32> {
        let initial_session_count = {
            let sessions = self.active_sessions.read().await;
            sessions.len()
        };

        // 清理过期会话
        {
            let mut sessions = self.active_sessions.write().await;
            let session_timeout = Duration::from_secs(self.config.session_timeout);
            let now = SystemTime::now();

            sessions.retain(|_, session| session.last_activity + session_timeout > now);
        }

        let final_session_count = {
            let sessions = self.active_sessions.read().await;
            sessions.len()
        };

        let removed_sessions = initial_session_count - final_session_count;

        // 清理其他过期数据
        self.cleanup_internal().await?;

        if removed_sessions > 0 {
            info!("清理了 {} 个过期会话", removed_sessions);
        }

        Ok(removed_sessions as u32)
    }
}

/// 权限验证辅助函数 - 使用统一的错误系统
pub async fn verify_role_permission(claims: &Claims, required_role: &str) -> Result<()> {
    if claims.roles.contains(&required_role.to_string())
        || claims.roles.contains(&"admin".to_string())
    {
        Ok(())
    } else {
        Err(ProxyError::authorization("权限不足"))
    }
}

/// 创建默认的认证管理器
pub fn create_default_auth_manager() -> Result<SecureAuthManager> {
    SecureAuthManager::new(AuthConfig::default())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_auth_manager_creation() {
        let config = AuthConfig::default();
        let auth_manager = SecureAuthManager::new(config);
        assert!(auth_manager.is_ok());
    }

    #[tokio::test]
    async fn test_session_management() {
        let auth_manager = create_default_auth_manager().unwrap();

        let user = User {
            id: "test_user".to_string(),
            username: "testuser".to_string(),
            email: "<EMAIL>".to_string(),
            roles: vec!["user".to_string()],
            created_at: Utc::now(),
            last_login: Some(Utc::now()),
            is_active: true,
        };

        // 创建会话
        let session = auth_manager.create_session(&user, Some("127.0.0.1")).await;
        assert!(session.is_ok());

        let session = session.unwrap();

        // 验证会话
        let verified = auth_manager
            .verify_session(&session.session_id, Some("127.0.0.1"))
            .await;
        assert!(verified.is_ok());

        // 撤销会话
        let revoked = auth_manager.revoke_session(&session.session_id).await;
        assert!(revoked.is_ok());

        // 验证撤销后的会话应该失败
        let verified_after_revoke = auth_manager
            .verify_session(&session.session_id, Some("127.0.0.1"))
            .await;
        assert!(verified_after_revoke.is_err());
    }
}
