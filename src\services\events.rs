use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{broadcast, RwLock};

use crate::services::{Event, EventService, EventSubscriber, Service};
use crate::types::{ProxyError, ProxyResult as Result};

/// 系统事件类型
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum SystemEvent {
    ConfigChanged {
        timestamp: DateTime<Utc>,
        changes: Vec<String>,
    },
    ProxyRequest {
        timestamp: DateTime<Utc>,
        method: String,
        path: String,
        status: u16,
        duration_ms: u64,
        client_ip: String,
    },
    SecurityAlert {
        timestamp: DateTime<Utc>,
        level: AlertLevel,
        message: String,
        source_ip: Option<String>,
    },
    CacheOperation {
        timestamp: DateTime<Utc>,
        operation: String,
        key: String,
        hit: bool,
    },
    HealthCheck {
        timestamp: DateTime<Utc>,
        service: String,
        status: HealthStatus,
        details: Option<String>,
    },
    UserAction {
        timestamp: DateTime<Utc>,
        user_id: String,
        action: String,
        resource: String,
        success: bool,
    },
    TaskProgress {
        timestamp: DateTime<Utc>,
        task_id: String,
        progress: u8,
        status: TaskStatus,
        message: Option<String>,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertLevel {
    Low,
    Medium,
    High,
    Critical,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum HealthStatus {
    Healthy,
    Degraded,
    Unhealthy,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TaskStatus {
    Running,
    Completed,
    Failed,
    Cancelled,
}

/// 事件处理器特征
#[async_trait]
pub trait EventHandler: Send + Sync {
    async fn handle(&self, event: &SystemEvent) -> Result<()>;
    fn event_types(&self) -> Vec<&'static str>;
}

/// 事件总线实现
pub struct EventBus {
    // 使用类型安全的广播通道
    event_tx: broadcast::Sender<SystemEvent>,

    // 事件处理器注册表
    handlers: Arc<RwLock<HashMap<String, Vec<Arc<dyn EventHandler>>>>>,

    // 事件存储（可选，用于审计和重放）
    event_store: Option<Arc<dyn EventStore>>,

    // 配置
    config: EventBusConfig,
}

#[derive(Debug, Clone)]
pub struct EventBusConfig {
    pub channel_capacity: usize,
    pub enable_persistence: bool,
    pub max_stored_events: usize,
    pub batch_size: usize,
}

impl Default for EventBusConfig {
    fn default() -> Self {
        Self {
            channel_capacity: 1000,
            enable_persistence: false,
            max_stored_events: 10000,
            batch_size: 100,
        }
    }
}

/// 事件存储接口
#[async_trait]
pub trait EventStore: Send + Sync {
    async fn store(&self, event: &SystemEvent) -> Result<()>;
    async fn retrieve(&self, filter: EventFilter) -> Result<Vec<SystemEvent>>;
    async fn count(&self, filter: EventFilter) -> Result<usize>;
}

#[derive(Debug, Clone)]
pub struct EventFilter {
    pub event_types: Option<Vec<String>>,
    pub start_time: Option<DateTime<Utc>>,
    pub end_time: Option<DateTime<Utc>>,
    pub limit: Option<usize>,
    pub offset: Option<usize>,
}

impl EventBus {
    pub fn new() -> Self {
        Self::with_config(EventBusConfig::default())
    }

    pub fn with_config(config: EventBusConfig) -> Self {
        let (event_tx, _) = broadcast::channel(config.channel_capacity);

        Self {
            event_tx,
            handlers: Arc::new(RwLock::new(HashMap::new())),
            event_store: None,
            config,
        }
    }

    pub fn with_store(mut self, store: Arc<dyn EventStore>) -> Self {
        self.event_store = Some(store);
        self
    }

    /// 发布事件
    pub async fn publish(&self, event: SystemEvent) -> Result<()> {
        // 发送到广播通道
        self.event_tx
            .send(event.clone())
            .map_err(|_| ProxyError::internal("事件总线已关闭"))?;

        // 持久化存储（如果启用）
        if let Some(store) = &self.event_store {
            store.store(&event).await?;
        }

        // 触发处理器
        self.trigger_handlers(&event).await?;

        Ok(())
    }

    /// 订阅事件
    pub fn subscribe(&self) -> broadcast::Receiver<SystemEvent> {
        self.event_tx.subscribe()
    }

    /// 注册事件处理器
    pub async fn register_handler(&self, handler: Arc<dyn EventHandler>) {
        let mut handlers = self.handlers.write().await;

        for event_type in handler.event_types() {
            handlers
                .entry(event_type.to_string())
                .or_insert_with(Vec::new)
                .push(handler.clone());
        }
    }

    /// 触发事件处理器
    async fn trigger_handlers(&self, event: &SystemEvent) -> Result<()> {
        let event_type = self.get_event_type(event);
        let handlers_to_execute: Vec<Arc<dyn EventHandler>> = {
            let handlers_map = self.handlers.read().await; // Acquire read lock
            if let Some(specific_handlers) = handlers_map.get(&event_type) {
                specific_handlers.clone() // Clone the Vec of Arcs
            } else {
                Vec::new()
            }
            // Read lock is released here as handlers_map goes out of scope
        };

        if !handlers_to_execute.is_empty() {
            let mut join_handles = Vec::new();

            for handler_arc in handlers_to_execute {
                let event_clone = event.clone();
                let join_handle = tokio::spawn(async move {
                    if let Err(e) = handler_arc.handle(&event_clone).await {
                        tracing::error!(
                            "事件处理器 {:?} 执行失败: {}",
                            handler_arc.event_types(),
                            e
                        );
                    }
                });
                join_handles.push(join_handle);
            }

            // 等待所有处理器完成（带超时）
            for handle in join_handles {
                if let Err(e) =
                    tokio::time::timeout(std::time::Duration::from_secs(30), handle).await
                {
                    // Log timeout error, e might be JoinError or TimeoutElapsed
                    tracing::warn!("事件处理器执行超时或任务panic: {:?}", e);
                }
            }
        }

        Ok(())
    }

    fn get_event_type(&self, event: &SystemEvent) -> String {
        match event {
            SystemEvent::ConfigChanged { .. } => "config_changed".to_string(),
            SystemEvent::ProxyRequest { .. } => "proxy_request".to_string(),
            SystemEvent::SecurityAlert { .. } => "security_alert".to_string(),
            SystemEvent::CacheOperation { .. } => "cache_operation".to_string(),
            SystemEvent::HealthCheck { .. } => "health_check".to_string(),
            SystemEvent::UserAction { .. } => "user_action".to_string(),
            SystemEvent::TaskProgress { .. } => "task_progress".to_string(),
        }
    }
}

#[async_trait]
impl EventService for EventBus {
    async fn publish(&self, topic: &str, payload: &[u8]) -> Result<()> {
        // 将payload转换为SystemEvent
        let event_str = String::from_utf8_lossy(payload);
        let system_event = SystemEvent::UserAction {
            timestamp: Utc::now(),
            user_id: "system".to_string(),
            action: topic.to_string(),
            resource: event_str.to_string(),
            success: true,
        };

        self.publish(system_event).await
    }

    async fn subscribe(&self, _topic: &str) -> Result<Box<dyn EventSubscriber + Send + Sync>> {
        // 简化实现，返回错误
        Err(ProxyError::internal("订阅功能暂未实现"))
    }
}

#[async_trait]
impl Service for EventBus {
    async fn start(&self) -> Result<()> {
        tracing::info!("事件总线启动成功");
        Ok(())
    }

    async fn stop(&self) -> Result<()> {
        tracing::info!("事件总线已停止");
        Ok(())
    }

    fn name(&self) -> &'static str {
        "event_bus"
    }
}

/// 常用事件构建器
impl SystemEvent {
    pub fn config_changed(changes: Vec<String>) -> Self {
        Self::ConfigChanged {
            timestamp: Utc::now(),
            changes,
        }
    }

    pub fn proxy_request(
        method: String,
        path: String,
        status: u16,
        duration_ms: u64,
        client_ip: String,
    ) -> Self {
        Self::ProxyRequest {
            timestamp: Utc::now(),
            method,
            path,
            status,
            duration_ms,
            client_ip,
        }
    }

    pub fn security_alert(level: AlertLevel, message: String, source_ip: Option<String>) -> Self {
        Self::SecurityAlert {
            timestamp: Utc::now(),
            level,
            message,
            source_ip,
        }
    }

    pub fn cache_operation(operation: String, key: String, hit: bool) -> Self {
        Self::CacheOperation {
            timestamp: Utc::now(),
            operation,
            key,
            hit,
        }
    }

    pub fn health_check(service: String, status: HealthStatus, details: Option<String>) -> Self {
        Self::HealthCheck {
            timestamp: Utc::now(),
            service,
            status,
            details,
        }
    }

    pub fn user_action(user_id: String, action: String, resource: String, success: bool) -> Self {
        Self::UserAction {
            timestamp: Utc::now(),
            user_id,
            action,
            resource,
            success,
        }
    }

    pub fn task_progress(
        task_id: String,
        progress: u8,
        status: TaskStatus,
        message: Option<String>,
    ) -> Self {
        Self::TaskProgress {
            timestamp: Utc::now(),
            task_id,
            progress,
            status,
            message,
        }
    }
}

/// 内存事件存储实现（用于开发和测试）
pub struct MemoryEventStore {
    events: Arc<RwLock<Vec<SystemEvent>>>,
    max_events: usize,
}

impl MemoryEventStore {
    pub fn new(max_events: usize) -> Self {
        Self {
            events: Arc::new(RwLock::new(Vec::new())),
            max_events,
        }
    }

    /// 提取事件时间戳的辅助方法
    fn extract_event_timestamp(event: &SystemEvent) -> DateTime<Utc> {
        match event {
            SystemEvent::ConfigChanged { timestamp, .. } => *timestamp,
            SystemEvent::ProxyRequest { timestamp, .. } => *timestamp,
            SystemEvent::SecurityAlert { timestamp, .. } => *timestamp,
            SystemEvent::CacheOperation { timestamp, .. } => *timestamp,
            SystemEvent::HealthCheck { timestamp, .. } => *timestamp,
            SystemEvent::UserAction { timestamp, .. } => *timestamp,
            SystemEvent::TaskProgress { timestamp, .. } => *timestamp,
        }
    }
}

#[async_trait]
impl EventStore for MemoryEventStore {
    async fn store(&self, event: &SystemEvent) -> Result<()> {
        let mut events = self.events.write().await;

        events.push(event.clone());

        // 保持最大事件数量限制
        if events.len() > self.max_events {
            events.remove(0);
        }

        Ok(())
    }

    async fn retrieve(&self, filter: EventFilter) -> Result<Vec<SystemEvent>> {
        let events = self.events.read().await;
        let mut filtered_events: Vec<SystemEvent> = events
            .iter()
            .filter(|event| {
                // 提取事件时间戳的辅助函数
                let event_time = Self::extract_event_timestamp(event);

                // 应用时间过滤器
                if let Some(start_time) = filter.start_time {
                    if event_time < start_time {
                        return false;
                    }
                }

                if let Some(end_time) = filter.end_time {
                    if event_time > end_time {
                        return false;
                    }
                }

                true
            })
            .cloned()
            .collect();

        // 应用分页
        if let Some(offset) = filter.offset {
            if offset < filtered_events.len() {
                filtered_events = filtered_events.into_iter().skip(offset).collect();
            } else {
                filtered_events.clear();
            }
        }

        if let Some(limit) = filter.limit {
            filtered_events.truncate(limit);
        }

        Ok(filtered_events)
    }

    async fn count(&self, _filter: EventFilter) -> Result<usize> {
        let events = self.events.read().await;
        Ok(events.len())
    }
}

/// 内存事件服务实现
pub struct InMemoryEventService {
    event_bus: EventBus,
}

impl InMemoryEventService {
    pub fn new() -> Self {
        Self {
            event_bus: EventBus::new(),
        }
    }
}

#[async_trait]
impl EventService for InMemoryEventService {
    async fn publish(&self, topic: &str, payload: &[u8]) -> Result<()> {
        let system_event = SystemEvent::UserAction {
            timestamp: Utc::now(),
            user_id: "system".to_string(),
            action: topic.to_string(),
            resource: String::from_utf8_lossy(payload).to_string(),
            success: true,
        };

        self.event_bus.publish(system_event).await
    }

    async fn subscribe(&self, topic: &str) -> Result<Box<dyn EventSubscriber + Send + Sync>> {
        let receiver = self.event_bus.subscribe();
        let subscriber = InMemoryEventSubscriber::new(receiver, topic.to_string());
        Ok(Box::new(subscriber))
    }
}

#[async_trait]
impl Service for InMemoryEventService {
    async fn start(&self) -> Result<()> {
        self.event_bus.start().await
    }

    async fn stop(&self) -> Result<()> {
        self.event_bus.stop().await
    }

    fn name(&self) -> &'static str {
        "in_memory_event_service"
    }
}

/// 内存事件订阅者实现
pub struct InMemoryEventSubscriber {
    receiver: broadcast::Receiver<SystemEvent>,
    topic: String,
}

impl InMemoryEventSubscriber {
    pub fn new(receiver: broadcast::Receiver<SystemEvent>, topic: String) -> Self {
        Self { receiver, topic }
    }
}

#[async_trait]
impl EventSubscriber for InMemoryEventSubscriber {
    async fn next_event(&mut self) -> Result<Option<Event>> {
        match self.receiver.recv().await {
            Ok(system_event) => {
                let event = Event {
                    topic: self.topic.clone(),
                    payload: serde_json::to_vec(&system_event)?,
                    timestamp: std::time::SystemTime::now(),
                };
                Ok(Some(event))
            }
            Err(broadcast::error::RecvError::Closed) => Ok(None),
            Err(broadcast::error::RecvError::Lagged(_)) => {
                // 处理滞后，继续尝试接收下一个事件
                self.next_event().await
            }
        }
    }
}
