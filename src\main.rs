//! SM - 智能代理系统
//!
//! 基于Pingora的高性能反向代理系统，提供：
//! - Pingora反向代理核心 (端口1911)
//! - Web管理界面 (端口1319)
//! - 递归代理功能
//! - 域名池管理

use std::sync::Arc;
use tokio::signal;
use tracing::{error, info};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

// 导入核心模块
mod api;
mod auth;
mod config;
mod db;
mod domain_pool;
mod error;
mod proxy_manager;
mod recursive_proxy;
mod security;
mod services;
mod types;
mod utils;

// 导入Pingora相关
use crate::types::ProxyResult;
use proxy_core::ProxyServerBuilder;

/// 系统配置
#[derive(Clone)]
pub struct SystemConfig {
    pub web_addr: String,
    pub proxy_addr: String,
    pub mongodb_uri: String,
}

impl Default for SystemConfig {
    fn default() -> Self {
        Self {
            web_addr: std::env::var("WEB_ADDR").unwrap_or_else(|_| "0.0.0.0:1319".to_string()),
            proxy_addr: std::env::var("PROXY_ADDR")
                .unwrap_or_else(|_| "127.0.0.1:1911".to_string()),
            mongodb_uri: std::env::var("MONGODB_URI")
                .unwrap_or_else(|_| "mongodb://localhost:27017/sm_proxy".to_string()),
        }
    }
}

#[tokio::main]
async fn main() -> ProxyResult<()> {
    // 初始化日志
    setup_logging()?;

    info!("🚀 启动SM智能代理系统 (基于Pingora)...");

    // 加载配置
    let config = SystemConfig::default();

    info!("📋 系统配置:");
    info!("  🌐 Web管理界面: http://{}", config.web_addr);
    info!("  🔄 Pingora代理: http://{}", config.proxy_addr);
    info!("  🗄️ MongoDB: {}", config.mongodb_uri);

    // 初始化数据库
    let database = init_database(&config.mongodb_uri).await?;
    info!("✅ 数据库连接成功");

    // 创建域名池服务 - 重新连接 MongoDB（简化实现）
    let mongo_client = mongodb::Client::with_uri_str(&config.mongodb_uri)
        .await
        .map_err(|e| anyhow::anyhow!("MongoDB客户端创建失败: {}", e))?;
    let mongo_db = mongo_client.database("sm");
    let domain_pool_repo = Arc::new(crate::domain_pool::repository::DomainPoolRepository::new(
        mongo_db,
    ));
    let domain_pool_service = Arc::new(
        domain_pool::DomainPoolService::new(domain_pool_repo)
            .map_err(|e| anyhow::anyhow!("域名池服务创建失败: {}", e))?,
    );

    // 创建代理管理器
    let proxy_manager = Arc::new(proxy_manager::ProxyManager::new(
        domain_pool_service.clone(),
    ));

    // 初始化代理管理器
    if let Err(e) = proxy_manager.initialize().await {
        tracing::warn!("代理管理器初始化失败: {}", e);
    }

    // 启动Pingora反向代理服务 (端口1911)
    let proxy_handle = tokio::spawn(start_pingora_proxy(
        config.proxy_addr.clone(),
        proxy_manager.clone(),
    ));

    // 启动Web管理界面 (端口1319)
    let web_handle = tokio::spawn(start_web_interface(
        config.web_addr.clone(),
        database,
        domain_pool_service,
    ));

    info!("✅ 所有服务启动完成");
    info!("🛡️  安全提示: Pingora代理服务仅本地访问");

    // 等待信号或服务完成
    tokio::select! {
        result = proxy_handle => {
            match result {
                Ok(Ok(())) => info!("Pingora代理服务正常退出"),
                Ok(Err(e)) => error!("Pingora代理服务错误: {}", e),
                Err(e) => error!("Pingora代理任务错误: {}", e),
            }
        }
        result = web_handle => {
            match result {
                Ok(Ok(())) => info!("Web管理界面正常退出"),
                Ok(Err(e)) => error!("Web管理界面错误: {}", e),
                Err(e) => error!("Web管理任务错误: {}", e),
            }
        }
        _ = signal::ctrl_c() => {
            info!("收到退出信号，正在关闭服务...");
        }
    }

    info!("🛑 SM智能代理系统已停止");
    Ok(())
}

/// 设置日志系统
fn setup_logging() -> ProxyResult<()> {
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "sm=info,pingora=info".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();
    Ok(())
}

/// 初始化数据库连接
async fn init_database(mongodb_uri: &str) -> ProxyResult<Arc<dyn crate::types::Database>> {
    use services::database::DatabaseAdapter;

    // 从URI中提取数据库名称
    let db_name = mongodb_uri
        .split('/')
        .next_back()
        .unwrap_or("sm_proxy")
        .split('?')
        .next()
        .unwrap_or("sm_proxy");

    let database = DatabaseAdapter::new(mongodb_uri, db_name).await?;
    Ok(Arc::new(database))
}

/// 启动Pingora反向代理服务
async fn start_pingora_proxy(
    listen_addr: String,
    proxy_manager: Arc<proxy_manager::ProxyManager>,
) -> ProxyResult<()> {
    info!("🔄 启动Pingora反向代理服务: {}", listen_addr);

    // 从代理管理器获取当前配置
    let mut config = proxy_manager.get_current_config().await;

    // 如果没有上游服务器，添加默认配置
    if config.upstreams.is_empty() {
        config.upstreams = vec![proxy_config::unified::UpstreamConfig {
            name: "default".to_string(),
            servers: vec![
                proxy_config::unified::ServerEntry {
                    addr: "httpbin.org:80".to_string(),
                },
                proxy_config::unified::ServerEntry {
                    addr: "example.com:80".to_string(),
                },
            ],
        }];
        info!("使用默认上游服务器配置");
    } else {
        info!(
            "使用动态上游服务器配置，包含 {} 个上游",
            config.upstreams.len()
        );
    }

    // 设置服务器配置
    config.server.listen = listen_addr.clone();

    // 构建Pingora服务器
    let server = ProxyServerBuilder::new(&config)
        .with_listen_addr(listen_addr)
        .build()?;

    // 在单独的任务中运行Pingora服务器
    tokio::task::spawn_blocking(move || {
        server.run_forever();
    })
    .await?;

    Ok(())
}

/// 启动Web管理界面
async fn start_web_interface(
    listen_addr: String,
    database: Arc<dyn crate::types::Database>,
    domain_pool_service: Arc<domain_pool::DomainPoolService>,
) -> ProxyResult<()> {
    info!("🌐 启动Web管理界面: {}", listen_addr);

    // 使用现有的API模块，传入域名池服务
    let app = api::create_router_with_services(database, Some(domain_pool_service)).await?;

    // 启动Axum服务器
    let listener = tokio::net::TcpListener::bind(&listen_addr).await?;
    axum::serve(listener, app).await?;

    Ok(())
}
