/**
 * SM镜像系统主题和缩放控制
 * 处理深色/浅色模式切换和页面缩放功能
 */

class ThemeController {
    constructor() {
        this.currentTheme = 'dark'; // 固定为深色模式
        this.currentScale = 100;
        this.scaleSteps = [75, 90, 100, 110, 125, 150];

        this.init();
    }

    init() {
        this.loadSavedSettings();
        this.bindEvents();
        this.updateUI();
    }

    /**
     * 加载保存的设置
     */
    loadSavedSettings() {
        // 固定为深色主题，不从存储加载
        this.currentTheme = 'dark';

        // 加载缩放设置
        const savedScale = localStorage.getItem('sm_scale');
        if (savedScale) {
            this.currentScale = parseInt(savedScale);
        }

        this.applyTheme();
        this.applyScale();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 缩放控制按钮
        const scaleUp = document.getElementById('scale-up');
        const scaleDown = document.getElementById('scale-down');

        if (scaleUp) {
            scaleUp.addEventListener('click', () => {
                this.scaleUp();
            });
        }

        if (scaleDown) {
            scaleDown.addEventListener('click', () => {
                this.scaleDown();
            });
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Plus 放大
            if ((e.ctrlKey || e.metaKey) && (e.key === '=' || e.key === '+')) {
                e.preventDefault();
                this.scaleUp();
            }

            // Ctrl/Cmd + Minus 缩小
            if ((e.ctrlKey || e.metaKey) && e.key === '-') {
                e.preventDefault();
                this.scaleDown();
            }

            // Ctrl/Cmd + 0 重置缩放
            if ((e.ctrlKey || e.metaKey) && e.key === '0') {
                e.preventDefault();
                this.resetScale();
            }
        });
    }

    /**
     * 应用主题（固定深色模式）
     */
    applyTheme() {
        const html = document.documentElement;
        html.setAttribute('data-theme', 'dark');
    }

    /**
     * 放大页面
     */
    scaleUp() {
        const currentIndex = this.scaleSteps.indexOf(this.currentScale);
        if (currentIndex < this.scaleSteps.length - 1) {
            this.currentScale = this.scaleSteps[currentIndex + 1];
            this.applyScale();
            this.saveSettings();
            this.showNotification(`页面缩放: ${this.currentScale}%`, 'info');
        }
    }

    /**
     * 缩小页面
     */
    scaleDown() {
        const currentIndex = this.scaleSteps.indexOf(this.currentScale);
        if (currentIndex > 0) {
            this.currentScale = this.scaleSteps[currentIndex - 1];
            this.applyScale();
            this.saveSettings();
            this.showNotification(`页面缩放: ${this.currentScale}%`, 'info');
        }
    }

    /**
     * 重置缩放
     */
    resetScale() {
        this.currentScale = 100;
        this.applyScale();
        this.saveSettings();
        this.showNotification('页面缩放已重置', 'info');
    }

    /**
     * 应用缩放
     */
    applyScale() {
        const body = document.body;

        // 移除所有缩放类
        this.scaleSteps.forEach(scale => {
            body.classList.remove(`scale-${scale}`);
        });

        // 添加当前缩放类
        body.classList.add(`scale-${this.currentScale}`);

        // 更新缩放指示器
        const scaleIndicator = document.getElementById('scale-indicator');
        if (scaleIndicator) {
            scaleIndicator.textContent = `${this.currentScale}%`;
        }

        // 更新按钮状态
        this.updateScaleButtons();
    }

    /**
     * 更新缩放按钮状态
     */
    updateScaleButtons() {
        const scaleUp = document.getElementById('scale-up');
        const scaleDown = document.getElementById('scale-down');
        const currentIndex = this.scaleSteps.indexOf(this.currentScale);

        if (scaleUp) {
            scaleUp.disabled = currentIndex >= this.scaleSteps.length - 1;
            scaleUp.style.opacity = scaleUp.disabled ? '0.5' : '1';
        }

        if (scaleDown) {
            scaleDown.disabled = currentIndex <= 0;
            scaleDown.style.opacity = scaleDown.disabled ? '0.5' : '1';
        }
    }

    /**
     * 保存设置
     */
    saveSettings() {
        // 只保存缩放设置，主题固定为深色
        localStorage.setItem('sm_scale', this.currentScale.toString());
    }

    /**
     * 更新UI
     */
    updateUI() {
        this.applyTheme();
        this.applyScale();
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        // 如果组件管理器存在，使用它显示通知
        if (window.componentManager) {
            window.componentManager.showNotification(message, type, 2000);
            return;
        }

        // 否则创建简单的通知
        const notification = document.createElement('div');
        notification.className = `theme-notification theme-notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border: 1px solid var(--border-color);
            z-index: 1200;
            font-size: 14px;
            font-weight: 500;
            animation: slideInRight 0.3s ease;
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 2000);
    }

    /**
     * 获取当前主题
     */
    getCurrentTheme() {
        return this.currentTheme;
    }

    /**
     * 获取当前缩放
     */
    getCurrentScale() {
        return this.currentScale;
    }

    /**
     * 设置主题
     */
    setTheme(theme) {
        if (theme === 'dark' || theme === 'light') {
            this.currentTheme = theme;
            this.applyTheme();
            this.saveSettings();
        }
    }

    /**
     * 设置缩放
     */
    setScale(scale) {
        if (this.scaleSteps.includes(scale)) {
            this.currentScale = scale;
            this.applyScale();
            this.saveSettings();
        }
    }
}

// 全局主题控制器实例
let themeController;

// 页面加载完成后初始化主题控制器
document.addEventListener('DOMContentLoaded', () => {
    themeController = new ThemeController();

    // 将主题控制器暴露到全局作用域
    window.themeController = themeController;

    console.log('🎨 主题控制器已初始化');
});

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// 导出主题控制器类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeController;
}
