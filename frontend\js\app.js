/**
 * 智能反向代理 - 主应用JS文件
 * 包含全局功能和公共方法
 */

// 应用程序全局对象
const App = {
    // 控制器实例
    sidebarController: null,
    userMenuController: null,

    // 初始化应用程序
    init: function () {

        // 认证检查已禁用 - 跳过
        console.log('🔓 跳过认证检查 - 认证系统已禁用');

        // 初始化控制器
        this.initializeControllers();

        // 初始化主题
        this.initializeTheme();

        // 初始化通知系统
        this.initializeNotifications();

        // 绑定全局事件
        this.bindEvents();

        console.log("应用程序初始化完成");
    },

    // 检查用户认证状态 - 已禁用登录验证
    checkAuth: function () {
        console.log('🔓 认证检查已禁用 - 跳过登录验证');

        // 设置一个虚拟的认证令牌，避免其他地方的检查失败
        if (!localStorage.getItem('auth_token')) {
            localStorage.setItem('auth_token', 'disabled_auth_token');
            localStorage.setItem('auth_user', JSON.stringify({
                username: 'admin',
                role: 'admin',
                authenticated: true
            }));
        }

        // 不再进行任何认证检查或重定向
        return;
    },

    // 初始化控制器
    initializeControllers: function () {
        // 侧边栏控制器现在由 sidebar.js 独立管理
        console.log('📋 侧边栏控制器由 sidebar.js 管理');

        // 初始化用户菜单控制器
        if (typeof UserMenuController !== 'undefined') {
            this.userMenuController = new UserMenuController();
        }
    },

    // 高亮当前页面的侧边栏菜单项
    highlightCurrentPage: function () {
        // 如果使用新版控制器，让控制器处理高亮逻辑
        if (this.sidebarController) {
            return;
        }

        // 保留旧版本兼容性（仅在没有新控制器时使用）
        const currentPath = window.location.pathname;
        const filename = currentPath.split('/').pop();

        const menuLinks = document.querySelectorAll('.sidebar .menu-list a');
        menuLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href === filename) {
                link.classList.add('is-active');
            }
        });
    },

    // 初始化主题
    initializeTheme: function () {
        // 目前只提供深色主题，未来可扩展为允许用户选择
        document.body.classList.add('dark-theme');
    },

    // 初始化通知系统
    initializeNotifications: function () {
        // 创建通知容器
        if (!document.querySelector('.toast-container')) {
            const toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container';
            document.body.appendChild(toastContainer);
        }
    },

    // 显示通知
    showNotification: function (message, type = 'info', duration = 3000) {
        const container = document.querySelector('.toast-container');

        const toast = document.createElement('div');
        toast.className = `toast is-${type}`;

        // 只保留消息文本内容
        // 添加消息文本
        const messageSpan = document.createElement('span');
        messageSpan.textContent = message;

        toast.appendChild(messageSpan);
        container.appendChild(toast);

        // 动画显示
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // 计时隐藏
        setTimeout(() => {
            toast.classList.remove('show');

            // 动画结束后移除元素
            setTimeout(() => {
                container.removeChild(toast);
            }, 300);
        }, duration);
    },

    // 加载内容
    loadContent: function (elementId, url, callback) {
        const element = document.getElementById(elementId);
        if (!element) return;

        element.innerHTML = '<div class="loading"><span class="icon"><i class="fas fa-spinner fa-pulse"></i></span><p>正在加载数据...</p></div>';

        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络请求失败');
                }
                return response.json();
            })
            .then(data => {
                if (callback) {
                    callback(element, data);
                }
            })
            .catch(error => {
                element.innerHTML = `<div class="notification is-danger">加载失败: ${error.message}</div>`;
                console.error('加载内容错误:', error);
            });
    },

    // 格式化日期时间
    formatDateTime: function (timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    },

    // 绑定全局事件
    bindEvents: function () {
        // 表格行点击事件
        document.addEventListener('click', function (e) {
            if (e.target.matches('.table-row-link') || e.target.closest('.table-row-link')) {
                const row = e.target.closest('.table-row-link');
                const href = row.dataset.href;
                if (href) {
                    window.location.href = href;
                }
            }
        });

        // 模态框控制
        document.addEventListener('click', function (e) {
            // 打开模态框
            if (e.target.matches('[data-modal-open]') || e.target.closest('[data-modal-open]')) {
                const button = e.target.closest('[data-modal-open]') || e.target;
                const modalId = button.dataset.modalOpen;
                const modal = document.getElementById(modalId);

                if (modal) {
                    modal.classList.add('is-active');
                    document.documentElement.classList.add('is-clipped');
                }
            }

            // 关闭模态框
            if (e.target.matches('[data-modal-close]') || e.target.closest('[data-modal-close]') || e.target.classList.contains('modal-background')) {
                const modal = e.target.closest('.modal');
                if (modal) {
                    modal.classList.remove('is-active');
                    document.documentElement.classList.remove('is-clipped');
                }
            }
        });

        // 标签页切换事件
        document.addEventListener('click', function (e) {
            if (e.target.matches('[data-tab]') || e.target.closest('[data-tab]')) {
                const tabLink = e.target.closest('[data-tab]') || e.target;
                const tabId = tabLink.dataset.tab;

                // 获取父容器
                const tabsContainer = tabLink.closest('.tabs');
                if (!tabsContainer) return;

                // 移除所有活动状态
                const allTabLinks = tabsContainer.querySelectorAll('[data-tab]');
                allTabLinks.forEach(link => {
                    link.parentElement.classList.remove('is-active');
                });

                // 设置当前标签为活动
                tabLink.parentElement.classList.add('is-active');

                // 获取内容容器
                const contentContainer = document.querySelector(`.tabs-content[data-tabs="${tabsContainer.dataset.tabs}"]`);
                if (!contentContainer) return;

                // 隐藏所有内容
                const allContents = contentContainer.querySelectorAll('.tab-content');
                allContents.forEach(content => {
                    content.classList.remove('is-active');
                });

                // 显示当前内容
                const activeContent = contentContainer.querySelector(`[data-tab-content="${tabId}"]`);
                if (activeContent) {
                    activeContent.classList.add('is-active');
                }
            }
        });

        // 全局绑定退出登录按钮事件
        document.addEventListener('click', function (e) {
            if (e.target && (e.target.id === 'logout-btn' || e.target.closest('#logout-btn'))) {
                window.logout();
            }
        });
    }
};

// === CSRF Token 自动注入 ===
function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
}

const originalFetch = window.fetch;
window.fetch = async function (input, init = {}) {
    let url = typeof input === 'string' ? input : input.url;
    const method = (init && init.method ? init.method : 'GET').toUpperCase();

    // 自动添加 Authorization 头
    const token = localStorage.getItem('auth_token');
    if (token && url.includes('/api/')) {
        if (!init.headers) init.headers = {};
        if (init.headers instanceof Headers) {
            init.headers.set('Authorization', `Bearer ${token}`);
        } else {
            init.headers['Authorization'] = `Bearer ${token}`;
        }
    }

    // CSRF Token 注入
    if (["POST", "PUT", "DELETE", "PATCH"].includes(method)) {
        const csrfToken = getCookie('csrf_token');
        if (csrfToken) {
            if (!init.headers) init.headers = {};
            if (init.headers instanceof Headers) {
                init.headers.set('X-CSRF-Token', csrfToken);
            } else {
                init.headers['X-CSRF-Token'] = csrfToken;
            }
        }
    }

    try {
        const response = await originalFetch(input, init);
        // 禁用401错误重定向 - 认证已禁用
        if (response.status === 401 && url.includes('/api/')) {
            console.log('🔓 收到401响应，但认证已禁用，继续处理');
            // 不再清除令牌或重定向
            return response;
        }
        return response;
    } catch (err) {
        throw err;
    }
};

// htmx 全局自动带上 Authorization 头
if (window.htmx) {
    document.addEventListener('htmx:configRequest', function (evt) {
        const token = localStorage.getItem('auth_token');
        if (token) {
            evt.detail.headers['Authorization'] = `Bearer ${token}`;
        }
    });
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', function () {
    App.init();
});

window.logout = function () {
    console.log('🔓 登出功能已禁用 - 认证系统已关闭');
    // 不再清除令牌或重定向，因为认证已禁用
    App.showNotification('认证系统已禁用，无需登出', 'info');
};

// 全局显示Toast通知的函数
window.showToast = function (message, type = 'info', duration = 3000) {
    App.showNotification(message, type, duration);
};