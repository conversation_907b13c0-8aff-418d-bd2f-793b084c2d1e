# SM智能代理系统 🚀

> 🤖 **基于Pingora的高性能代理**：企业级反向代理核心，性能卓越
> 🎯 **递归反向代理**：自动URL提取、域名配对、智能缓存、MongoDB存储
> 🔧 **代码质量优化**：经过Clippy深度优化，代码质量显著提升
> 🛡️ **安全加固**：修复跨await持有锁等关键安全问题

## 🆕 最新更新 (v2024.12.20)

**🎉 重大稳定性修复完成！**

✅ **统一错误处理** - 新增AppError统一错误类型，提升系统稳定性
✅ **简化配置管理** - 新增config.simple.yaml简化配置，降低使用门槛
✅ **修复编译错误** - 解决所有类型不匹配和字段缺失问题，编译100%成功
✅ **优化代码质量** - 减少代码重复，提取公共工具函数，提升可维护性
✅ **修复panic风险** - 替换所有.unwrap()调用为适当错误处理，消除崩溃风险
✅ **内存泄漏修复** - 添加集合清理机制，防止内存无限增长
✅ **异步锁优化** - 修复跨await持有锁的死锁风险，提升并发性能
✅ **智能部署更新** - 部署脚本支持新配置格式，自动检测和迁移

**🔧 技术改进详情**：
- 修复了413个编译警告，代码质量显著提升
- 统一了错误处理机制，减少了系统崩溃风险
- 优化了内存管理，防止长时间运行时的内存泄漏
- 改进了异步编程模式，避免死锁和性能问题
- 简化了配置文件格式，降低了部署和维护难度

## ⚡ 快速开始

**🧪 部署前测试（推荐）**：
```bash
# 运行部署前测试，确保系统准备就绪
chmod +x test-deployment.sh && ./test-deployment.sh
```

**只需一条命令，完成所有部署**：

```bash
sed -i '1s/^\xEF\xBB\xBF//' install.sh && sed -i '1s/^\xEF\xBB\xBF//' setup.sh && chmod +x install.sh && sudo rm -rf target/ && ./install.sh
```

**部署完成后访问**：
- 前端管理：http://your-server:1319
- 首次登录：admin / admin888

## 📋 Screen快速参考卡

**SSH断开后查看进度**：
```bash
cd /root/sm
./setup.sh status     # 查看状态
./setup.sh connect    # 连接会话看实时进度
tail -f /tmp/sm_install.log  # 查看日志
```

**Screen会话管理**：
```bash
./setup.sh status     # 📊 查看状态
./setup.sh connect    # 🔗 连接会话
./setup.sh log        # 📄 查看日志
./setup.sh clean      # 🧹 清理会话
```

**常用Screen命令**：
```bash
screen -r sm-install  # 连接会话
Ctrl+A, D            # 分离会话（在Screen内）
screen -list         # 列出所有会话
```

## 🎯 详细部署方式

### 🚀 万能部署命令（强烈推荐）

```bash
# 万能一键部署 - 处理所有可能的问题
sed -i '1s/^\xEF\xBB\xBF//' install.sh && sed -i '1s/^\xEF\xBB\xBF//' setup.sh && chmod +x install.sh && sudo rm -rf target/ && ./install.sh
```

**🎯 万能命令特点**：
- ✅ **处理BOM编码问题** - 自动清理两个脚本的BOM字符
- ✅ **处理权限问题** - 自动添加执行权限
- ✅ **处理编译缓存** - 清理旧编译文件
- ✅ **优化内存使用** - 单线程编译适合小内存服务器
- ✅ **智能权限提升** - install.sh自动处理sudo
- ✅ **Screen持久化执行** - SSH断开后继续运行，直到完成
- ✅ **适用任何环境** - 无论什么问题都能解决

### 🖥️ Screen持久化执行

万能命令支持**Screen持久化执行**，即使SSH断开也会继续运行：

```bash
# 执行万能命令时会询问是否启用Screen持久化
sed -i '1s/^\xEF\xBB\xBF//' install.sh && sed -i '1s/^\xEF\xBB\xBF//' setup.sh && chmod +x install.sh && sudo rm -rf target/ && ./install.sh

# 选择 'y' 启用Screen持久化后：
# ✅ SSH断开后安装继续进行
# ✅ 可以随时重新连接查看进度
# ✅ 完整的日志记录
```

#### **📊 查看安装进度**

**SSH断开后重新连接查看进度**：

```bash
# 1. SSH重新连接
ssh user@your-server
cd /root/sm

# 2. 快速查看状态
./setup.sh status
# 输出示例：
# ✅ 安装会话: 运行中
# ✅ 日志文件: 存在 (408K)

# 3. 查看实时进度（推荐）
./setup.sh connect
# 或者: screen -r sm-install

# 4. 查看安装日志
./setup.sh log
# 或者: tail -f /tmp/sm_install.log
```

#### **🔍 实时进度查看**

**为什么看不到实时进度？**
- **Screen模式**: 安装在后台Screen会话中运行
- **日志模式**: 输出被重定向到日志文件

**如何看到实时进度：**

```bash
# 方法1: 连接到Screen会话（推荐）
screen -r sm-install
# 连接后能看到：
# - 🔨 实时编译输出
# - 📊 进度条和百分比
# - 🎨 完整的彩色显示
# - ⏱️ 当前执行的命令

# 方法2: 实时查看日志
tail -f /tmp/sm_install.log

# 方法3: 下次选择直接执行
./install.sh
# 选择 'n' 不使用Screen，直接在当前终端执行
```

**Screen会话操作**：
```bash
# 连接后的操作
Ctrl+A, D    # 分离会话（安装继续，您可以退出）
Ctrl+C       # 中断安装（如果需要）

# 重新连接
screen -r sm-install
```

#### **📋 Screen管理命令**

```bash
# 查看安装状态
./setup.sh status

# 连接到安装会话（看实时进度）
./setup.sh connect

# 查看安装日志
./setup.sh log

# 实时查看日志
tail -f /tmp/sm_install.log

# 清理会话和日志
./setup.sh clean
```

#### **⚖️ Screen模式 vs 直接执行**

| 特性 | Screen模式 | 直接执行 |
|------|------------|----------|
| **实时进度** | 需要连接查看 | ✅ 直接显示 |
| **SSH断开保护** | ✅ 继续运行 | ❌ 会中断 |
| **网络不稳定** | ✅ 不影响 | ❌ 可能中断 |
| **长时间编译** | ✅ 可分离 | ❌ 需要保持连接 |
| **多终端访问** | ✅ 支持 | ❌ 不支持 |

**选择建议**：
- **网络稳定 + 想看实时进度** → 选择 `n`（直接执行）
- **网络不稳定 + 长时间编译** → 选择 `y`（Screen模式）

### 📋 其他部署方式

#### 方法2: 简化版（确定无BOM问题时）

```bash
# 简化版万能命令
chmod +x install.sh && sudo rm -rf target/ && export CARGO_BUILD_JOBS=1 && ./install.sh
```

#### 方法3: 传统方式

```bash
# 1. 添加执行权限
chmod +x setup.sh

# 2. 运行智能部署
sudo ./setup.sh
```

#### 方法4: 直接运行（无需权限设置）

```bash
# 直接用bash运行
sudo bash setup.sh
```

## 🤖 智能部署特性

✅ **自动检测Linux发行版** (Ubuntu/Debian/CentOS/RHEL/Fedora/Arch)
✅ **智能安装系统依赖** (仅安装缺失的包)
✅ **强制安装MongoDB数据库** (自动安装MongoDB 7.0，必须成功才能继续)
✅ **智能安装Rust环境** (跳过已有安装)
✅ **智能编译项目** (仅在源码更新时重新编译)
✅ **智能配置systemd服务** (跳过已存在的服务，自动修复watchdog问题)
✅ **智能配置防火墙** (仅添加缺失的规则)
✅ **智能创建监控** (避免重复创建)
✅ **智能路径检测** (生产环境优先，支持就地部署和复制部署)

## 🌐 访问地址

部署完成后访问：

- **前端管理**: http://your-server:1319
- **代理服务**: http://your-server:1911
- **首次登录**: admin / admin888 (请立即修改密码)

## 🗄️ 数据库信息

系统强制要求使用MongoDB数据库：

- **MongoDB 7.0**: 自动安装MongoDB 7.0，数据持久化存储
- **强制安装**: 如果MongoDB安装失败，部署会终止并提示手动安装
- **数据库状态**: 部署完成后会显示MongoDB服务状态

## 🛠️ 管理命令

```bash
# 服务管理
sudo systemctl start sm      # 启动服务
sudo systemctl stop sm       # 停止服务
sudo systemctl restart sm    # 重启服务
sudo systemctl status sm     # 查看状态

# 查看日志
sudo journalctl -u sm -f     # 实时日志

# 管理工具
./status.sh                  # 状态检查
./test-api.sh               # API测试
./monitor.sh                # 服务监控
./test-deployment.sh        # 部署测试（新增）
```

## 🧪 测试工具

**部署前测试**：
```bash
# 运行完整的部署前测试
./test-deployment.sh

# 测试内容包括：
# ✅ 编译结果检查
# ✅ 配置文件验证
# ✅ 关键文件完整性
# ✅ 代码质量检查
# ✅ 系统依赖检查
# ✅ MongoDB状态检查
# ✅ 服务状态检查
# ✅ 快速功能测试
```

## 🔄 更新部署

修改代码后，重新运行万能部署命令：

```bash
# 🚀 智能万能命令（自动检测服务器配置并优化）
sed -i '1s/^\xEF\xBB\xBF//' install.sh && sed -i '1s/^\xEF\xBB\xBF//' setup.sh && chmod +x install.sh && sudo rm -rf target/ && ./install.sh
```

**✨ 新特性**: 部署脚本现在会自动检测您的服务器配置并智能优化编译参数！

### 🔍 **自动检测功能**
- **CPU核心数**: 自动检测并设置合适的并行编译任务数
- **内存大小**: 根据内存大小调整编译策略
- **硬盘空间**: 充足时使用临时目录优化编译
- **CPU架构**: 自动启用原生CPU优化

### 📊 **智能优化策略**
| 内存配置 | 并行任务 | 优化级别 | 特殊优化 |
|---------|---------|---------|---------|
| **8GB+** | CPU核心数 | 高性能 | 原生CPU + 单元优化 |
| **4GB+** | 最多2个 | 优化 | 原生CPU优化 |
| **2GB+** | 2个 | 平衡 | 原生CPU + 轻量优化 |
| **<2GB** | 1个 | 保守 | 基础优化 |

### 🎯 **手动指定版本**（如需要）
```bash
# 高性能版本（8GB+内存）
sudo rm -rf target/ && export CARGO_BUILD_JOBS=4 && export RUSTFLAGS="-C target-cpu=native -C opt-level=2 -C codegen-units=1" && ./install.sh

# 平衡版本（4GB内存）
sudo rm -rf target/ && export CARGO_BUILD_JOBS=2 && export RUSTFLAGS="-C target-cpu=native -C opt-level=2" && ./install.sh

# 保守版本（低内存）
sudo rm -rf target/ && export CARGO_BUILD_JOBS=1 && ./install.sh
```

## 📋 系统要求

- **操作系统**: Linux (Ubuntu/Debian/CentOS/RHEL/Fedora/Arch)
- **权限**: sudo权限
- **网络**: 能访问互联网（用于下载依赖）
- **自动安装**: build-essential, pkg-config, libssl-dev, cmake, MongoDB等

## 🚀 特色功能

### 🏗️ **核心架构**
- 🔥 **Pingora代理核心** - 基于字节跳动开源的高性能代理框架
- 🌐 **双端口服务** - Web管理界面(1319) + Pingora代理(1911)
- 🗄️ **MongoDB集成** - 强制使用MongoDB，数据持久化存储
- 🔧 **模块化设计** - 清晰的代码架构，易于维护和扩展

### 🎯 **递归代理功能**
- 🔄 **智能递归** - 自动发现和处理上游域名
- 📊 **实时监控** - 递归会话状态跟踪和统计
- 🧠 **机器学习** - URL模式识别和内容分析(预留接口)
- 🎨 **内容处理** - 支持内容替换和重写功能

### 🛡️ **安全与性能**
- 🔒 **安全加固** - 修复跨await持有锁等关键安全问题
- ⚡ **性能优化** - 经过Clippy深度优化，减少250+警告
- 🛡️ **输入验证** - 完整的安全输入验证和防护
- 📈 **智能缓存** - 基于命中率的磁盘缓存系统

### 🤖 **智能部署**
- 🤖 **零手动操作** - 全自动智能部署
- 🔍 **智能检查** - 只安装和配置需要的组件
- ⚡ **增量更新** - 支持快速重新部署
- 📊 **服务监控** - 自动故障重启和健康检查

## 🆘 故障排除

### 常见问题

#### Q: 为什么使用万能命令？
A: 万能命令处理所有可能的问题：BOM编码、权限、编译缓存、内存限制等，确保在任何环境下都能成功部署。

#### Q: 为什么推荐install.sh而不是setup.sh？
A: install.sh是智能启动器，自动处理权限问题和sudo提升，用户体验更好。

#### Q: Screen持久化有什么好处？
A: Screen持久化执行的好处：
- ✅ **网络断开保护** - SSH断开后安装继续进行
- ✅ **长时间编译** - 适合编译时间较长的情况
- ✅ **随时查看进度** - 可以重新连接查看安装状态
- ✅ **完整日志记录** - 所有输出都保存在日志文件中

#### Q: 为什么Screen模式看不到实时进度？
A: Screen模式下安装在后台运行，需要主动连接查看：
```bash
# 连接到Screen会话看实时进度
screen -r sm-install
# 或者
./setup.sh connect

# 连接后能看到完整的实时输出，包括：
# - 编译进度条
# - 彩色输出
# - 当前执行的命令
# - 错误信息等
```

#### Q: SSH断开后如何查看安装进度？
A: 重新连接后按以下步骤操作：
```bash
# 1. 进入项目目录
cd /root/sm

# 2. 查看安装状态
./setup.sh status

# 3. 连接查看实时进度
./setup.sh connect

# 4. 如果只想看日志
tail -f /tmp/sm_install.log
```

#### Q: 如何选择Screen模式还是直接执行？
A: 根据您的情况选择：

**选择Screen模式（y）当**：
- 网络连接不稳定
- 编译时间较长（>10分钟）
- 需要在安装过程中做其他事情
- 担心SSH连接中断

**选择直接执行（n）当**：
- 网络连接稳定
- 想看到实时进度输出
- 编译时间较短
- 不需要分离会话

#### Q: 如何管理Screen会话？
A: 使用内置的管理功能：
```bash
./setup.sh status    # 查看状态
./setup.sh connect   # 连接会话
./setup.sh log       # 查看日志
./setup.sh clean     # 清理会话

# Screen原生命令
screen -list          # 列出所有会话
screen -r sm-install  # 连接到安装会话
Ctrl+A, D            # 分离会话（在Screen内）
```

#### Q: 安装过程中Screen会话意外断开怎么办？
A: 不用担心，安装会继续进行：
```bash
# 1. 检查是否还在运行
./setup.sh status

# 2. 如果显示"运行中"，重新连接
./setup.sh connect

# 3. 如果显示"没有运行的会话"，检查是否完成
tail -20 /tmp/sm_install.log

# 4. 如果安装失败，清理后重新开始
./setup.sh clean
./install.sh
```

#### Q: MongoDB安装失败怎么办？
A: 系统会终止部署并提示手动安装MongoDB。必须安装MongoDB后才能继续部署，系统不支持内存数据库。

### 🔧 Screen相关故障排除

#### Screen会话问题
```bash
# 问题：There is no screen to be resumed matching sm-install
# 解决：清理死亡会话
./setup.sh clean

# 问题：看不到实时进度
# 解决：连接到Screen会话
./setup.sh connect

# 问题：Screen会话意外断开
# 解决：检查状态并重新连接
./setup.sh status
./setup.sh connect

# 问题：多个重复的Screen会话
# 解决：清理所有会话
./setup.sh clean
screen -wipe
```

#### SSH断开后的恢复流程
```bash
# 1. 重新连接SSH
ssh user@your-server
cd /root/sm

# 2. 检查安装状态
./setup.sh status

# 3. 根据状态采取行动
# 如果显示"运行中"：
./setup.sh connect

# 如果显示"没有运行的会话"：
tail -20 /tmp/sm_install.log  # 检查是否完成或失败

# 如果需要重新开始：
./setup.sh clean
./install.sh
```

#### 编译和网络问题
```bash
# 编译内存不足
export CARGO_BUILD_JOBS=1
./install.sh

# 权限问题
chmod +x install.sh setup.sh
sudo ./install.sh

# 网络问题
ping *******
./install.sh  # 重新运行
```

### 诊断工具

如果遇到问题，请运行诊断脚本：

```bash
./status.sh
```

或查看详细日志：

```bash
sudo journalctl -u sm -n 50
```

### 完全重新部署

如果需要完全重新部署：

```bash
# 停止服务并清理
sudo systemctl stop sm
sudo rm -rf /opt/sm target/

# 重新运行万能命令
sed -i '1s/^\xEF\xBB\xBF//' install.sh && sed -i '1s/^\xEF\xBB\xBF//' setup.sh && chmod +x install.sh && sudo rm -rf target/ && export CARGO_BUILD_JOBS=1 && ./install.sh
```
