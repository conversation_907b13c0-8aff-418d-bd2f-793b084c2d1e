/**
 * SM镜像系统认证状态管理器
 * 处理HTMX认证、令牌管理、认证状态同步
 */

class AuthStateManager {
    constructor() {
        this.token = null;
        this.isAuthenticated = false;
        this.tokenElement = null;
        this.debugMode = false;

        this.init();
    }

    init() {
        // 检查是否已禁用认证系统
        const authToken = localStorage.getItem('auth_token');
        if (authToken === 'disabled_auth_token') {
            console.log('🔓 检测到认证系统已禁用，启用兼容模式');
            this.enableDisabledAuthMode();
            return;
        }

        // 监听用户认证管理器的认证事件
        document.addEventListener('userAuthenticated', (e) => {
            this.handleUserAuthenticated(e.detail);
        });

        // 监听注销事件
        document.addEventListener('logout', () => {
            this.handleLogout();
        });

        // 配置HTMX认证
        this.setupHTMXAuth();

        // 定期验证令牌
        this.startTokenValidation();

        // 调试模式处理
        if (this.debugMode) {
            this.enableDebugMode();
        }
    }

    /**
     * 禁用认证模式 - 用于开发/测试
     */
    enableDisabledAuthMode() {
        console.log('🔓 启用禁用认证模式');
        this.token = 'disabled_auth_token';
        this.isAuthenticated = true;

        if (this.tokenElement) {
            this.tokenElement.value = this.token;
        }

        // 配置HTMX但不进行实际认证检查
        this.setupHTMXAuth();

        // 立即触发认证事件，让所有功能可用
        setTimeout(() => {
            this.triggerAuthStateChange();
            this.updateHTMXElements();
            console.log('🔓 所有功能已启用（认证已禁用）');
        }, 100);
    }

    /**
     * 配置HTMX认证
     */
    setupHTMXAuth() {
        // 获取隐藏的令牌输入框
        this.tokenElement = document.getElementById('auth-token');

        // 配置HTMX请求头
        document.addEventListener('htmx:configRequest', (event) => {
            if (this.token) {
                event.detail.headers['Authorization'] = `Bearer ${this.token}`;
            }
        });

        // 处理认证失败响应
        document.addEventListener('htmx:responseError', (event) => {
            const status = event.detail.xhr.status;

            // 如果认证已禁用，不显示认证错误
            if (this.token === 'disabled_auth_token') {
                console.log(`🔓 收到HTTP ${status}响应，但认证已禁用，继续处理`);
                return;
            }

            if (status === 401) {
                this.handleAuthError('需要登录才能访问此功能');
            } else if (status === 403) {
                this.handleAuthError('访问被拒绝');
            } else if (status >= 500) {
                this.handleAuthError('服务器错误，请稍后重试');
            }
        });

        // 处理成功响应
        document.addEventListener('htmx:afterRequest', (event) => {
            if (event.detail.xhr.status === 200) {
                // 请求成功，更新最后活动时间
                this.updateLastActivity();
            }
        });
    }

    /**
     * 处理用户认证成功
     */
    handleUserAuthenticated(authData) {
        this.token = authData.token;
        this.isAuthenticated = true;

        // 更新隐藏的令牌输入框
        if (this.tokenElement) {
            this.tokenElement.value = this.token;
        }

        // 触发认证状态变化事件
        this.triggerAuthStateChange();

        // 更新所有HTMX元素
        this.updateHTMXElements();

        console.log('✅ 认证状态已更新');
    }

    /**
     * 处理用户注销
     */
    handleLogout() {
        this.token = null;
        this.isAuthenticated = false;

        // 清空令牌输入框
        if (this.tokenElement) {
            this.tokenElement.value = '';
        }

        // 触发认证状态变化事件
        this.triggerAuthStateChange();

        console.log('🔓 用户已注销');
    }

    /**
     * 处理认证错误
     */
    handleAuthError(message) {
        console.warn('🔒 认证错误:', message);

        // 显示错误通知
        if (window.App && window.App.showNotification) {
            window.App.showNotification(message, 'error');
        }

        // 清除认证状态
        this.handleLogout();

        // 可选：重定向到登录页面
        // window.location.href = '/login.html';
    }

    /**
     * 触发认证状态变化事件
     */
    triggerAuthStateChange() {
        const event = new CustomEvent('authStateChanged', {
            detail: {
                isAuthenticated: this.isAuthenticated,
                token: this.token
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * 更新HTMX元素
     */
    updateHTMXElements() {
        // 触发所有需要认证的HTMX元素
        const authElements = document.querySelectorAll('[hx-headers*="Authorization"]');
        authElements.forEach(element => {
            if (element.hasAttribute('hx-trigger')) {
                // 重新触发HTMX请求
                htmx.trigger(element, 'refresh');
            }
        });
    }

    /**
     * 开始令牌验证
     */
    startTokenValidation() {
        // 每5分钟验证一次令牌
        setInterval(() => {
            if (this.token && this.token !== 'disabled_auth_token') {
                this.validateToken();
            }
        }, 5 * 60 * 1000);
    }

    /**
     * 验证令牌有效性
     */
    async validateToken() {
        if (!this.token || this.token === 'disabled_auth_token') {
            return;
        }

        try {
            const response = await fetch('/api/public/auth/validate', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('令牌验证失败');
            }

            const data = await response.json();
            if (!data.valid) {
                throw new Error('令牌已过期');
            }

        } catch (error) {
            console.warn('令牌验证失败:', error);
            this.handleAuthError('会话已过期，请重新登录');
        }
    }

    /**
     * 更新最后活动时间
     */
    updateLastActivity() {
        localStorage.setItem('last_activity', Date.now().toString());
    }

    /**
     * 调试模式
     */
    enableDebugMode() {
        console.warn('⚠️ 认证状态管理器已启用调试模式');
        this.token = 'debug-token-' + Date.now();
        this.isAuthenticated = true;

        if (this.tokenElement) {
            this.tokenElement.value = this.token;
        }

        // 延迟触发认证事件
        setTimeout(() => {
            this.triggerAuthStateChange();
            this.updateHTMXElements();
        }, 100);
    }

    /**
     * 获取当前令牌
     */
    getToken() {
        return this.token;
    }

    /**
     * 检查是否已认证
     */
    isUserAuthenticated() {
        return this.isAuthenticated;
    }
}

// 全局实例
window.authStateManager = new AuthStateManager();