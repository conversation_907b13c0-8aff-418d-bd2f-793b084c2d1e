//! 配置文件监听器 - 精简版，移除重复功能

#![cfg(feature = "watch")]

use crate::unified::{ConfigConverter, UnifiedConfig};
use crate::ConfigError;
use notify::{
    Config, Event, EventKind, RecommendedWatcher, RecursiveMode, Result as NotifyResult, Watcher,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::sync::{broadcast, watch, RwLock};
use tracing::{error, info, warn};

/// 配置变更事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConfigChangeEvent {
    /// 文件被创建
    Created {
        path: PathBuf,
        timestamp: SystemTime,
    },
    /// 文件被修改
    Modified {
        path: PathBuf,
        timestamp: SystemTime,
    },
    /// 文件被删除
    Deleted {
        path: PathBuf,
        timestamp: SystemTime,
    },
    /// 文件被重命名
    Renamed {
        from: PathBuf,
        to: PathBuf,
        timestamp: SystemTime,
    },
    /// 统一配置重新加载
    UnifiedReloaded { config: UnifiedConfig },
    /// 配置验证失败
    ValidationFailed { errors: Vec<String> },
    /// 文件系统错误
    FileSystemError { error: String },
}

/// 配置变更类型 (废弃，保留兼容性)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConfigChangeType {
    Created,
    Modified,
    Deleted,
    Renamed { from: PathBuf, to: PathBuf },
    Error(String),
}

/// 配置监听器状态
#[derive(Debug, Clone)]
pub enum WatcherState {
    /// 正在运行
    Running,
    /// 已暂停
    Paused,
    /// 已停止
    Stopped,
    /// 错误状态
    Error(String),
}

/// 配置文件监听器 - 精简版，只支持统一配置
pub struct ConfigWatcher {
    /// 文件系统监听器
    _watcher: RecommendedWatcher,
    /// 当前配置
    config: Arc<RwLock<UnifiedConfig>>,
    /// 配置变更接收器
    config_rx: watch::Receiver<UnifiedConfig>,
    /// 事件广播发送器
    event_tx: broadcast::Sender<ConfigChangeEvent>,
    /// 监听器状态
    state: Arc<RwLock<WatcherState>>,
    /// 监听的文件路径（支持多文件监听）
    watched_paths: Arc<RwLock<HashMap<PathBuf, SystemTime>>>,
    /// 去抖动延迟
    debounce_delay: Duration,
}

impl ConfigWatcher {
    /// 创建新的配置监听器（支持单个或多个文件）
    pub fn new<P: AsRef<Path>>(
        config_paths: Vec<P>,
        debounce_delay: Option<Duration>,
    ) -> Result<Self, ConfigError> {
        if config_paths.is_empty() {
            return Err(ConfigError::ValidationError(
                "至少需要指定一个配置文件".to_string(),
            ));
        }

        let debounce_delay = debounce_delay.unwrap_or(Duration::from_millis(500));

        // 加载初始配置
        let mut merged_config = None;
        let mut watched_paths = HashMap::new();

        for path in &config_paths {
            let path_buf = path.as_ref().to_path_buf();
            let content = std::fs::read_to_string(&path_buf)
                .map_err(|e| ConfigError::FileError(format!("读取配置文件失败: {}", e)))?;

            let config: UnifiedConfig = serde_yaml::from_str(&content)
                .map_err(|e| ConfigError::ParseError(format!("配置解析失败: {}", e)))?;

            if let Some(base_config) = merged_config {
                merged_config = Some(ConfigConverter::merge_configs(&base_config, &config)?);
            } else {
                merged_config = Some(config);
            }

            watched_paths.insert(path_buf, SystemTime::now());
        }

        let initial_config = merged_config.unwrap();
        ConfigConverter::validate_config(&initial_config)?;

        let (config_tx, config_rx) = watch::channel(initial_config.clone());
        let (event_tx, _) = broadcast::channel(100);

        // 创建文件系统监听器
        let event_tx_clone = event_tx.clone();
        let config_tx_clone = config_tx.clone();
        let watched_paths_clone = Arc::new(RwLock::new(watched_paths.clone()));

        let mut watcher = RecommendedWatcher::new(
            move |res: NotifyResult<Event>| {
                let event_tx = event_tx_clone.clone();
                let config_tx = config_tx_clone.clone(); // This is watch::Sender
                let watched_paths = watched_paths_clone.clone(); // This is Arc<RwLock<HashMap<PathBuf, SystemTime>>>
                let current_debounce_delay = debounce_delay; // Capture debounce_delay

                tokio::spawn(async move {
                    match res {
                        Ok(event) => {
                            // Pass debounce_delay to handle_notify_event
                            if let Err(e) = Self::handle_notify_event(
                                event,
                                event_tx,
                                config_tx,
                                watched_paths,
                                current_debounce_delay,
                            )
                            .await
                            {
                                error!("Error handling file event: {}", e);
                            }
                        }
                        Err(e) => {
                            error!("File watcher error: {}", e);
                            let _ = event_tx.send(ConfigChangeEvent::FileSystemError {
                                error: e.to_string(),
                            });
                        }
                    }
                });
            },
            Config::default(),
        )
        .map_err(|e| ConfigError::WatcherError(format!("Failed to create file watcher: {}", e)))?;

        // 添加监听路径
        for path in &config_paths {
            watcher
                .watch(path.as_ref(), RecursiveMode::NonRecursive)
                .map_err(|e| ConfigError::WatcherError(format!("添加监听路径失败: {}", e)))?;
        }

        Ok(Self {
            _watcher: watcher,
            config: Arc::new(RwLock::new(initial_config)),
            config_rx,
            event_tx,
            state: Arc::new(RwLock::new(WatcherState::Running)),
            watched_paths: Arc::new(RwLock::new(watched_paths)),
            debounce_delay,
        })
    }

    /// 添加新的配置文件到监听列表
    pub async fn add_config_file<P: AsRef<Path>>(&self, path: P) -> Result<(), ConfigError> {
        let path_buf = path.as_ref().to_path_buf();

        // 验证文件存在且可读
        let content = std::fs::read_to_string(&path_buf)
            .map_err(|e| ConfigError::FileError(format!("读取配置文件失败: {}", e)))?;

        let _config: UnifiedConfig = serde_yaml::from_str(&content)
            .map_err(|e| ConfigError::ParseError(format!("配置解析失败: {}", e)))?;

        // 添加到监听路径
        let mut watched_paths = self.watched_paths.write().await;
        watched_paths.insert(path_buf, SystemTime::now());

        info!("已添加配置文件到监听列表: {:?}", path.as_ref());
        Ok(())
    }

    /// 停止监听
    pub async fn stop(&self) {
        let mut state = self.state.write().await;
        *state = WatcherState::Stopped;
        info!("配置监听器已停止");
    }

    /// 获取当前配置
    pub async fn get_config(&self) -> UnifiedConfig {
        self.config.read().await.clone()
    }

    /// 获取配置接收器（用于监听配置变更）
    pub fn config_receiver(&self) -> watch::Receiver<UnifiedConfig> {
        self.config_rx.clone()
    }

    /// 订阅配置变更事件
    pub fn subscribe_events(&self) -> broadcast::Receiver<ConfigChangeEvent> {
        self.event_tx.subscribe()
    }

    /// 获取监听器状态
    pub async fn get_state(&self) -> WatcherState {
        self.state.read().await.clone()
    }

    /// 手动重新加载配置
    pub async fn reload_config<P: AsRef<Path>>(&self, config_path: P) -> Result<(), ConfigError> {
        let content = std::fs::read_to_string(&config_path)
            .map_err(|e| ConfigError::FileError(format!("读取配置文件失败: {}", e)))?;

        let new_config: UnifiedConfig = serde_yaml::from_str(&content)
            .map_err(|e| ConfigError::ParseError(format!("配置解析失败: {}", e)))?;

        ConfigConverter::validate_config(&new_config)?;

        // 更新配置
        {
            let mut config = self.config.write().await;
            *config = new_config.clone();
        }

        // 发送事件
        let _ = self
            .event_tx
            .send(ConfigChangeEvent::UnifiedReloaded { config: new_config });

        info!("配置已手动重新加载: {:?}", config_path.as_ref());
        Ok(())
    }

    /// 处理notify事件
    async fn handle_notify_event(
        event: Event,
        event_tx: broadcast::Sender<ConfigChangeEvent>,
        config_tx: watch::Sender<UnifiedConfig>, // Renamed from _config_tx
        watched_paths_arc: Arc<RwLock<HashMap<PathBuf, SystemTime>>>, // Renamed from _watched_paths
        debounce_delay: Duration,
    ) -> Result<(), ConfigError> {
        let timestamp = SystemTime::now();
        let mut should_reload = false;
        let mut modified_path: Option<PathBuf> = None;

        match event.kind {
            EventKind::Create(kind) => {
                // 处理创建事件 kind: CreateKind
                for path in event.paths {
                    let _ = event_tx.send(ConfigChangeEvent::Created { path, timestamp });
                }
            }
            EventKind::Modify(kind) => {
                // 处理修改事件 kind: ModifyKind
                if matches!(
                    kind,
                    notify::event::ModifyKind::Data(_) | notify::event::ModifyKind::Name(_)
                ) {
                    for path in &event.paths {
                        let watched_paths = watched_paths_arc.read().await;
                        if watched_paths.contains_key(path) {
                            info!("Configuration file modified: {:?}", path);
                            should_reload = true;
                            modified_path = Some(path.clone());
                            let _ = event_tx.send(ConfigChangeEvent::Modified {
                                path: path.clone(),
                                timestamp,
                            });
                            break; // Process first relevant path
                        }
                    }
                }
            }
            EventKind::Remove(kind) => {
                // 处理删除事件 kind: RemoveKind
                if matches!(kind, notify::event::RemoveKind::File) {
                    for path in event.paths {
                        let _ = event_tx.send(ConfigChangeEvent::Deleted { path, timestamp });
                    }
                }
            }
            _ => {}
        }

        if should_reload {
            // Apply debounce
            tokio::time::sleep(debounce_delay).await;

            // Attempt to reload all watched configurations
            let mut new_merged_config: Option<UnifiedConfig> = None;
            let watched_paths = watched_paths_arc.read().await;
            let paths_to_load: Vec<PathBuf> = watched_paths.keys().cloned().collect();
            drop(watched_paths); // Release read lock

            if paths_to_load.is_empty() {
                warn!("No paths to reload, skipping.");
                return Ok(());
            }

            for path_to_load in paths_to_load {
                match tokio::fs::read_to_string(&path_to_load).await {
                    Ok(content) => {
                        match serde_yaml::from_str::<UnifiedConfig>(&content) {
                            Ok(config_part) => {
                                if let Some(base) = new_merged_config.as_mut() {
                                    match ConfigConverter::merge_configs(base, &config_part) {
                                        Ok(merged) => *base = merged,
                                        Err(e) => {
                                            error!(
                                                "Failed to merge config from {:?}: {}",
                                                path_to_load, e
                                            );
                                            let _ = event_tx.send(
                                                ConfigChangeEvent::ValidationFailed {
                                                    errors: vec![e.to_string()],
                                                },
                                            );
                                            return Ok(()); // Stop reload on merge error
                                        }
                                    }
                                } else {
                                    new_merged_config = Some(config_part);
                                }
                            }
                            Err(e) => {
                                error!("Failed to parse config from {:?}: {}", path_to_load, e);
                                let _ = event_tx.send(ConfigChangeEvent::ValidationFailed {
                                    errors: vec![format!(
                                        "Parse error for {:?}: {}",
                                        path_to_load, e
                                    )],
                                });
                                return Ok(()); // Stop reload on parse error
                            }
                        }
                    }
                    Err(e) => {
                        error!("Failed to read config file {:?}: {}", path_to_load, e);
                        let _ = event_tx.send(ConfigChangeEvent::FileSystemError {
                            error: format!("Read error for {:?}: {}", path_to_load, e),
                        });
                        return Ok(()); // Stop reload on read error
                    }
                }
            }

            if let Some(final_config) = new_merged_config {
                match ConfigConverter::validate_config(&final_config) {
                    Ok(_) => {
                        if config_tx.send(final_config.clone()).is_ok() {
                            info!("Configuration reloaded successfully.");
                            let _ = event_tx.send(ConfigChangeEvent::UnifiedReloaded {
                                config: final_config,
                            });
                        } else {
                            error!("Failed to send reloaded config through watch channel (receiver dropped?).");
                        }
                    }
                    Err(errors) => {
                        error!("Reloaded configuration validation failed: {:?}", errors);
                        let _ = event_tx.send(ConfigChangeEvent::ValidationFailed {
                            errors: vec![errors.to_string()],
                        });
                    }
                }
            } else {
                warn!("No configuration was loaded after modification event.");
            }
        }

        Ok(())
    }

    /// 处理文件系统事件 (Kept for now, but logic moved to handle_notify_event)
    async fn handle_file_event(
        _debounce_delay: Duration, // Parameter kept for signature compatibility if called elsewhere
    ) -> Result<(), ConfigError> {
        Ok(())
    }

    /// 尝试重新加载配置 (Kept for now, but logic moved to handle_notify_event)
    async fn try_reload_config(
        event_tx: &broadcast::Sender<ConfigChangeEvent>,
    ) -> Result<(), ConfigError> {
        // Original placeholder logic
        let _ = event_tx.send(ConfigChangeEvent::ValidationFailed {
            errors: vec![
                "Configuration reload logic is now part of handle_notify_event.".to_string(),
            ],
        });
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use tempfile::tempdir;
    use tokio::time::sleep;

    #[tokio::test]
    async fn test_config_watcher_creation() {
        let dir = tempdir().unwrap();
        let config_path = dir.path().join("test.yaml");

        // 创建基本配置文件
        let config_content = r#"
server:
  listen: "0.0.0.0:8080"
  workers: 4
"#;
        fs::write(&config_path, config_content).unwrap();

        let watcher = ConfigWatcher::new(vec![&config_path], None);
        assert!(watcher.is_ok());
    }

    #[tokio::test]
    async fn test_multi_file_watcher() {
        let dir = tempdir().unwrap();
        let config1 = dir.path().join("config1.yaml");
        let config2 = dir.path().join("config2.yaml");

        fs::write(&config1, "server:\n  workers: 4").unwrap();
        fs::write(&config2, "proxy:\n  timeout: 30").unwrap();

        let watcher = ConfigWatcher::new(vec![&config1, &config2], None);
        assert!(watcher.is_ok());
    }
}
