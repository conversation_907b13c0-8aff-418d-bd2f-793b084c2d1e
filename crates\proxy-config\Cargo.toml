[package]
name = "proxy-config"
version = "0.1.0"
edition = "2021"
description = "Configuration management for proxy system"

[dependencies]
# 基础依赖
proxy-types = { path = "../proxy-types" }  # 只依赖基础类型
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"
toml = "0.8"
thiserror = "1.0"
tokio = { version = "1.35", features = ["fs"] }
tracing = "0.1"
notify = { version = "6.1", optional = true }
once_cell = "1.19"
regex = "1.10"

[dev-dependencies]
tempfile = "3.8"

[features]
default = []
watch = ["dep:notify"]