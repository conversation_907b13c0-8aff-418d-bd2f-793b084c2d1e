use crate::error::{AppError, AppR<PERSON>ult};
use serde::{Deserialize, Serialize};
use std::env;
use std::path::Path;

/// 简化的应用配置
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AppConfig {
    pub server: ServerConfig,
    pub database: DatabaseConfig,
    pub security: SecurityConfig,
    pub logging: LoggingConfig,
    pub cache: CacheConfig,
}

/// 服务器配置
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ServerConfig {
    pub web_host: String,
    pub web_port: u16,
    pub proxy_host: String,
    pub proxy_port: u16,
    pub api_host: Option<String>,
    pub api_port: Option<u16>,
    pub workers: Option<usize>,
}

/// 数据库配置
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub mongodb_uri: String,
    pub redis_uri: Option<String>,
    pub connection_timeout: u64,
    pub max_connections: u32,
}

/// 安全配置
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct SecurityConfig {
    pub jwt_secret: String,
    pub admin_username: String,
    pub admin_password_hash: String,
    pub session_timeout: u64,
    pub max_login_attempts: u32,
    pub lockout_duration: u64,
    pub rate_limit_enabled: bool,
    pub rate_limit_per_minute: u32,
    pub access_control: Option<AccessControlConfig>,
}

/// 访问控制配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccessControlConfig {
    pub api_access: ApiAccessConfig,
    pub web_access: WebAccessConfig,
}

/// API访问控制配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiAccessConfig {
    pub internal_ips: Vec<String>,
    pub require_auth_for_external: bool,
    pub internal_endpoints: Vec<String>,
}

/// Web访问控制配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebAccessConfig {
    pub public_access: bool,
    pub allowed_origins: Vec<String>,
    pub security_headers: SecurityHeadersConfig,
}

/// 安全头配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityHeadersConfig {
    pub enabled: bool,
}

/// 日志配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    pub level: String,
    pub file_path: String,
    pub max_size_mb: u64,
    pub max_files: u32,
    pub compress: bool,
}

/// 缓存配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheConfig {
    pub max_size_mb: u64,
    pub default_ttl_seconds: u64,
    pub cleanup_interval_seconds: u64,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            server: ServerConfig::default(),
            database: DatabaseConfig::default(),
            security: SecurityConfig::default(),
            logging: LoggingConfig::default(),
            cache: CacheConfig::default(),
        }
    }
}

impl Default for ServerConfig {
    fn default() -> Self {
        Self {
            web_host: "0.0.0.0".to_string(), // 前端对外开放
            web_port: 1319,
            proxy_host: "127.0.0.1".to_string(), // 代理仅内部访问
            proxy_port: 1911,
            api_host: Some("127.0.0.1".to_string()), // API仅内部访问
            api_port: Some(1320),
            workers: None,
        }
    }
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            mongodb_uri: "mongodb://localhost:27017/sm".to_string(),
            redis_uri: Some("redis://localhost:6379/0".to_string()),
            connection_timeout: 30,
            max_connections: 100,
        }
    }
}

impl Default for SecurityConfig {
    fn default() -> Self {
        Self {
            jwt_secret: "change-me-in-production".to_string(),
            admin_username: "admin".to_string(),
            admin_password_hash: "change-me-in-production".to_string(),
            session_timeout: 1800, // 30 minutes
            max_login_attempts: 3,
            lockout_duration: 3600, // 1 hour
            rate_limit_enabled: true,
            rate_limit_per_minute: 100,
            access_control: Some(AccessControlConfig::default()),
        }
    }
}

impl Default for AccessControlConfig {
    fn default() -> Self {
        Self {
            api_access: ApiAccessConfig::default(),
            web_access: WebAccessConfig::default(),
        }
    }
}

impl Default for ApiAccessConfig {
    fn default() -> Self {
        Self {
            internal_ips: vec![
                "127.0.0.1".to_string(),
                "::1".to_string(),
                "localhost".to_string(),
            ],
            require_auth_for_external: true,
            internal_endpoints: vec![
                "/health".to_string(),
                "/metrics".to_string(),
                "/status".to_string(),
            ],
        }
    }
}

impl Default for WebAccessConfig {
    fn default() -> Self {
        Self {
            public_access: true,
            allowed_origins: vec!["*".to_string()],
            security_headers: SecurityHeadersConfig::default(),
        }
    }
}

impl Default for SecurityHeadersConfig {
    fn default() -> Self {
        Self { enabled: true }
    }
}

impl Default for LoggingConfig {
    fn default() -> Self {
        Self {
            level: "info".to_string(),
            file_path: "./logs/app.log".to_string(),
            max_size_mb: 100,
            max_files: 10,
            compress: true,
        }
    }
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            max_size_mb: 256,
            default_ttl_seconds: 1800,     // 30 minutes
            cleanup_interval_seconds: 300, // 5 minutes
        }
    }
}

/// 配置加载器
pub struct ConfigLoader;

impl ConfigLoader {
    /// 从文件加载配置
    pub fn load_from_file<P: AsRef<Path>>(path: P) -> AppResult<AppConfig> {
        let path = path.as_ref();

        if !path.exists() {
            return Ok(AppConfig::default());
        }

        let content = std::fs::read_to_string(path)
            .map_err(|e| AppError::app_config(format!("读取配置文件失败: {}", e)))?;

        let mut config: AppConfig = if path.extension().and_then(|s| s.to_str()) == Some("yaml")
            || path.extension().and_then(|s| s.to_str()) == Some("yml")
        {
            serde_yaml::from_str(&content)
                .map_err(|e| AppError::app_config(format!("YAML解析失败: {}", e)))?
        } else {
            serde_json::from_str(&content)
                .map_err(|e| AppError::app_config(format!("JSON解析失败: {}", e)))?
        };

        // 应用环境变量覆盖
        Self::apply_env_overrides(&mut config)?;

        // 验证配置
        Self::validate_config(&config)?;

        Ok(config)
    }

    /// 应用环境变量覆盖
    fn apply_env_overrides(config: &mut AppConfig) -> AppResult<()> {
        // 服务器配置
        if let Ok(host) = env::var("WEB_HOST") {
            config.server.web_host = host;
        }
        if let Ok(port) = env::var("WEB_PORT") {
            config.server.web_port = port
                .parse()
                .map_err(|_| AppError::app_config("WEB_PORT必须是有效的端口号"))?;
        }
        if let Ok(host) = env::var("PROXY_HOST") {
            config.server.proxy_host = host;
        }
        if let Ok(port) = env::var("PROXY_PORT") {
            config.server.proxy_port = port
                .parse()
                .map_err(|_| AppError::app_config("PROXY_PORT必须是有效的端口号"))?;
        }
        if let Ok(host) = env::var("API_HOST") {
            config.server.api_host = Some(host);
        }
        if let Ok(port) = env::var("API_PORT") {
            config.server.api_port = Some(
                port.parse()
                    .map_err(|_| AppError::app_config("API_PORT必须是有效的端口号"))?,
            );
        }

        // 数据库配置
        if let Ok(uri) = env::var("MONGODB_URI") {
            config.database.mongodb_uri = uri;
        }
        if let Ok(uri) = env::var("REDIS_URI") {
            config.database.redis_uri = Some(uri);
        }

        // 安全配置
        if let Ok(secret) = env::var("JWT_SECRET") {
            config.security.jwt_secret = secret;
        }
        if let Ok(username) = env::var("ADMIN_USERNAME") {
            config.security.admin_username = username;
        }
        if let Ok(hash) = env::var("ADMIN_PASSWORD_HASH") {
            config.security.admin_password_hash = hash;
        }

        // 日志配置
        if let Ok(level) = env::var("LOG_LEVEL") {
            config.logging.level = level;
        }

        Ok(())
    }

    /// 验证配置
    fn validate_config(config: &AppConfig) -> AppResult<()> {
        // 验证端口号
        if config.server.web_port == 0 || config.server.web_port == config.server.proxy_port {
            return Err(AppError::app_config("Web端口和代理端口不能相同且必须大于0"));
        }

        // 验证数据库URI
        if config.database.mongodb_uri.is_empty() {
            return Err(AppError::app_config("MongoDB URI不能为空"));
        }

        // 验证安全配置
        if config.security.jwt_secret == "change-me-in-production" {
            return Err(AppError::app_config("生产环境必须设置JWT_SECRET环境变量"));
        }
        if config.security.admin_password_hash == "change-me-in-production" {
            return Err(AppError::app_config(
                "生产环境必须设置ADMIN_PASSWORD_HASH环境变量",
            ));
        }

        // 验证日志级别
        let valid_levels = ["trace", "debug", "info", "warn", "error"];
        if !valid_levels.contains(&config.logging.level.as_str()) {
            return Err(AppError::app_config(format!(
                "无效的日志级别: {}，有效值: {:?}",
                config.logging.level, valid_levels
            )));
        }

        Ok(())
    }

    /// 保存配置到文件
    pub fn save_to_file<P: AsRef<Path>>(config: &AppConfig, path: P) -> AppResult<()> {
        let path = path.as_ref();

        let content = if path.extension().and_then(|s| s.to_str()) == Some("yaml")
            || path.extension().and_then(|s| s.to_str()) == Some("yml")
        {
            serde_yaml::to_string(config)
                .map_err(|e| AppError::app_config(format!("YAML序列化失败: {}", e)))?
        } else {
            serde_json::to_string_pretty(config)
                .map_err(|e| AppError::app_config(format!("JSON序列化失败: {}", e)))?
        };

        // 确保目录存在
        if let Some(parent) = path.parent() {
            std::fs::create_dir_all(parent)
                .map_err(|e| AppError::app_config(format!("创建配置目录失败: {}", e)))?;
        }

        std::fs::write(path, content)
            .map_err(|e| AppError::app_config(format!("写入配置文件失败: {}", e)))?;

        Ok(())
    }

    /// 创建默认配置文件
    pub fn create_default_config<P: AsRef<Path>>(path: P) -> AppResult<()> {
        let config = AppConfig::default();
        Self::save_to_file(&config, path)
    }
}

/// 配置管理器
pub struct ConfigManager {
    config: AppConfig,
    file_path: Option<String>,
}

impl ConfigManager {
    /// 从文件创建配置管理器
    pub fn from_file<P: AsRef<Path>>(path: P) -> AppResult<Self> {
        let path = path.as_ref();
        let config = ConfigLoader::load_from_file(path)?;

        Ok(Self {
            config,
            file_path: Some(path.to_string_lossy().to_string()),
        })
    }

    /// 从默认配置创建配置管理器
    pub fn from_default() -> Self {
        Self {
            config: AppConfig::default(),
            file_path: None,
        }
    }

    /// 获取配置
    pub fn config(&self) -> &AppConfig {
        &self.config
    }

    /// 更新配置
    pub fn update_config(&mut self, new_config: AppConfig) -> AppResult<()> {
        ConfigLoader::validate_config(&new_config)?;
        self.config = new_config;
        Ok(())
    }

    /// 保存配置到文件
    pub fn save(&self) -> AppResult<()> {
        if let Some(path) = &self.file_path {
            ConfigLoader::save_to_file(&self.config, path)?;
        }
        Ok(())
    }

    /// 重新加载配置
    pub fn reload(&mut self) -> AppResult<()> {
        if let Some(path) = &self.file_path {
            self.config = ConfigLoader::load_from_file(path)?;
        }
        Ok(())
    }
}

/// 便利函数：加载配置
pub fn load_config() -> AppResult<AppConfig> {
    // 按优先级尝试加载配置文件
    let config_paths = [
        "config.yaml",
        "config.yml",
        "config.json",
        "config/config.yaml",
        "config/config.yml",
        "config/config.json",
    ];

    for path in &config_paths {
        if Path::new(path).exists() {
            return ConfigLoader::load_from_file(path);
        }
    }

    // 如果没有找到配置文件，使用默认配置
    let mut config = AppConfig::default();
    ConfigLoader::apply_env_overrides(&mut config)?;
    ConfigLoader::validate_config(&config)?;

    Ok(config)
}
