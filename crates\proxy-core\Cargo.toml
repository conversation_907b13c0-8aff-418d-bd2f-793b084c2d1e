[package]
name = "proxy-core"
version = "0.1.0"
edition = "2021"
description = "Core proxy service built on Pingora"

[dependencies]
# 基础依赖
proxy-types = { path = "../proxy-types" }
proxy-config = { path = "../proxy-config" }

# Pingora依赖
pingora = { version = "0.5", features = ["lb"], default-features = false }
pingora-core = { version = "0.5", features = ["openssl"], default-features = false }
pingora-proxy = { version = "0.5", default-features = false }
pingora-http = { version = "0.5", default-features = false }
pingora-load-balancing = { version = "0.5", default-features = false }

# 标准依赖
tokio = "1.35"
anyhow = "1.0"
tracing = "0.1"
serde = { version = "1.0", features = ["derive"] }
async-trait = "0.1"
thiserror = "1.0"
once_cell = "1.19"
regex = "1.10"
serde_json = "1.0"
serde_yaml = "0.9"
toml = "0.8"
paste = "1.0"  # 用于宏生成
zeroize = { version = "1.7", features = ["derive"] }

[features]
default = []