//! 智能缓存系统

use super::config::CacheConfig;
use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use tokio::sync::RwLock;

/// 缓存条目
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CacheEntry {
    /// 缓存内容
    pub content: Vec<u8>,
    /// 内容类型
    pub content_type: String,
    /// 创建时间
    pub created_at: u64,
    /// 过期时间
    pub expires_at: u64,
    /// 命中次数
    pub hit_count: u64,
    /// 最后访问时间
    pub last_accessed: u64,
    /// 内容大小（字节）
    pub size: usize,
}

/// 缓存统计信息
#[derive(Debug, Clone, Default)]
pub struct CacheStats {
    /// 总命中次数
    pub total_hits: u64,
    /// 总请求次数
    pub total_requests: u64,
    /// 当前缓存条目数
    pub entry_count: usize,
    /// 当前缓存大小（字节）
    pub total_size: usize,
    /// 命中率
    pub hit_rate: f64,
}

/// 智能缓存系统
pub struct IntelligentCache {
    /// 缓存存储
    storage: Arc<RwLock<HashMap<String, CacheEntry>>>,
    /// 配置
    config: CacheConfig,
    /// 统计信息
    stats: Arc<RwLock<CacheStats>>,
    /// 最后清理时间
    last_cleanup: Arc<RwLock<Instant>>,
}

impl IntelligentCache {
    /// 创建新的智能缓存
    pub fn new(config: CacheConfig) -> Self {
        Self {
            storage: Arc::new(RwLock::new(HashMap::new())),
            config,
            stats: Arc::new(RwLock::new(CacheStats::default())),
            last_cleanup: Arc::new(RwLock::new(Instant::now())),
        }
    }

    /// 获取缓存内容
    pub async fn get(&self, key: &str) -> Option<CacheEntry> {
        if !self.config.enabled {
            return None;
        }

        let mut stats = self.stats.write().await;
        stats.total_requests += 1;

        let mut storage = self.storage.write().await;

        if let Some(entry) = storage.get_mut(key) {
            let now = self.current_timestamp();

            // 检查是否过期
            if entry.expires_at > now {
                entry.hit_count += 1;
                entry.last_accessed = now;
                stats.total_hits += 1;
                stats.hit_rate = stats.total_hits as f64 / stats.total_requests as f64;

                return Some(entry.clone());
            } else {
                // 移除过期条目
                storage.remove(key);
                stats.entry_count = storage.len();
                stats.total_size = storage.values().map(|e| e.size).sum();
            }
        }

        stats.hit_rate = stats.total_hits as f64 / stats.total_requests as f64;
        None
    }

    /// 设置缓存内容
    pub async fn set(&self, key: String, content: Vec<u8>, content_type: String) -> Result<()> {
        if !self.config.enabled {
            return Ok(());
        }

        let now = self.current_timestamp();
        let expires_at = now + self.config.ttl_seconds;
        let size = content.len();

        let entry = CacheEntry {
            content,
            content_type,
            created_at: now,
            expires_at,
            hit_count: 0,
            last_accessed: now,
            size,
        };

        let mut storage = self.storage.write().await;
        let mut stats = self.stats.write().await;

        // 检查缓存大小限制
        let max_size_bytes = self.config.max_size_mb * 1024 * 1024;
        let current_size = stats.total_size + size;

        if current_size > max_size_bytes as usize {
            // 触发智能清理
            drop(stats);
            drop(storage);
            self.intelligent_cleanup().await?;

            storage = self.storage.write().await;
            stats = self.stats.write().await;
        }

        storage.insert(key, entry);
        stats.entry_count = storage.len();
        stats.total_size = storage.values().map(|e| e.size).sum();

        Ok(())
    }

    /// 智能清理缓存
    pub async fn intelligent_cleanup(&self) -> Result<()> {
        let mut storage = self.storage.write().await;
        let mut stats = self.stats.write().await;
        let now = self.current_timestamp();

        let mut entries_to_remove = Vec::new();

        // 1. 移除过期条目
        for (key, entry) in storage.iter() {
            if entry.expires_at <= now {
                entries_to_remove.push(key.clone());
            }
        }

        // 2. 移除低命中率条目
        let mut hit_rates: Vec<(String, f64)> = storage
            .iter()
            .filter(|(key, _)| !entries_to_remove.contains(key))
            .map(|(key, entry)| {
                let age_seconds = now.saturating_sub(entry.created_at);
                let hit_rate = if age_seconds > 0 {
                    entry.hit_count as f64 / age_seconds as f64
                } else {
                    0.0
                };
                (key.clone(), hit_rate)
            })
            .collect();

        hit_rates.sort_by(|a, b| a.1.partial_cmp(&b.1).unwrap_or(std::cmp::Ordering::Equal));

        // 移除命中率低于阈值的条目
        for (key, hit_rate) in hit_rates {
            if hit_rate < self.config.hit_rate_threshold {
                entries_to_remove.push(key);
            }
        }

        // 3. 如果仍然超过大小限制，移除最旧的条目
        let max_size_bytes = self.config.max_size_mb * 1024 * 1024;
        let mut current_size = storage.values().map(|e| e.size).sum::<usize>();

        while current_size > max_size_bytes as usize && !storage.is_empty() {
            if let Some((oldest_key, oldest_entry)) = storage
                .iter()
                .filter(|(key, _)| !entries_to_remove.contains(key))
                .min_by_key(|(_, entry)| entry.last_accessed)
                .map(|(k, v)| (k.clone(), v.clone()))
            {
                entries_to_remove.push(oldest_key);
                current_size = current_size.saturating_sub(oldest_entry.size);
            } else {
                break;
            }
        }

        // 执行清理
        for key in entries_to_remove {
            storage.remove(&key);
        }

        stats.entry_count = storage.len();
        stats.total_size = storage.values().map(|e| e.size).sum();

        *self.last_cleanup.write().await = Instant::now();

        tracing::info!(
            "缓存清理完成: {} 个条目, {} MB",
            stats.entry_count,
            stats.total_size / (1024 * 1024)
        );

        Ok(())
    }

    /// 定期清理任务
    pub async fn start_cleanup_task(&self) {
        let storage = self.storage.clone();
        let config = self.config.clone();
        let last_cleanup = self.last_cleanup.clone();

        tokio::spawn(async move {
            let mut interval =
                tokio::time::interval(Duration::from_secs(config.cleanup_interval_seconds));

            loop {
                interval.tick().await;

                let should_cleanup = {
                    let last = last_cleanup.read().await;
                    last.elapsed() >= Duration::from_secs(config.cleanup_interval_seconds)
                };

                if should_cleanup {
                    // 创建临时缓存实例进行清理
                    let temp_cache = IntelligentCache {
                        storage: storage.clone(),
                        config: config.clone(),
                        stats: Arc::new(RwLock::new(CacheStats::default())),
                        last_cleanup: last_cleanup.clone(),
                    };

                    if let Err(e) = temp_cache.intelligent_cleanup().await {
                        tracing::error!("缓存清理失败: {}", e);
                    }
                }
            }
        });
    }

    /// 获取缓存统计信息
    pub async fn get_stats(&self) -> CacheStats {
        self.stats.read().await.clone()
    }

    /// 清空所有缓存
    pub async fn clear_all(&self) {
        let mut storage = self.storage.write().await;
        let mut stats = self.stats.write().await;

        storage.clear();
        stats.entry_count = 0;
        stats.total_size = 0;
    }

    /// 获取当前时间戳
    fn current_timestamp(&self) -> u64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs()
    }

    /// 检查缓存键是否存在
    pub async fn contains_key(&self, key: &str) -> bool {
        let storage = self.storage.read().await;
        storage.contains_key(key)
    }

    /// 移除指定缓存条目
    pub async fn remove(&self, key: &str) -> bool {
        let mut storage = self.storage.write().await;
        let mut stats = self.stats.write().await;

        if storage.remove(key).is_some() {
            stats.entry_count = storage.len();
            stats.total_size = storage.values().map(|e| e.size).sum();
            true
        } else {
            false
        }
    }

    /// 获取缓存键列表
    pub async fn get_keys(&self) -> Vec<String> {
        let storage = self.storage.read().await;
        storage.keys().cloned().collect()
    }
}
