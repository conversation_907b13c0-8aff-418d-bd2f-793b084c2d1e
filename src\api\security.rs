// src/api/security.rs

// 安全相关 handler、结构体、路由注册函数将在此实现

use crate::types::AppState;
use axum::http::{header, Request};
use axum::middleware::Next;
use axum::{
    body::Body,
    extract::ConnectInfo,
    http::{HeaderValue, StatusCode},
    response::Response,
};
use base64::{engine::general_purpose, Engine};
use rand::{thread_rng, Rng};
#[cfg(feature = "security-extra")]
use sha2::{Digest, Sha256};
use std::net::SocketAddr;
use std::sync::Arc;

const CSRF_COOKIE_NAME: &str = "csrf-token";
const CSRF_HEADER_NAME: &str = "X-CSRF-Token";

// CSRF保护中间件
pub async fn csrf_protection_middleware(req: Request<Body>, next: Next) -> Response {
    // 跳过GET、HEAD、OPTIONS请求的CSRF检查
    let method = req.method().clone();

    if method == "GET" || method == "HEAD" || method == "OPTIONS" {
        return next.run(req).await;
    }

    // 获取CSRF令牌（从Cookie和请求头）
    let cookies = req.headers().get(header::COOKIE);
    let csrf_header = req.headers().get(CSRF_HEADER_NAME);

    let cookie_token = cookies.and_then(|cookie_value| {
        let cookie_str = cookie_value.to_str().unwrap_or_default();
        for cookie_part in cookie_str.split(';') {
            let trimmed = cookie_part.trim();
            if trimmed.starts_with(CSRF_COOKIE_NAME) {
                if let Some(token) = trimmed.strip_prefix(&format!("{}=", CSRF_COOKIE_NAME)) {
                    return Some(token.to_string());
                }
            }
        }
        None
    });

    let header_token = csrf_header.and_then(|v| v.to_str().ok().map(|s| s.to_string()));

    // 如果没有令牌或令牌不匹配，返回403响应
    if cookie_token.is_none() || header_token.is_none() || cookie_token != header_token {
        return match Response::builder()
            .status(StatusCode::FORBIDDEN)
            .header("Content-Type", "application/json")
            .body(Body::from("{\"error\":\"CSRF token validation failed\"}"))
        {
            Ok(response) => response,
            Err(_) => {
                // 如果响应构建失败，返回一个基本的403响应
                Response::builder()
                    .status(StatusCode::FORBIDDEN)
                    .body(Body::from("CSRF token validation failed"))
                    .unwrap_or_else(|_| Response::new(Body::from("Error")))
            }
        };
    }

    // 令牌匹配，继续处理请求
    next.run(req).await
}

// 生成CSRF令牌
pub fn generate_csrf_token() -> String {
    // 使用随机数和哈希算法生成令牌
    let mut rng = thread_rng();
    let random: [u8; 32] = rng.gen();
    #[cfg(feature = "security-extra")]
    let mut hasher = Sha256::new();
    #[cfg(not(feature = "security-extra"))]
    let mut hasher = std::collections::hash_map::DefaultHasher::new();
    hasher.update(random);
    general_purpose::STANDARD.encode(hasher.finalize())
}

// API速率限制中间件
pub async fn api_rate_limit_middleware(
    req: Request<Body>,
    state: Arc<AppState>,
    next: Next,
) -> Response {
    let client_ip = extract_client_ip(&req);

    // 简化的速率限制逻辑 - 实际应该使用配置中的限制
    // 这里只是占位符实现
    tracing::info!("Rate limit check for IP: {}", client_ip);

    next.run(req).await
}

// 安全响应头中间件
pub async fn security_headers_middleware(req: Request<Body>, next: Next) -> Response {
    // 处理请求
    let mut response = next.run(req).await;

    // 添加安全头
    let headers = response.headers_mut();
    headers.insert(
        header::X_XSS_PROTECTION,
        HeaderValue::from_static("1; mode=block"),
    );
    headers.insert(
        header::X_CONTENT_TYPE_OPTIONS,
        HeaderValue::from_static("nosniff"),
    );
    headers.insert(
        header::X_FRAME_OPTIONS,
        HeaderValue::from_static("SAMEORIGIN"),
    );

    // 添加CSP头
    headers.insert(
        header::CONTENT_SECURITY_POLICY,
        HeaderValue::from_static(
            "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:;"
        )
    );

    response
}

// 审计日志中间件
pub async fn audit_log_middleware(
    req: Request<Body>,
    state: Arc<AppState>,
    next: Next,
) -> Response {
    // 获取请求信息用于日志记录
    let method = req.method().clone();
    let path = req.uri().path().to_string();
    let client_ip = extract_client_ip(&req);

    // 记录请求开始
    let start = std::time::Instant::now();
    tracing::info!(
        target: "audit",
        method = %method,
        path = %path,
        client_ip = %client_ip,
        "请求开始"
    );

    // 处理请求
    let response = next.run(req).await;

    // 计算处理时间
    let duration = start.elapsed();

    // 记录请求完成
    tracing::info!(
        target: "audit",
        method = %method,
        path = %path,
        client_ip = %client_ip,
        status = %response.status().as_u16(),
        duration_ms = %duration.as_millis(),
        "请求完成"
    );

    response
}

// 从请求或连接信息中提取客户端IP
fn extract_client_ip(req: &Request<Body>) -> String {
    // 优先从请求头获取
    if let Some(forwarded) = req.headers().get("X-Forwarded-For") {
        if let Ok(ip_str) = forwarded.to_str() {
            // 只取第一个IP（最初的客户端）
            if let Some(first_ip) = ip_str.split(',').next() {
                return first_ip.trim().to_string();
            }
        }
    }

    if let Some(real_ip) = req.headers().get("X-Real-IP") {
        if let Ok(ip_str) = real_ip.to_str() {
            return ip_str.trim().to_string();
        }
    }

    // 如果请求中有ConnectInfo扩展数据，从中获取
    if let Some(conn_info) = req.extensions().get::<ConnectInfo<SocketAddr>>() {
        return conn_info.0.ip().to_string();
    }

    // 如果无法获取IP，使用未知标记
    "unknown".to_string()
}

// 路由注册入口
pub fn routes() -> axum::Router<Arc<AppState>> {
    axum::Router::new()
    // 可根据需要添加安全相关API路由
}
