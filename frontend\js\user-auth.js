/**
 * SM镜像系统用户认证模块
 * 处理用户登录、密码修改、会话管理
 */

class UserAuthManager {
    constructor() {
        this.currentUser = null;
        this.authToken = null;
        this.isFirstLogin = false;

        this.init();
    }

    init() {
        this.bindEvents();
        this.loadStoredAuth();
        this.startPeriodicSave();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 登录表单提交
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin();
            });
        }

        // 密码显示/隐藏切换
        const passwordToggle = document.getElementById('password-toggle');
        if (passwordToggle) {
            passwordToggle.addEventListener('click', () => {
                this.togglePasswordVisibility();
            });
        }

        // 修改密码表单
        const changePasswordForm = document.getElementById('change-password-form');
        if (changePasswordForm) {
            changePasswordForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleChangePassword();
            });
        }

        // 修改密码模态框控制
        this.bindModalEvents();
    }

    /**
     * 绑定模态框事件
     */
    bindModalEvents() {
        const modal = document.getElementById('change-password-modal');
        const closeBtn = document.getElementById('change-password-close');
        const cancelBtn = document.getElementById('change-password-cancel');

        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.hideChangePasswordModal();
            });
        }

        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.hideChangePasswordModal();
            });
        }

        // 点击模态框外部关闭（点击背景遮罩）
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.hideChangePasswordModal();
                }
            });
        }

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal && modal.style.display !== 'none') {
                this.hideChangePasswordModal();
            }
        });
    }

    /**
     * 处理用户登录
     */
    async handleLogin() {
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        const submitBtn = document.getElementById('login-submit-btn');

        if (!username || !password) {
            this.showNotification('请输入用户名和密码', 'error');
            return;
        }

        // 更新按钮状态
        const originalText = submitBtn.textContent;
        submitBtn.textContent = '🔄 登录中...';
        submitBtn.disabled = true;

        try {
            const response = await fetch('/api/public/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Real-IP': '127.0.0.1'
                },
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            });

            const data = await response.json();

            if (response.ok && data.success) {
                // 登录成功
                this.authToken = data.token;
                this.currentUser = data.user;
                this.isFirstLogin = data.user.is_first_login;

                // 保存认证信息
                this.storeAuth();

                // 显示成功消息
                this.showNotification(data.message || '登录成功！', 'success');

                // 检查是否首次登录
                if (this.isFirstLogin) {
                    // 首次登录，延迟显示修改密码界面
                    setTimeout(() => {
                        this.showChangePasswordModal(true);
                    }, 1500);
                } else {
                    // 非首次登录，直接切换到主界面
                    setTimeout(() => {
                        this.switchToMainInterface();
                    }, 800);
                }

            } else {
                throw new Error(data.error || '登录失败');
            }

        } catch (error) {
            console.error('登录错误:', error);
            this.showNotification(`登录失败: ${error.message}`, 'error');
        } finally {
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    }

    /**
     * 处理密码修改
     */
    async handleChangePassword() {
        const oldPassword = document.getElementById('old-password').value;
        const newPassword = document.getElementById('new-password').value;
        const confirmPassword = document.getElementById('confirm-password').value;
        const submitBtn = document.getElementById('change-password-submit');

        // 验证输入
        if (!oldPassword || !newPassword || !confirmPassword) {
            this.showNotification('请填写所有密码字段', 'error');
            return;
        }

        if (newPassword !== confirmPassword) {
            this.showNotification('新密码和确认密码不匹配', 'error');
            return;
        }

        if (newPassword.length < 6) {
            this.showNotification('新密码长度至少6位', 'error');
            return;
        }

        if (newPassword === 'admin' || newPassword === '123456' || newPassword === 'password') {
            this.showNotification('密码过于简单，请使用更复杂的密码', 'error');
            return;
        }

        // 更新按钮状态
        const originalText = submitBtn.textContent;
        submitBtn.textContent = '🔄 修改中...';
        submitBtn.disabled = true;

        try {
            const response = await fetch('/api/public/auth/change-password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authToken}`,
                    'X-Real-IP': '127.0.0.1'
                },
                body: JSON.stringify({
                    username: this.currentUser.username,
                    old_password: oldPassword,
                    new_password: newPassword
                })
            });

            const data = await response.json();

            if (response.ok && data.success) {
                // 密码修改成功
                this.isFirstLogin = false;
                this.currentUser.is_first_login = false;
                this.storeAuth();

                this.showNotification('密码修改成功！', 'success');
                this.hideChangePasswordModal();

                // 切换到主界面
                setTimeout(() => {
                    this.switchToMainInterface();
                }, 500);

            } else {
                throw new Error(data.error || '密码修改失败');
            }

        } catch (error) {
            console.error('密码修改错误:', error);
            this.showNotification(`密码修改失败: ${error.message}`, 'error');
        } finally {
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    }

    /**
     * 切换密码可见性
     */
    togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const toggleBtn = document.getElementById('password-toggle');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleBtn.textContent = '🙈';
        } else {
            passwordInput.type = 'password';
            toggleBtn.textContent = '👁️';
        }
    }

    /**
     * 显示修改密码模态框
     */
    showChangePasswordModal(isForced = false) {
        const modal = document.getElementById('change-password-modal');
        if (modal) {
            modal.style.display = 'flex';

            if (isForced) {
                this.showNotification('🔐 首次登录检测到，为了安全请立即修改默认密码', 'warning');
            }
        }
    }

    /**
     * 隐藏修改密码模态框
     */
    hideChangePasswordModal() {
        const modal = document.getElementById('change-password-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        if (window.App && window.App.showNotification) {
            window.App.showNotification(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    /**
     * 加载存储的认证信息
     */
    loadStoredAuth() {
        const token = localStorage.getItem('auth_token');
        const user = localStorage.getItem('auth_user');

        if (token && user) {
            try {
                this.authToken = token;
                this.currentUser = JSON.parse(user);
            } catch (error) {
                console.error('解析存储的用户信息失败:', error);
                this.clearAuth();
            }
        }
    }

    /**
     * 存储认证信息
     */
    storeAuth() {
        if (this.authToken && this.currentUser) {
            localStorage.setItem('auth_token', this.authToken);
            localStorage.setItem('auth_user', JSON.stringify(this.currentUser));
        }
    }

    /**
     * 清除认证信息
     */
    clearAuth() {
        this.authToken = null;
        this.currentUser = null;
        localStorage.removeItem('auth_token');
        localStorage.removeItem('auth_user');
    }

    /**
     * 切换到主界面
     */
    switchToMainInterface() {
        // 隐藏登录界面
        const loginScreen = document.getElementById('login-screen');
        if (loginScreen) {
            loginScreen.style.display = 'none';
        }

        // 显示主界面
        const mainApp = document.getElementById('main-app');
        if (mainApp) {
            mainApp.style.display = 'block';
        }

        console.log('✅ 已切换到主界面');
    }

    /**
     * 开始定期保存
     */
    startPeriodicSave() {
        setInterval(() => {
            if (this.authToken && this.currentUser) {
                this.storeAuth();
            }
        }, 30000); // 每30秒保存一次
    }
}

// 全局实例
window.userAuthManager = new UserAuthManager();