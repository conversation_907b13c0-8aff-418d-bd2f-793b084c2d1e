/**
 * SM镜像系统 - 主要控制器集合
 * 包含核心系统控制器和全局配置管理
 */

// ===== 全局配置管理 =====
class ConfigManager {
    constructor() {
        this.config = {
            apiEndpoint: '/api',
            refreshInterval: 30000,
            maxRetries: 3,
            timeout: 10000
        };
    }

    get(key) {
        return this.config[key];
    }

    set(key, value) {
        this.config[key] = value;
    }

    getApiUrl(endpoint) {
        return `${this.config.apiEndpoint}${endpoint}`;
    }
}

// ===== 通知系统 =====
class NotificationSystem {
    constructor() {
        this.container = null;
        this.init();
    }

    init() {
        this.createContainer();
    }

    createContainer() {
        this.container = document.getElementById('notification-container');
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'notification-container';
            document.body.appendChild(this.container);
        }
    }

    show(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        const id = 'notif_' + Date.now();
        notification.id = id;

        notification.innerHTML = `
            <div class="notification-header">
                <span class="notification-title">${this.getTitle(type)}</span>
                <button class="notification-close" onclick="notificationSystem.remove('${id}')">&times;</button>
            </div>
            <div class="notification-message">${message}</div>
        `;

        this.container.appendChild(notification);

        // 显示动画
        setTimeout(() => notification.classList.add('show'), 10);

        // 自动移除
        if (duration > 0) {
            setTimeout(() => this.remove(id), duration);
        }

        return id;
    }

    remove(id) {
        const notification = document.getElementById(id);
        if (notification) {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }
    }

    getTitle(type) {
        const titles = {
            success: '成功',
            error: '错误',
            warning: '警告',
            info: '信息'
        };
        return titles[type] || titles.info;
    }
}

// ===== 界面缩放控制器 =====
class ScaleController {
    constructor() {
        this.currentScale = 100;
        this.minScale = 90;
        this.maxScale = 120;
        this.step = 5;

        this.init();
    }

    init() {
        // 从本地存储加载缩放设置
        this.loadScale();

        // 绑定缩放控制按钮
        this.bindControls();

        // 应用初始缩放
        this.applyScale();
    }

    bindControls() {
        const scaleUpBtn = document.getElementById('scale-up');
        const scaleDownBtn = document.getElementById('scale-down');

        if (scaleUpBtn) {
            scaleUpBtn.addEventListener('click', () => this.scaleUp());
        }

        if (scaleDownBtn) {
            scaleDownBtn.addEventListener('click', () => this.scaleDown());
        }
    }

    scaleUp() {
        if (this.currentScale < this.maxScale) {
            this.currentScale += this.step;
            this.applyScale();
            this.saveScale();
        }
    }

    scaleDown() {
        if (this.currentScale > this.minScale) {
            this.currentScale -= this.step;
            this.applyScale();
            this.saveScale();
        }
    }

    applyScale() {
        const scaleContainer = document.querySelector('.scale-container');
        if (scaleContainer) {
            // 移除所有缩放类
            scaleContainer.className = scaleContainer.className.replace(/scale-\d+/g, '');
            // 添加当前缩放类
            scaleContainer.classList.add(`scale-${this.currentScale}`);
        }

        // 更新缩放指示器
        const indicator = document.getElementById('scale-indicator');
        if (indicator) {
            indicator.textContent = `${this.currentScale}%`;
        }
    }

    loadScale() {
        const savedScale = localStorage.getItem('sm_ui_scale');
        if (savedScale) {
            this.currentScale = parseInt(savedScale);
        }
    }

    saveScale() {
        localStorage.setItem('sm_ui_scale', this.currentScale.toString());
    }
}

// ===== 全屏控制器 =====
class FullscreenController {
    constructor() {
        this.isFullscreen = false;
        this.init();
    }

    init() {
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', () => this.toggle());
        }

        // 监听全屏状态变化
        document.addEventListener('fullscreenchange', () => this.handleFullscreenChange());
    }

    toggle() {
        if (!this.isFullscreen) {
            this.enterFullscreen();
        } else {
            this.exitFullscreen();
        }
    }

    enterFullscreen() {
        const element = document.documentElement;

        if (element.requestFullscreen) {
            element.requestFullscreen();
        } else if (element.mozRequestFullScreen) {
            element.mozRequestFullScreen();
        } else if (element.webkitRequestFullscreen) {
            element.webkitRequestFullscreen();
        } else if (element.msRequestFullscreen) {
            element.msRequestFullscreen();
        }
    }

    exitFullscreen() {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
        }
    }

    handleFullscreenChange() {
        this.isFullscreen = !!document.fullscreenElement;

        const btn = document.getElementById('fullscreen-btn');
        if (btn) {
            btn.textContent = this.isFullscreen ? '⛶' : '⛶';
            btn.title = this.isFullscreen ? '退出全屏' : '全屏';
        }
    }
}

// ===== 全局搜索控制器 =====
class GlobalSearch {
    constructor() {
        this.searchInput = null;
        this.searchBtn = null;
        this.init();
    }

    init() {
        this.searchInput = document.getElementById('global-search');
        this.searchBtn = document.querySelector('.search-btn');

        if (this.searchInput) {
            this.searchInput.addEventListener('input', this.debounce(() => this.handleSearch(), 300));
            this.searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.performSearch();
                }
            });
        }

        if (this.searchBtn) {
            this.searchBtn.addEventListener('click', () => this.performSearch());
        }
    }

    handleSearch() {
        const query = this.searchInput.value.trim();
        if (query.length > 0) {
            this.showSearchSuggestions(query);
        } else {
            this.hideSearchSuggestions();
        }
    }

    performSearch() {
        const query = this.searchInput.value.trim();
        if (query.length > 0) {
            this.searchContent(query);
        }
    }

    searchContent(query) {
        // 根据当前活跃的标签页进行搜索
        const activeSection = document.querySelector('.content-section.active');
        if (!activeSection) return;

        const sectionId = activeSection.id;

        switch (sectionId) {
            case 'domains':
                if (window.domainManager) {
                    const searchInput = document.getElementById('domain-search');
                    if (searchInput) {
                        searchInput.value = query;
                        window.domainManager.handleFilterChange();
                    }
                }
                break;
            case 'tasks':
                const taskSearch = document.getElementById('task-search');
                if (taskSearch) {
                    taskSearch.value = query;
                    // 触发任务搜索
                }
                break;
            case 'logs':
                const logSearch = document.getElementById('log-search');
                if (logSearch) {
                    logSearch.value = query;
                    // 触发日志搜索
                }
                break;
            default:
                // 显示搜索结果
                this.showSearchResults(query);
        }
    }

    showSearchSuggestions(query) {
        // TODO: 实现搜索建议
        console.log('显示搜索建议:', query);
    }

    hideSearchSuggestions() {
        // TODO: 隐藏搜索建议
    }

    showSearchResults(query) {
        // 显示全局搜索结果
        window.showNotification(`正在搜索: ${query}`, 'info');
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// ===== 标签页管理器 =====
class TabManager {
    constructor() {
        this.init();
    }

    init() {
        // 绑定所有tab切换事件
        this.bindTabEvents();
    }

    bindTabEvents() {
        // 域名池标签页
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-pool-tab]')) {
                this.switchPoolTab(e.target.dataset.poolTab);
            }
        });

        // 黑名单标签页
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-blacklist-tab]')) {
                this.switchBlacklistTab(e.target.dataset.blacklistTab);
            }
        });

        // 配置标签页
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-config-tab]')) {
                this.switchConfigTab(e.target.dataset.configTab);
            }
        });
    }

    switchPoolTab(tabName) {
        // 更新按钮状态
        document.querySelectorAll('[data-pool-tab]').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.poolTab === tabName);
        });

        // 更新内容区域
        document.querySelectorAll('.pool-tab-content').forEach(content => {
            content.classList.toggle('active', content.id === tabName);
        });
    }

    switchBlacklistTab(tabName) {
        // 更新按钮状态
        document.querySelectorAll('[data-blacklist-tab]').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.blacklistTab === tabName);
        });

        // 根据标签类型加载相应内容
        this.loadBlacklistContent(tabName);
    }

    switchConfigTab(tabName) {
        // 更新按钮状态
        document.querySelectorAll('[data-config-tab]').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.configTab === tabName);
        });

        // 加载配置内容
        this.loadConfigContent(tabName);
    }

    loadBlacklistContent(type) {
        const container = document.querySelector('.blacklist-content');
        if (!container) return;

        if (!window.authManager || !window.authManager.isAuthenticated()) {
            container.innerHTML = '<div class="auth-required">需要认证后查看黑名单</div>';
            return;
        }

        // 显示加载状态
        container.innerHTML = '<div class="loading-spinner">加载中...</div>';

        // 加载对应类型的黑名单数据
        fetch(`/api/blacklist?type=${type}`, {
            headers: {
                'Authorization': `Bearer ${window.authManager.getToken()}`
            }
        })
            .then(response => response.json())
            .then(data => {
                this.renderBlacklistContent(container, data.data || []);
            })
            .catch(error => {
                container.innerHTML = `<div class="error-message">加载失败: ${error.message}</div>`;
            });
    }

    renderBlacklistContent(container, data) {
        if (data.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">🚫</div>
                    <h3>暂无黑名单规则</h3>
                    <p>点击添加按钮创建第一个黑名单规则</p>
                </div>
            `;
            return;
        }

        let html = `
            <div class="data-table">
                <table>
                    <thead>
                        <tr>
                            <th>规则</th>
                            <th>类型</th>
                            <th>创建时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        data.forEach(item => {
            html += `
                <tr>
                    <td>${item.rule}</td>
                    <td><span class="type-badge">${item.type}</span></td>
                    <td>${new Date(item.created_at).toLocaleString()}</td>
                    <td><span class="status-badge ${item.enabled ? 'status-success' : 'status-warning'}">${item.enabled ? '启用' : '禁用'}</span></td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-outline" onclick="editBlacklistRule('${item.id}')" title="编辑">✏️</button>
                            <button class="btn btn-sm btn-outline" onclick="deleteBlacklistRule('${item.id}')" title="删除">🗑️</button>
                        </div>
                    </td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = html;
    }

    loadConfigContent(section) {
        const container = document.querySelector('.config-content');
        if (!container) return;

        if (!window.authManager || !window.authManager.isAuthenticated()) {
            container.innerHTML = '<div class="auth-required">需要认证后查看系统配置</div>';
            return;
        }

        // 显示加载状态
        container.innerHTML = '<div class="loading-spinner">加载配置...</div>';

        // 加载配置数据
        fetch(`/api/config/${section}`, {
            headers: {
                'Authorization': `Bearer ${window.authManager.getToken()}`
            }
        })
            .then(response => response.json())
            .then(data => {
                this.renderConfigContent(container, section, data.data || {});
            })
            .catch(error => {
                container.innerHTML = `<div class="error-message">加载配置失败: ${error.message}</div>`;
            });
    }

    renderConfigContent(container, section, config) {
        // 根据不同配置段渲染不同的表单
        const forms = {
            general: this.renderGeneralConfig,
            proxy: this.renderProxyConfig,
            cache: this.renderCacheConfig,
            security: this.renderSecurityConfig,
            performance: this.renderPerformanceConfig
        };

        const renderFunc = forms[section] || this.renderDefaultConfig;
        container.innerHTML = renderFunc(config);
    }

    renderGeneralConfig(config) {
        return `
            <form id="general-config-form">
                <div class="form-section">
                    <h4>基本设置</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="server-name">服务器名称</label>
                            <input type="text" id="server-name" name="server_name" value="${config.server_name || ''}" placeholder="SM镜像系统">
                        </div>
                        <div class="form-group">
                            <label for="listen-port">监听端口</label>
                            <input type="number" id="listen-port" name="listen_port" value="${config.listen_port || 8080}" min="1" max="65535">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="worker-threads">工作线程数</label>
                            <input type="number" id="worker-threads" name="worker_threads" value="${config.worker_threads || 4}" min="1" max="32">
                        </div>
                        <div class="form-group">
                            <label for="max-connections">最大连接数</label>
                            <input type="number" id="max-connections" name="max_connections" value="${config.max_connections || 1000}" min="100">
                        </div>
                    </div>
                </div>
            </form>
        `;
    }

    renderProxyConfig(config) {
        return `
            <form id="proxy-config-form">
                <div class="form-section">
                    <h4>代理设置</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="connect-timeout">连接超时(秒)</label>
                            <input type="number" id="connect-timeout" name="connect_timeout" value="${config.connect_timeout || 30}" min="1" max="300">
                        </div>
                        <div class="form-group">
                            <label for="read-timeout">读取超时(秒)</label>
                            <input type="number" id="read-timeout" name="read_timeout" value="${config.read_timeout || 60}" min="1" max="600">
                        </div>
                    </div>
                </div>
            </form>
        `;
    }

    renderCacheConfig(config) {
        return `
            <form id="cache-config-form">
                <div class="form-section">
                    <h4>缓存设置</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="cache-size">缓存大小(MB)</label>
                            <input type="number" id="cache-size" name="cache_size" value="${config.cache_size || 1024}" min="64">
                        </div>
                        <div class="form-group">
                            <label for="cache-ttl">缓存TTL(秒)</label>
                            <input type="number" id="cache-ttl" name="cache_ttl" value="${config.cache_ttl || 3600}" min="60">
                        </div>
                    </div>
                </div>
            </form>
        `;
    }

    renderSecurityConfig(config) {
        return `
            <form id="security-config-form">
                <div class="form-section">
                    <h4>安全设置</h4>
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="enable_rate_limit" ${config.enable_rate_limit ? 'checked' : ''}>
                            <span class="checkmark"></span>
                            启用速率限制
                        </label>
                    </div>
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="enable_ip_whitelist" ${config.enable_ip_whitelist ? 'checked' : ''}>
                            <span class="checkmark"></span>
                            启用IP白名单
                        </label>
                    </div>
                </div>
            </form>
        `;
    }

    renderPerformanceConfig(config) {
        return `
            <form id="performance-config-form">
                <div class="form-section">
                    <h4>性能调优</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="buffer-size">缓冲区大小(KB)</label>
                            <input type="number" id="buffer-size" name="buffer_size" value="${config.buffer_size || 64}" min="8" max="1024">
                        </div>
                        <div class="form-group">
                            <label for="keepalive-timeout">Keep-Alive超时(秒)</label>
                            <input type="number" id="keepalive-timeout" name="keepalive_timeout" value="${config.keepalive_timeout || 15}" min="1" max="300">
                        </div>
                    </div>
                </div>
            </form>
        `;
    }

    renderDefaultConfig(config) {
        return `
            <div class="config-placeholder">
                <p>该配置段暂未实现</p>
                <pre>${JSON.stringify(config, null, 2)}</pre>
            </div>
        `;
    }
}

// ===== 主应用控制器 =====
class MainController {
    constructor() {
        this.configManager = new ConfigManager();
        this.notificationSystem = new NotificationSystem();
        this.scaleController = new ScaleController();
        this.fullscreenController = new FullscreenController();
        this.globalSearch = new GlobalSearch();
        this.tabManager = new TabManager();

        this.init();
    }

    init() {
        console.log('🎛️ 主控制器初始化完成');

        // 初始化所有子控制器
        this.initializeControllers();

        // 绑定全局事件
        this.bindGlobalEvents();
    }

    initializeControllers() {
        // 各个控制器的初始化逻辑已经在构造函数中完成
    }

    bindGlobalEvents() {
        // 全局键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'f':
                        e.preventDefault();
                        this.globalSearch.focus();
                        break;
                    case 'Enter':
                        if (e.shiftKey) {
                            e.preventDefault();
                            this.fullscreenController.toggle();
                        }
                        break;
                }
            }
        });
    }

    // 获取配置
    getConfig(key) {
        return this.configManager.get(key);
    }

    // 显示通知
    showNotification(message, type = 'info', duration = 3000) {
        return this.notificationSystem.show(message, type, duration);
    }
}

// ===== 日志管理器 =====
class LogManager {
    constructor() {
        this.logContainer = null;
        this.autoRefresh = true;
        this.refreshInterval = null;
        this.isPaused = false;

        this.init();
    }

    init() {
        this.logContainer = document.getElementById('log-container');
        this.bindEvents();

        // 监听标签切换到日志页面
        document.addEventListener('tab-change', (e) => {
            if (e.detail.tab === 'logs') {
                this.startAutoRefresh();
            } else {
                this.stopAutoRefresh();
            }
        });
    }

    bindEvents() {
        // 清空日志按钮
        const clearBtn = document.getElementById('clear-logs');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearLogs());
        }

        // 暂停/继续按钮
        const pauseBtn = document.getElementById('pause-logs');
        if (pauseBtn) {
            pauseBtn.addEventListener('click', () => this.togglePause());
        }

        // 自动刷新复选框
        const autoRefreshCheck = document.getElementById('log-auto-refresh');
        if (autoRefreshCheck) {
            autoRefreshCheck.addEventListener('change', (e) => {
                this.autoRefresh = e.target.checked;
                if (this.autoRefresh && !this.isPaused) {
                    this.startAutoRefresh();
                } else {
                    this.stopAutoRefresh();
                }
            });
        }

        // 日志级别过滤
        const levelFilter = document.getElementById('log-level-filter');
        if (levelFilter) {
            levelFilter.addEventListener('change', () => this.filterLogs());
        }

        // 日志搜索
        const searchInput = document.getElementById('log-search');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce(() => this.filterLogs(), 300));
        }
    }

    startAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        if (this.autoRefresh && !this.isPaused) {
            this.refreshInterval = setInterval(() => {
                this.loadLogs();
            }, 2000); // 每2秒刷新一次
        }
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    togglePause() {
        this.isPaused = !this.isPaused;
        const btn = document.getElementById('pause-logs');

        if (btn) {
            btn.textContent = this.isPaused ? '▶️ 继续更新' : '⏸️ 暂停更新';
        }

        if (this.isPaused) {
            this.stopAutoRefresh();
        } else if (this.autoRefresh) {
            this.startAutoRefresh();
        }
    }

    async loadLogs() {
        if (!window.authManager || !window.authManager.isAuthenticated()) {
            return;
        }

        try {
            const response = await fetch('/api/logs', {
                headers: {
                    'Authorization': `Bearer ${window.authManager.getToken()}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.renderLogs(data.data || []);
            }
        } catch (error) {
            console.error('加载日志失败:', error);
        }
    }

    renderLogs(logs) {
        if (!this.logContainer) return;

        // 保存滚动位置
        const wasAtBottom = this.logContainer.scrollTop >= this.logContainer.scrollHeight - this.logContainer.clientHeight - 50;

        // 清空现有日志
        this.logContainer.innerHTML = '';

        // 渲染新日志
        logs.forEach(log => {
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <span class="log-time">${this.formatTime(log.timestamp)}</span>
                <span class="log-level log-${log.level.toLowerCase()}">${log.level}</span>
                <span class="log-message">${this.escapeHtml(log.message)}</span>
            `;
            this.logContainer.appendChild(logEntry);
        });

        // 如果之前在底部，自动滚动到底部
        if (wasAtBottom) {
            this.logContainer.scrollTop = this.logContainer.scrollHeight;
        }
    }

    filterLogs() {
        // 实现日志过滤逻辑
        const levelFilter = document.getElementById('log-level-filter');
        const searchInput = document.getElementById('log-search');

        const filterLevel = levelFilter ? levelFilter.value : '';
        const searchText = searchInput ? searchInput.value.toLowerCase() : '';

        const entries = this.logContainer.querySelectorAll('.log-entry');

        entries.forEach(entry => {
            const level = entry.querySelector('.log-level').textContent.toLowerCase();
            const message = entry.querySelector('.log-message').textContent.toLowerCase();

            const levelMatch = !filterLevel || level === filterLevel;
            const textMatch = !searchText || message.includes(searchText);

            entry.style.display = (levelMatch && textMatch) ? 'flex' : 'none';
        });
    }

    clearLogs() {
        if (confirm('确定要清空所有日志吗？此操作不可恢复。')) {
            if (this.logContainer) {
                this.logContainer.innerHTML = '';
            }
            window.showNotification('日志已清空', 'info');
        }
    }

    formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleString();
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// ===== 全局初始化 =====
document.addEventListener('DOMContentLoaded', () => {
    // 创建主控制器实例
    window.mainController = new MainController();

    // 创建独立的日志管理器
    window.logManager = new LogManager();

    console.log('📋 所有主要控制器初始化完成');
});

// ===== 全局工具函数 =====
window.MainUtils = {
    formatDateTime: function (timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    },

    formatBytes: function (bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    },

    debounce: function (func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

// ===== 向后兼容的全局函数 =====
window.showNotification = function (message, type = 'info', duration = 3000) {
    if (window.mainController) {
        return window.mainController.showNotification(message, type, duration);
    }
};
