//! 简化的认证模块
//!
//! 提供JWT认证、密码哈希、用户管理等功能

use anyhow::Result;
use bcrypt::{hash, verify, DEFAULT_COST};
use chrono::{Duration, Utc};
use jsonwebtoken::{decode, encode, Header, Validation};
use std::sync::Arc;

use axum::{
    extract::{Request, State},
    http::{HeaderMap, StatusCode},
    middleware::Next,
    response::Response,
};
use serde::{Deserialize, Serialize};

/// JWT 密钥管理 - 安全版本
#[derive(Clone, serde::Serialize, serde::Deserialize)]
pub struct SecureJwtSecret {
    secret: Vec<u8>,
    created_at: chrono::DateTime<chrono::Utc>,
    expires_at: chrono::DateTime<chrono::Utc>,
}

impl SecureJwtSecret {
    /// 检查密钥是否过期
    pub fn is_expired(&self) -> bool {
        chrono::Utc::now() > self.expires_at
    }

    /// 获取密钥字节
    pub fn as_bytes(&self) -> &[u8] {
        &self.secret
    }

    /// 获取编码密钥
    pub fn encoding_key(&self) -> jsonwebtoken::EncodingKey {
        jsonwebtoken::EncodingKey::from_secret(&self.secret)
    }

    /// 获取解码密钥
    pub fn decoding_key(&self) -> jsonwebtoken::DecodingKey {
        jsonwebtoken::DecodingKey::from_secret(&self.secret)
    }
}

/// JWT Claims结构
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Claims {
    pub sub: String,    // 用户名
    pub role: String,   // 用户角色
    pub exp: i64,       // 过期时间
    pub iat: i64,       // 签发时间
    pub is_admin: bool, // 是否管理员
}

/// 认证服务
#[derive(Clone)]
pub struct AuthService {
    database: Arc<dyn crate::types::Database>,
    jwt_secret: SecureJwtSecret,
}

impl AuthService {
    /// 创建认证服务
    pub fn new(database: Arc<dyn crate::types::Database>) -> Self {
        let jwt_secret = Self::generate_or_load_jwt_secret();

        Self {
            database,
            jwt_secret,
        }
    }

    /// 生成或加载 JWT 密钥
    fn generate_or_load_jwt_secret() -> SecureJwtSecret {
        // 尝试从安全存储加载密钥
        if let Ok(secret) = Self::load_jwt_secret_from_file() {
            if !secret.is_expired() {
                return secret;
            } else {
                tracing::warn!("JWT 密钥已过期，生成新密钥");
            }
        }

        // 生成新的安全密钥
        let secret = Self::generate_secure_jwt_secret();

        // 保存到安全存储
        if let Err(e) = Self::save_jwt_secret_to_file(&secret) {
            tracing::error!("保存 JWT 密钥失败: {}", e);
        }

        secret
    }

    /// 生成安全的 JWT 密钥
    fn generate_secure_jwt_secret() -> SecureJwtSecret {
        use rand::RngCore;

        let mut secret = vec![0u8; 64]; // 512位密钥
        rand::thread_rng().fill_bytes(&mut secret);

        let now = chrono::Utc::now();

        SecureJwtSecret {
            secret,
            created_at: now,
            expires_at: now + chrono::Duration::days(30), // 30天后过期
        }
    }

    /// 从文件加载 JWT 密钥
    fn load_jwt_secret_from_file() -> Result<SecureJwtSecret> {
        use std::fs;

        let key_file = "./keys/jwt_secret.key";

        if !std::path::Path::new(key_file).exists() {
            return Err(anyhow::anyhow!("JWT 密钥文件不存在"));
        }

        let content = fs::read(key_file)?;
        let secret: SecureJwtSecret = serde_json::from_slice(&content)?;

        Ok(secret)
    }

    /// 保存 JWT 密钥到文件
    fn save_jwt_secret_to_file(secret: &SecureJwtSecret) -> Result<()> {
        use std::fs;

        // 使用当前工作目录下的keys目录，避免权限问题
        let keys_dir = "./keys";
        fs::create_dir_all(keys_dir)?;

        let key_file = "./keys/jwt_secret.key";
        let content = serde_json::to_vec_pretty(secret)?;

        fs::write(key_file, content)?;

        // 设置安全权限 (仅所有者可读写)
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let mut perms = fs::metadata(key_file)?.permissions();
            perms.set_mode(0o600);
            fs::set_permissions(key_file, perms)?;
        }

        tracing::info!("JWT 密钥已安全保存到: {}", key_file);
        Ok(())
    }

    /// 用户登录
    pub async fn login(&self, username: &str, password: &str) -> Result<String> {
        // 从数据库获取用户
        let user = self
            .database
            .get_user_by_username(username)
            .await
            .map_err(|e| anyhow::anyhow!("数据库查询失败: {:?}", e))?;

        let user = user.ok_or_else(|| anyhow::anyhow!("用户不存在"))?;

        // 验证密码
        if !verify(password, &user.password_hash)? {
            return Err(anyhow::anyhow!("密码错误"));
        }

        // 更新最后登录时间
        let _ = self.database.update_last_login(username).await;

        // 生成JWT token
        let token = self.generate_token(username, &user.role)?;
        Ok(token)
    }

    /// 生成JWT token
    pub fn generate_token(&self, username: &str, role: &str) -> Result<String> {
        let now = Utc::now();
        let exp = now + Duration::hours(24); // 24小时过期

        let claims = Claims {
            sub: username.to_string(),
            role: role.to_string(),
            exp: exp.timestamp(),
            iat: now.timestamp(),
            is_admin: role == "admin",
        };

        let token = encode(&Header::default(), &claims, &self.jwt_secret.encoding_key())?;

        Ok(token)
    }

    /// 验证JWT token
    pub async fn verify_token(&self, token: &str) -> Result<Claims> {
        let token_data = decode::<Claims>(
            token,
            &self.jwt_secret.decoding_key(),
            &Validation::default(),
        )?;

        Ok(token_data.claims)
    }

    /// 哈希密码
    pub fn hash_password(&self, password: &str) -> Result<String> {
        let hashed = hash(password, DEFAULT_COST)?;
        Ok(hashed)
    }

    /// 修改密码
    pub async fn change_password(
        &self,
        username: &str,
        old_password: &str,
        new_password: &str,
    ) -> Result<()> {
        // 验证旧密码
        let user = self
            .database
            .get_user_by_username(username)
            .await
            .map_err(|e| anyhow::anyhow!("数据库查询失败: {:?}", e))?;

        let user = user.ok_or_else(|| anyhow::anyhow!("用户不存在"))?;

        if !verify(old_password, &user.password_hash)? {
            return Err(anyhow::anyhow!("旧密码错误"));
        }

        // 哈希新密码
        let new_hash = self.hash_password(new_password)?;

        // 更新数据库
        self.database
            .update_user_password(username, &new_hash, false)
            .await
            .map_err(|e| anyhow::anyhow!("密码更新失败: {:?}", e))?;

        Ok(())
    }

    /// 创建安全的管理员用户
    pub async fn ensure_admin_user(&self) -> Result<()> {
        // 检查是否已有管理员用户
        let admin_count = self
            .database
            .count_admin_users()
            .await
            .map_err(|e| anyhow::anyhow!("查询管理员用户失败: {:?}", e))?;

        if admin_count == 0 {
            // 生成安全的临时密码
            let temp_password = self.generate_secure_temp_password();

            // 创建安全的管理员用户
            self.database
                .create_secure_admin_user(&temp_password)
                .await
                .map_err(|e| anyhow::anyhow!("创建管理员失败: {:?}", e))?;

            // 安全地记录临时密码
            tracing::warn!(
                "🔐 管理员账户已创建。临时密码: {} (请立即登录并修改密码)",
                temp_password
            );

            // 将临时密码写入安全文件
            if let Err(e) = self.save_temp_password_securely(&temp_password).await {
                tracing::error!("保存临时密码失败: {}", e);
            }
        }

        Ok(())
    }

    /// 生成安全的临时密码
    fn generate_secure_temp_password(&self) -> String {
        use rand::Rng;
        const CHARSET: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZ\
                                abcdefghijklmnopqrstuvwxyz\
                                0123456789!@#$%^&*";
        let mut rng = rand::thread_rng();

        (0..16)
            .map(|_| {
                let idx = rng.gen_range(0..CHARSET.len());
                CHARSET[idx] as char
            })
            .collect()
    }

    /// 安全地保存临时密码
    async fn save_temp_password_securely(&self, password: &str) -> Result<()> {
        use std::fs;

        // 使用当前目录下的临时文件，避免权限问题
        let temp_file = "./sm_admin_temp_password.txt";
        let content = format!(
            "SM 管理员临时密码\n生成时间: {}\n用户名: admin\n密码: {}\n\n⚠️ 请立即登录并修改密码，然后删除此文件！\n",
            Utc::now().format("%Y-%m-%d %H:%M:%S UTC"),
            password
        );

        fs::write(temp_file, content)?;

        // 在Unix系统上设置安全权限
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let mut perms = fs::metadata(temp_file)?.permissions();
            perms.set_mode(0o600); // 仅所有者可读写
            fs::set_permissions(temp_file, perms)?;
        }

        tracing::info!("临时密码已保存到: {} (仅所有者可读)", temp_file);
        Ok(())
    }
}

/// 登录请求
#[derive(Debug, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

/// 登录响应
#[derive(Debug, Serialize)]
pub struct LoginResponse {
    pub token: String,
    pub username: String,
    pub role: String,
    pub expires_in: i64,
}

/// 修改密码请求
#[derive(Debug, Deserialize)]
pub struct ChangePasswordRequest {
    pub old_password: String,
    pub new_password: String,
}

/// JWT认证中间件 - 强制执行认证
pub async fn jwt_auth_middleware(
    State(auth_service): State<AuthService>,
    mut request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // 获取 Authorization header
    let headers = request.headers();
    if let Some(auth_header) = headers.get("authorization") {
        if let Ok(auth_str) = auth_header.to_str() {
            if auth_str.starts_with("Bearer ") {
                let token = &auth_str[7..];
                match auth_service.verify_token(token).await {
                    Ok(claims) => {
                        // 在请求中插入用户信息
                        request.extensions_mut().insert(claims);
                        return Ok(next.run(request).await);
                    }
                    Err(_) => {
                        return Err(StatusCode::UNAUTHORIZED);
                    }
                }
            }
        }
    }

    // 检查是否为登录或静态资源请求
    let path = request.uri().path();
    if path.starts_with("/api/auth/login") || path.starts_with("/static/") || path == "/" {
        return Ok(next.run(request).await);
    }

    Err(StatusCode::UNAUTHORIZED)
}

/// 管理员权限中间件
pub async fn admin_auth_middleware(request: Request, next: Next) -> Result<Response, StatusCode> {
    // 从请求扩展中获取用户信息
    let user_claims = request
        .extensions()
        .get::<Claims>()
        .ok_or(StatusCode::UNAUTHORIZED)?;

    // 检查管理员权限
    if !user_claims.is_admin {
        return Err(StatusCode::FORBIDDEN);
    }

    Ok(next.run(request).await)
}

/// 获取客户端IP地址
pub fn get_client_ip(headers: &HeaderMap) -> String {
    // 按优先级检查各种代理头
    if let Some(forwarded_for) = headers.get("x-forwarded-for") {
        if let Ok(forwarded_str) = forwarded_for.to_str() {
            return forwarded_str
                .split(',')
                .next()
                .map(|s| s.trim().to_string())
                .unwrap_or_else(|| "unknown".to_string());
        }
    }

    if let Some(real_ip) = headers.get("x-real-ip") {
        if let Ok(ip_str) = real_ip.to_str() {
            return ip_str.to_string();
        }
    }

    "unknown".to_string()
}
