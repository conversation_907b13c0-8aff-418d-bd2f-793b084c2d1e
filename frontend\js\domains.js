/**
 * 域名管理器
 * 负责域名的增删改查、批量操作、健康检查等功能
 */
class DomainManager {
    constructor() {
        this.domains = [];
        this.filteredDomains = [];
        this.currentFilters = {
            status: '',
            type: '',
            search: ''
        };

        this.init();
    }

    init() {
        // 监听认证状态变化
        document.addEventListener('authenticated', () => {
            this.loadDomains();
        });

        document.addEventListener('logout', () => {
            this.resetDomains();
        });

        // 监听标签切换
        document.addEventListener('tab-change', (e) => {
            if (e.detail.tab === 'domains') {
                this.refreshDomains();
            }
        });

        // 绑定事件
        this.bindEvents();

        // 如果已经认证，立即加载数据
        if (window.authManager && window.authManager.isAuthenticated()) {
            this.loadDomains();
        }
    }

    bindEvents() {
        // 监听自定义事件来显示模态框
        window.addEventListener('show-modal', (e) => {
            const { modalType } = e.detail;
            switch (modalType) {
                case 'add-domain':
                    this.showAddDomainModal();
                    break;
                case 'batch-add':
                    this.showImportModal();
                    break;
                default:
                    console.log(`未处理的模态框类型: ${modalType}`);
            }
        });

        // 过滤器事件
        const statusFilter = document.getElementById('domain-status-filter');
        const typeFilter = document.getElementById('domain-type-filter');
        const searchInput = document.getElementById('domain-search');
        const clearFiltersBtn = document.getElementById('clear-filters');

        if (statusFilter) {
            statusFilter.addEventListener('change', () => this.handleFilterChange());
        }

        if (typeFilter) {
            typeFilter.addEventListener('change', () => this.handleFilterChange());
        }

        if (searchInput) {
            const debouncedFilter = window.MainUtils ?
                window.MainUtils.debounce(() => this.handleFilterChange(), 300) :
                (() => this.handleFilterChange()); // 回退方案
            searchInput.addEventListener('input', debouncedFilter);
        }

        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => this.clearFilters());
        }

        // 按钮事件
        this.bindActionButtons();

        // 模态框事件
        this.bindModalEvents();

        // 绑定批量添加确认按钮
        const confirmBatchBtn = document.getElementById('confirm-batch-add');
        if (confirmBatchBtn) {
            confirmBatchBtn.addEventListener('click', () => {
                this.handleBatchAdd();
            });
        }
    }

    bindActionButtons() {
        console.log('🔗 绑定域名管理按钮事件');

        // 由于ComponentManager已经处理data-modal属性，我们不需要重复绑定
        // 只需要绑定其他自定义按钮

        // 导出按钮
        const exportBtns = document.querySelectorAll('[data-action="export-domains"]');
        exportBtns.forEach(btn => {
            btn.addEventListener('click', () => this.exportDomains());
        });

        // 立即登录按钮
        const promptLoginBtn = document.getElementById('prompt-login');
        if (promptLoginBtn) {
            promptLoginBtn.addEventListener('click', () => this.showLoginModal());
        }

        console.log('✅ 域名管理按钮事件绑定完成（使用ComponentManager处理模态框）');
    }

    bindModalEvents() {
        // 监听模态框显示事件 - 但避免与 bindEvents 中的监听重复
        // 这里可以处理其他特定的模态框事件

        // 处理模态框关闭事件
        document.addEventListener('close-modal', (e) => {
            const { modalId } = e.detail;
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.remove();
            }
        });

        // 处理模态框确认事件
        document.addEventListener('modal-confirm', (e) => {
            const { modalType, data } = e.detail;
            switch (modalType) {
                case 'delete-domain':
                    this.deleteDomain(data.domainId);
                    break;
                case 'batch-delete':
                    this.performBatchDelete(data.domainIds);
                    break;
            }
        });
    }

    async loadDomains() {
        try {
            const response = await fetch('/api/domains', {
                headers: {
                    'Authorization': `Bearer ${window.authManager.getToken()}`
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();
            this.domains = data.data || data.domains || [];
            this.applyFilters();
            this.renderDomainTable();

        } catch (error) {
            console.error('加载域名列表失败:', error);
            this.showError('加载域名列表失败');
        }
    }

    refreshDomains() {
        if (window.authManager && window.authManager.isAuthenticated()) {
            this.loadDomains();
        }
    }

    resetDomains() {
        this.domains = [];
        this.filteredDomains = [];
        this.renderDomainTable();
    }

    handleFilterChange() {
        // 获取当前过滤器值
        const statusFilter = document.getElementById('domain-status-filter');
        const typeFilter = document.getElementById('domain-type-filter');
        const searchInput = document.getElementById('domain-search');

        this.currentFilters = {
            status: statusFilter ? statusFilter.value : '',
            type: typeFilter ? typeFilter.value : '',
            search: searchInput ? searchInput.value.toLowerCase() : ''
        };

        this.applyFilters();
        this.renderDomainTable();
    }

    applyFilters() {
        this.filteredDomains = this.domains.filter(domain => {
            // 状态过滤
            if (this.currentFilters.status && domain.status !== this.currentFilters.status) {
                return false;
            }

            // 类型过滤
            if (this.currentFilters.type && domain.type !== this.currentFilters.type) {
                return false;
            }

            // 搜索过滤
            if (this.currentFilters.search) {
                const searchText = this.currentFilters.search;
                return (
                    domain.domain.toLowerCase().includes(searchText) ||
                    domain.upstream?.toLowerCase().includes(searchText) ||
                    domain.description?.toLowerCase().includes(searchText)
                );
            }

            return true;
        });
    }

    clearFilters() {
        // 重置过滤器
        const statusFilter = document.getElementById('domain-status-filter');
        const typeFilter = document.getElementById('domain-type-filter');
        const searchInput = document.getElementById('domain-search');

        if (statusFilter) statusFilter.value = '';
        if (typeFilter) typeFilter.value = '';
        if (searchInput) searchInput.value = '';

        this.currentFilters = { status: '', type: '', search: '' };
        this.applyFilters();
        this.renderDomainTable();
    }

    renderDomainTable() {
        const container = document.getElementById('domain-table');
        if (!container) return;

        if (!window.authManager || !window.authManager.isAuthenticated()) {
            container.innerHTML = `
                <div class="auth-required">
                    <div class="auth-prompt">
                        <div class="auth-icon">🔐</div>
                        <p>请先登录以查看域名列表</p>
                        <button class="btn btn-primary" onclick="window.authManager.showLoginModal()">立即登录</button>
                    </div>
                </div>
            `;
            return;
        }

        if (this.filteredDomains.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">🌐</div>
                    <h3>暂无域名</h3>
                    <p>您还没有添加任何域名，点击下方按钮开始添加</p>
                    <button class="btn btn-primary" onclick="domainManager.showAddDomainModal()">
                        ➕ 添加域名
                    </button>
                </div>
            `;
            return;
        }

        // 构建表格
        let html = `
            <div class="table-header">
                <div class="table-actions">
                    <span class="result-count">共 ${this.filteredDomains.length} 个域名</span>
                    <div class="table-controls">
                        <button class="btn btn-sm btn-outline" onclick="domainManager.selectAll()">全选</button>
                        <button class="btn btn-sm btn-outline" onclick="domainManager.batchDelete()">批量删除</button>
                    </div>
                </div>
            </div>
            <div class="data-table">
                <table>
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="select-all-domains"></th>
                            <th>域名</th>
                            <th>状态</th>
                            <th>类型</th>
                            <th>上游服务</th>
                            <th>最后检查</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        this.filteredDomains.forEach(domain => {
            html += this.renderDomainRow(domain);
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = html;

        // 绑定表格事件
        this.bindTableEvents();
    }

    renderDomainRow(domain) {
        const statusClass = this.getStatusClass(domain.status);
        const statusText = this.getStatusText(domain.status);

        return `
            <tr data-domain-id="${domain.id || domain.domain}">
                <td><input type="checkbox" class="domain-checkbox" value="${domain.id || domain.domain}"></td>
                <td>
                    <div class="domain-info">
                        <div class="domain-name">${domain.domain}</div>
                        ${domain.description ? `<div class="domain-desc">${domain.description}</div>` : ''}
                    </div>
                </td>
                <td>
                    <span class="status-badge ${statusClass}">${statusText}</span>
                </td>
                <td>
                    <span class="type-badge">${this.getTypeText(domain.type)}</span>
                </td>
                <td>
                    <span class="upstream-info">${domain.upstream || '-'}</span>
                </td>
                <td>
                    <span class="last-check">${this.formatLastCheck(domain.last_check)}</span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-outline" onclick="domainManager.testDomain('${domain.domain}')" title="测试连通性">🧪</button>
                        <button class="btn btn-sm btn-outline" onclick="domainManager.editDomain('${domain.id || domain.domain}')" title="编辑">✏️</button>
                        <button class="btn btn-sm btn-outline" onclick="domainManager.deleteDomain('${domain.id || domain.domain}')" title="删除">🗑️</button>
                    </div>
                </td>
            </tr>
        `;
    }

    bindTableEvents() {
        // 全选复选框
        const selectAllCheckbox = document.getElementById('select-all-domains');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                const checkboxes = document.querySelectorAll('.domain-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = e.target.checked;
                });
            });
        }

        // 单个复选框
        const checkboxes = document.querySelectorAll('.domain-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateSelectAllState();
            });
        });
    }

    updateSelectAllState() {
        const selectAllCheckbox = document.getElementById('select-all-domains');
        const checkboxes = document.querySelectorAll('.domain-checkbox');
        const checkedBoxes = document.querySelectorAll('.domain-checkbox:checked');

        if (selectAllCheckbox) {
            selectAllCheckbox.checked = checkedBoxes.length === checkboxes.length;
            selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < checkboxes.length;
        }
    }

    getStatusClass(status) {
        switch (status) {
            case 'active': return 'status-success';
            case 'inactive': return 'status-warning';
            case 'error': return 'status-error';
            default: return 'status-unknown';
        }
    }

    getStatusText(status) {
        switch (status) {
            case 'active': return '活跃';
            case 'inactive': return '停用';
            case 'error': return '异常';
            default: return '未知';
        }
    }

    getTypeText(type) {
        switch (type) {
            case 'downstream': return '下游域名';
            case 'upstream': return '上游域名';
            default: return '普通域名';
        }
    }

    formatLastCheck(timestamp) {
        if (!timestamp) return '从未检查';

        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;

        if (diff < 60000) return '刚刚';
        if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
        if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
        return date.toLocaleDateString();
    }

    // 域名操作方法
    async testDomain(domain) {
        try {
            const response = await fetch(`/api/domains/${domain}/test`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${window.authManager.getToken()}`
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess(`域名 ${domain} 连通性测试成功`);
            } else {
                this.showError(`域名 ${domain} 连通性测试失败: ${result.message}`);
            }

        } catch (error) {
            this.showError(`测试域名失败: ${error.message}`);
        }
    }

    editDomain(domainId) {
        // 显示编辑域名模态框
        this.showEditDomainModal(domainId);
    }

    async deleteDomain(domainId) {
        if (!confirm('确定要删除这个域名吗？此操作不可恢复。')) {
            return;
        }

        try {
            const response = await fetch(`/api/domains/${domainId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${window.authManager.getToken()}`
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess('域名删除成功');
                this.loadDomains(); // 重新加载列表
            } else {
                this.showError(`删除域名失败: ${result.message}`);
            }

        } catch (error) {
            this.showError(`删除域名失败: ${error.message}`);
        }
    }

    selectAll() {
        const selectAllCheckbox = document.getElementById('select-all-domains');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.dispatchEvent(new Event('change'));
        }
    }

    batchDelete() {
        const checkedBoxes = document.querySelectorAll('.domain-checkbox:checked');
        if (checkedBoxes.length === 0) {
            this.showWarning('请先选择要删除的域名');
            return;
        }

        if (!confirm(`确定要删除选中的 ${checkedBoxes.length} 个域名吗？此操作不可恢复。`)) {
            return;
        }

        // 批量删除逻辑
        const domainIds = Array.from(checkedBoxes).map(cb => cb.value);
        this.performBatchDelete(domainIds);
    }

    async performBatchDelete(domainIds) {
        try {
            const promises = domainIds.map(id =>
                fetch(`/api/domains/${id}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${window.authManager.getToken()}`
                    }
                })
            );

            await Promise.all(promises);
            this.showSuccess(`成功删除 ${domainIds.length} 个域名`);
            this.loadDomains(); // 重新加载列表

        } catch (error) {
            this.showError(`批量删除失败: ${error.message}`);
        }
    }

    // 模态框方法 - 由ComponentManager处理，这里保留兼容性方法
    showAddDomainModal() {
        console.log('🔧 显示添加域名模态框（通过ComponentManager）');

        // 触发ComponentManager的模态框显示
        const addButton = document.querySelector('[data-modal="add-domain-modal"]');
        if (addButton) {
            addButton.click();
        } else {
            console.error('❌ 找不到添加域名按钮');
        }
    }

    // ComponentManager已处理模态框事件，这里不需要重复绑定

    // 显示批量导入模态框 - 由ComponentManager处理
    showImportModal() {
        console.log('🔧 显示批量导入模态框（通过ComponentManager）');

        // 触发ComponentManager的模态框显示
        const importButton = document.querySelector('[data-modal="import-domains-modal"]');
        if (importButton) {
            importButton.click();
        } else {
            console.error('❌ 找不到批量导入按钮');
        }
    }

    // 处理添加域名
    async handleAddDomain() {
        const form = document.getElementById('add-domain-form');
        if (!form) return;

        const formData = new FormData(form);
        const domainDirection = formData.get('domain-direction');
        const domainData = {
            domain: formData.get('domain'),
            upstream: formData.get('upstream'),
            description: formData.get('description'),
            type: formData.get('type'),
            direction: domainDirection
        };

        // 验证数据
        if (!domainData.domain || !domainData.upstream) {
            this.showError('请填写完整的域名和上游服务信息');
            return;
        }

        // 验证域名方向选择（必选）
        if (!domainDirection) {
            this.showError('请选择域名方向（上游或下游）');
            this.highlightRequiredField('domain-direction');
            return;
        }

        try {
            const token = window['authManager'] ? window['authManager'].getToken() : localStorage.getItem('auth_token');
            const response = await fetch('/api/domains', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(domainData)
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.message || '添加域名失败');
            }

            this.showSuccess('域名添加成功');
            this.closeModal('add-domain-modal');
            this.loadDomains(); // 重新加载域名列表

        } catch (error) {
            console.error('添加域名失败:', error);
            this.showError(error.message || '添加域名失败');
        }
    }

    // 处理批量导入
    async handleImportDomains() {
        const modal = document.getElementById('import-domains-modal');
        if (!modal) return;

        const activeTab = modal.querySelector('.tab-content.is-active');
        if (!activeTab) return;

        let domainsData = [];

        if (activeTab.getAttribute('data-tab-content') === 'text-input') {
            // 处理文本输入
            const textarea = modal.querySelector('#domains-text');
            if (!textarea || !textarea.value.trim()) {
                this.showError('请输入域名列表');
                return;
            }

            const lines = textarea.value.trim().split('\n');
            domainsData = lines.map(line => {
                const parts = line.split(',').map(part => part.trim());
                if (parts.length >= 2) {
                    return {
                        domain: parts[0],
                        upstream: parts[1],
                        description: parts[2] || '',
                        type: 'downstream'
                    };
                }
                return null;
            }).filter(item => item !== null);

        } else {
            // 处理文件上传
            const fileInput = modal.querySelector('.file-input');
            if (!fileInput || !fileInput.files.length) {
                this.showError('请选择要上传的文件');
                return;
            }

            // 这里可以添加文件解析逻辑
            this.showError('文件上传功能暂未实现');
            return;
        }

        if (domainsData.length === 0) {
            this.showError('没有找到有效的域名数据');
            return;
        }

        try {
            const token = window['authManager'] ? window['authManager'].getToken() : localStorage.getItem('auth_token');
            const response = await fetch('/api/domains/batch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({ domains: domainsData })
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.message || '批量导入失败');
            }

            const result = await response.json();
            this.showSuccess(`成功导入 ${result.success_count || domainsData.length} 个域名`);
            this.closeModal('import-domains-modal');
            this.loadDomains(); // 重新加载域名列表

        } catch (error) {
            console.error('批量导入失败:', error);
            this.showError(error.message || '批量导入失败');
        }
    }

    // 处理批量添加
    async handleBatchAdd() {
        const modal = document.getElementById('batch-add-modal');
        if (!modal) return;

        // 获取域名方向选择
        const directionRadios = modal.querySelectorAll('input[name="batch-domain-direction"]');
        const selectedDirection = Array.from(directionRadios).find(radio => radio.checked);

        // 验证域名方向选择（必选）
        if (!selectedDirection) {
            this.showError('请选择域名方向（上游或下游）');
            this.highlightRequiredField('batch-domain-direction');
            return;
        }

        // 获取批量添加的域名列表
        const batchType = modal.querySelector('input[name="batch-type"]:checked')?.value;
        let domains = [];

        try {
            switch (batchType) {
                case 'manual':
                    const textarea = modal.querySelector('#batch-domains');
                    if (textarea && textarea.value.trim()) {
                        domains = textarea.value.trim().split('\n')
                            .map(line => line.trim())
                            .filter(line => line.length > 0);
                    }
                    break;
                case 'generate':
                    // 处理自动生成的域名
                    domains = this.generateDomains();
                    break;
                case 'import':
                    // 处理文件导入的域名
                    domains = this.getImportedDomains();
                    break;
            }

            if (domains.length === 0) {
                this.showError('请输入要添加的域名');
                return;
            }

            // 批量添加域名到域名池
            await this.performBatchAdd(domains, selectedDirection.value);

        } catch (error) {
            console.error('批量添加失败:', error);
            this.showError(error.message || '批量添加失败');
        }
    }

    // 执行批量添加
    async performBatchAdd(domains, direction) {
        try {
            const token = window['authManager'] ? window['authManager'].getToken() : localStorage.getItem('auth_token');

            const response = await fetch('/api/domain-pool/batch-add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    domains: domains,
                    direction: direction
                })
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.message || '批量添加失败');
            }

            const result = await response.json();
            this.showSuccess(`成功添加 ${result.success_count || domains.length} 个${direction === 'upstream' ? '上游' : '下游'}域名到域名池`);

            // 关闭模态框
            const modal = document.getElementById('batch-add-modal');
            if (modal) {
                modal.style.display = 'none';
            }

            // 重新加载域名列表
            this.loadDomains();

        } catch (error) {
            throw error;
        }
    }

    // 高亮显示必填字段
    highlightRequiredField(fieldName) {
        const radios = document.querySelectorAll(`input[name="${fieldName}"]`);
        if (radios.length > 0) {
            const formGroup = radios[0].closest('.form-group, .option-group');
            if (formGroup) {
                formGroup.classList.add('error');

                // 移除已存在的错误消息
                const existingError = formGroup.querySelector('.error-message');
                if (existingError) {
                    existingError.remove();
                }

                // 添加错误消息
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.textContent = '请选择一个选项';
                formGroup.appendChild(errorDiv);

                // 3秒后移除错误状态
                setTimeout(() => {
                    formGroup.classList.remove('error');
                    const errorMsg = formGroup.querySelector('.error-message');
                    if (errorMsg) {
                        errorMsg.remove();
                    }
                }, 3000);
            }
        }
    }

    // 生成域名（自动生成模式）
    generateDomains() {
        const modal = document.getElementById('batch-add-modal');
        const prefix = modal.querySelector('#domain-prefix')?.value || '';
        const suffix = modal.querySelector('#domain-suffix')?.value || '';
        const startNum = parseInt(modal.querySelector('#start-number')?.value) || 1;
        const endNum = parseInt(modal.querySelector('#end-number')?.value) || 100;

        const domains = [];
        for (let i = startNum; i <= endNum; i++) {
            domains.push(`${prefix}${i}${suffix}`);
        }
        return domains;
    }

    // 获取导入的域名（文件导入模式）
    getImportedDomains() {
        // 这里应该处理文件导入的逻辑
        // 暂时返回空数组
        return [];
    }

    // 显示成功消息
    showSuccess(message) {
        if (window.showToast) {
            window.showToast(message, 'success');
        } else {
            alert(message);
        }
    }

    // 显示错误消息
    showError(message) {
        if (window.showToast) {
            window.showToast(message, 'error');
        } else {
            alert(message);
        }
    }
}

// 初始化域名管理器
document.addEventListener('DOMContentLoaded', () => {
    window.domainManager = new DomainManager();
});

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DomainManager;
}