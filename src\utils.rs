#[allow(dead_code)]
#[allow(unused_imports)]
#[allow(unused_variables)]
use crate::error::{AppError, AppResult};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::error;

/// 通用的集合清理工具
pub struct CollectionCleaner;

impl CollectionCleaner {
    /// 清理Vec，保持指定大小
    pub fn cleanup_vec<T>(vec: &mut Vec<T>, max_size: usize, keep_size: usize) {
        if vec.len() > max_size {
            let remove_count = vec.len() - keep_size;
            vec.drain(0..remove_count);
        }
    }

    /// 清理HashMap，保持指定大小
    pub fn cleanup_hashmap<K, V>(map: &mut HashMap<K, V>, max_size: usize, keep_size: usize)
    where
        K: Clone + std::hash::Hash + Eq,
    {
        if map.len() > max_size {
            let remove_count = map.len() - keep_size;
            let keys_to_remove: Vec<K> = map.keys().take(remove_count).cloned().collect();
            for key in keys_to_remove {
                map.remove(&key);
            }
        }
    }

    /// 异步清理带锁的Vec
    pub async fn cleanup_locked_vec<T>(
        vec: &Arc<RwLock<Vec<T>>>,
        max_size: usize,
        keep_size: usize,
    ) {
        let mut vec_guard = vec.write().await;
        Self::cleanup_vec(&mut *vec_guard, max_size, keep_size);
    }

    /// 异步清理带锁的HashMap
    pub async fn cleanup_locked_hashmap<K, V>(
        map: &Arc<RwLock<HashMap<K, V>>>,
        max_size: usize,
        keep_size: usize,
    ) where
        K: Clone + std::hash::Hash + Eq,
    {
        let mut map_guard = map.write().await;
        Self::cleanup_hashmap(&mut *map_guard, max_size, keep_size);
    }
}

/// 通用的验证工具
pub struct ValidationUtils;

impl ValidationUtils {
    /// 验证字符串不为空
    pub fn validate_not_empty(input: &str, field_name: &str) -> AppResult<()> {
        if input.is_empty() {
            return Err(AppError::validation(format!("{} 不能为空", field_name)));
        }
        Ok(())
    }

    /// 验证字符串长度
    pub fn validate_length(
        input: &str,
        field_name: &str,
        min_len: Option<usize>,
        max_len: Option<usize>,
    ) -> AppResult<()> {
        if let Some(min) = min_len {
            if input.len() < min {
                return Err(AppError::validation(format!(
                    "{} 长度不能少于 {} 个字符",
                    field_name, min
                )));
            }
        }

        if let Some(max) = max_len {
            if input.len() > max {
                return Err(AppError::validation(format!(
                    "{} 长度不能超过 {} 个字符",
                    field_name, max
                )));
            }
        }

        Ok(())
    }

    /// 验证域名格式
    pub fn validate_domain(domain: &str) -> AppResult<()> {
        Self::validate_not_empty(domain, "域名")?;
        Self::validate_length(domain, "域名", None, Some(253))?;

        // 基本域名格式验证
        if !domain.contains('.') {
            return Err(AppError::validation("域名格式无效".to_string()));
        }

        // 检查是否包含非法字符
        for ch in domain.chars() {
            if !ch.is_alphanumeric() && ch != '.' && ch != '-' && ch != '_' {
                return Err(AppError::validation("域名包含非法字符".to_string()));
            }
        }

        Ok(())
    }

    /// 验证URL格式
    pub fn validate_url(url: &str) -> AppResult<()> {
        Self::validate_not_empty(url, "URL")?;

        // 基本URL格式检查
        if !url.starts_with("http://") && !url.starts_with("https://") {
            return Err(AppError::validation(
                "URL必须以http://或https://开头".to_string(),
            ));
        }

        // 使用url crate进行更严格的验证
        match url::Url::parse(url) {
            Ok(_) => Ok(()),
            Err(_) => Err(AppError::validation("URL格式无效".to_string())),
        }
    }

    /// 验证用户名格式
    pub fn validate_username(username: &str) -> AppResult<()> {
        Self::validate_not_empty(username, "用户名")?;
        Self::validate_length(username, "用户名", Some(3), Some(50))?;

        // 基本用户名格式检查
        if !username
            .chars()
            .all(|c| c.is_ascii_alphanumeric() || c == '_' || c == '-')
        {
            return Err(AppError::validation(
                "用户名只能包含字母、数字、下划线和连字符".to_string(),
            ));
        }

        Ok(())
    }
}

/// 通用的错误处理工具
pub struct ErrorUtils;

impl ErrorUtils {
    /// 记录错误并返回通用错误响应
    pub fn log_and_convert_error<E>(error: E, context: &str) -> AppError
    where
        E: std::error::Error + Send + Sync + 'static,
    {
        error!("错误发生在 {}: {}", context, error);
        AppError::internal(format!("{}: {}", context, error))
    }

    /// 安全地记录错误（避免敏感信息泄露）
    pub fn log_error_safely<E>(error: E, context: &str, user_message: &str) -> AppError
    where
        E: std::error::Error + Send + Sync + 'static,
    {
        // 记录详细错误到日志
        error!("内部错误在 {}: {}", context, error);
        // 返回用户友好的错误信息
        AppError::internal(user_message.to_string())
    }

    /// 将Result转换为AppResult，添加上下文信息
    pub fn with_context<T, E>(result: Result<T, E>, context: &str) -> AppResult<T>
    where
        E: std::error::Error + Send + Sync + 'static,
    {
        result.map_err(|e| Self::log_and_convert_error(e, context))
    }
}

/// 通用的时间工具
pub struct TimeUtils;

impl TimeUtils {
    /// 获取当前时间戳（秒）
    pub fn current_timestamp() -> i64 {
        chrono::Utc::now().timestamp()
    }

    /// 获取当前时间戳（毫秒）
    pub fn current_timestamp_millis() -> i64 {
        chrono::Utc::now().timestamp_millis()
    }

    /// 检查时间戳是否过期
    pub fn is_expired(timestamp: i64, duration_seconds: i64) -> bool {
        let now = Self::current_timestamp();
        now - timestamp > duration_seconds
    }

    /// 格式化时间戳为可读字符串
    pub fn format_timestamp(timestamp: i64) -> String {
        match chrono::DateTime::from_timestamp(timestamp, 0) {
            Some(dt) => dt.format("%Y-%m-%d %H:%M:%S UTC").to_string(),
            None => "Invalid timestamp".to_string(),
        }
    }
}

/// 通用的字符串工具
pub struct StringUtils;

impl StringUtils {
    /// 安全地截断字符串
    pub fn truncate_safely(s: &str, max_len: usize) -> String {
        if s.len() <= max_len {
            s.to_string()
        } else {
            format!("{}...", &s[..max_len.saturating_sub(3)])
        }
    }

    /// 清理字符串中的控制字符
    pub fn sanitize_string(s: &str) -> String {
        s.chars()
            .filter(|c| !c.is_control() || *c == '\n' || *c == '\r' || *c == '\t')
            .collect()
    }

    /// 生成随机字符串
    pub fn generate_random_string(length: usize) -> String {
        use rand::Rng;
        const CHARSET: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZ\
                                abcdefghijklmnopqrstuvwxyz\
                                0123456789";
        let mut rng = rand::thread_rng();
        (0..length)
            .map(|_| {
                let idx = rng.gen_range(0..CHARSET.len());
                CHARSET[idx] as char
            })
            .collect()
    }

    /// 掩码敏感信息
    pub fn mask_sensitive(s: &str, show_chars: usize) -> String {
        if s.len() <= show_chars {
            "*".repeat(s.len())
        } else {
            format!("{}****", &s[..show_chars])
        }
    }
}

/// 通用的网络工具
pub struct NetworkUtils;

impl NetworkUtils {
    /// 验证IP地址格式
    pub fn validate_ip(ip: &str) -> AppResult<std::net::IpAddr> {
        ip.parse()
            .map_err(|_| AppError::validation("无效的IP地址格式".to_string()))
    }

    /// 验证端口号
    pub fn validate_port(port: u16) -> AppResult<()> {
        if port == 0 {
            return Err(AppError::validation("端口号不能为0".to_string()));
        }
        Ok(())
    }

    /// 验证主机:端口格式
    pub fn validate_host_port(host_port: &str) -> AppResult<(String, u16)> {
        let parts: Vec<&str> = host_port.rsplitn(2, ':').collect();
        if parts.len() != 2 {
            return Err(AppError::validation("主机:端口格式无效".to_string()));
        }

        let host = parts[1].to_string();
        let port: u16 = parts[0]
            .parse()
            .map_err(|_| AppError::validation("端口号格式无效".to_string()))?;

        Self::validate_port(port)?;

        Ok((host, port))
    }
}

/// 通用的异步工具
pub struct AsyncUtils;

impl AsyncUtils {
    /// 带超时的异步操作
    pub async fn with_timeout<T, F>(
        future: F,
        timeout_duration: std::time::Duration,
    ) -> AppResult<T>
    where
        F: std::future::Future<Output = T>,
    {
        match tokio::time::timeout(timeout_duration, future).await {
            Ok(result) => Ok(result),
            Err(_) => Err(AppError::timeout("操作超时".to_string())),
        }
    }

    /// 安全地获取锁（带超时）
    pub async fn safe_write_lock<T>(
        lock: &Arc<RwLock<T>>,
        timeout_duration: std::time::Duration,
    ) -> AppResult<tokio::sync::RwLockWriteGuard<'_, T>> {
        match tokio::time::timeout(timeout_duration, lock.write()).await {
            Ok(guard) => Ok(guard),
            Err(_) => Err(AppError::timeout("获取写锁超时".to_string())),
        }
    }

    /// 安全地获取读锁（带超时）
    pub async fn safe_read_lock<T>(
        lock: &Arc<RwLock<T>>,
        timeout_duration: std::time::Duration,
    ) -> AppResult<tokio::sync::RwLockReadGuard<'_, T>> {
        match tokio::time::timeout(timeout_duration, lock.read()).await {
            Ok(guard) => Ok(guard),
            Err(_) => Err(AppError::timeout("获取读锁超时".to_string())),
        }
    }
}

/// API响应工具
pub struct ApiUtils;

impl ApiUtils {
    /// 创建成功响应
    pub fn success<T>(data: T) -> crate::types::ApiResponse<T> {
        crate::types::ApiResponse {
            success: true,
            data: Some(data),
            error: None,
            error_code: None,
            timestamp: chrono::Utc::now(),
            request_id: None,
        }
    }

    /// 创建带消息的成功响应
    pub fn success_with_message<T>(
        data: T,
        message: impl Into<String>,
    ) -> crate::types::ApiResponse<T> {
        crate::types::ApiResponse {
            success: true,
            timestamp: chrono::Utc::now(),
            data: Some(data),
            error: None,
            error_code: None,
            request_id: None,
        }
    }

    /// 创建错误响应
    pub fn error<T>(message: impl Into<String>) -> crate::types::ApiResponse<T> {
        crate::types::ApiResponse {
            success: false,
            data: None,
            error: Some(message.into()),
            error_code: None,
            timestamp: chrono::Utc::now(),
            request_id: None,
        }
    }

    /// 创建带错误代码的错误响应
    pub fn error_with_code<T>(
        message: impl Into<String>,
        error_code: impl Into<String>,
    ) -> crate::types::ApiResponse<T> {
        crate::types::ApiResponse {
            success: false,
            data: None,
            error: Some(message.into()),
            error_code: Some(error_code.into()),
            timestamp: chrono::Utc::now(),
            request_id: None,
        }
    }

    /// 从AppError创建错误响应
    pub fn from_app_error<T>(error: AppError) -> crate::types::ApiResponse<T> {
        crate::types::ApiResponse {
            success: false,
            data: None,
            error: Some(error.to_string()),
            error_code: Some(Self::get_error_code(&error)),
            timestamp: chrono::Utc::now(),
            request_id: None,
        }
    }

    /// 获取错误代码
    fn get_error_code(error: &AppError) -> String {
        match error {
            AppError::AppConfig(_) => "CONFIG_ERROR",
            AppError::Environment(_) => "ENV_ERROR",
            AppError::Startup(_) => "STARTUP_ERROR",
            AppError::Database(_) => "DATABASE_ERROR",
            AppError::Network(_) => "NETWORK_ERROR",
            AppError::Auth(_) => "AUTH_ERROR",
            AppError::Authorization(_) => "AUTHORIZATION_ERROR",
            AppError::Validation(_) => "VALIDATION_ERROR",
            AppError::Security(_) => "SECURITY_ERROR",
            AppError::Cache(_) => "CACHE_ERROR",
            AppError::Proxy(_) => "PROXY_ERROR",
            AppError::RecursiveProxy(_) => "RECURSIVE_PROXY_ERROR",
            AppError::DomainPool(_) => "DOMAIN_POOL_ERROR",
            AppError::Internal(_) => "INTERNAL_ERROR",
            AppError::InvalidInput(_) => "INVALID_INPUT",
            AppError::NotFound(_) => "NOT_FOUND",
            AppError::Conflict(_) => "CONFLICT",
            AppError::RateLimit(_) => "RATE_LIMIT",
            AppError::Timeout(_) => "TIMEOUT",
            AppError::ServiceUnavailable(_) => "SERVICE_UNAVAILABLE",
        }
        .to_string()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_validation_utils() {
        // 测试域名验证
        assert!(ValidationUtils::validate_domain("example.com").is_ok());
        assert!(ValidationUtils::validate_domain("").is_err());
        assert!(ValidationUtils::validate_domain("invalid").is_err());

        // 测试用户名验证
        assert!(ValidationUtils::validate_username("user123").is_ok());
        assert!(ValidationUtils::validate_username("ab").is_err()); // 太短
        assert!(ValidationUtils::validate_username("user@123").is_err()); // 非法字符
    }

    #[test]
    fn test_string_utils() {
        // 测试字符串截断
        assert_eq!(StringUtils::truncate_safely("hello", 10), "hello");
        assert_eq!(StringUtils::truncate_safely("hello world", 8), "hello...");

        // 测试敏感信息掩码
        assert_eq!(StringUtils::mask_sensitive("password123", 4), "pass****");
        assert_eq!(StringUtils::mask_sensitive("abc", 4), "***");
    }

    #[test]
    fn test_time_utils() {
        let now = TimeUtils::current_timestamp();
        assert!(now > 0);

        // 测试过期检查
        assert!(!TimeUtils::is_expired(now, 60)); // 不应该过期
        assert!(TimeUtils::is_expired(now - 120, 60)); // 应该过期
    }

    #[test]
    fn test_network_utils() {
        // 测试IP验证
        assert!(NetworkUtils::validate_ip("***********").is_ok());
        assert!(NetworkUtils::validate_ip("invalid").is_err());

        // 测试主机:端口验证
        assert!(NetworkUtils::validate_host_port("localhost:8080").is_ok());
        assert!(NetworkUtils::validate_host_port("invalid").is_err());
    }
}
