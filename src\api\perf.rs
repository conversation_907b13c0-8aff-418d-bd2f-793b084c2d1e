// src/api/perf.rs

// 只保留基本的性能配置接口
use crate::api::ApiResponse;
use crate::types::{AppState, PerfConfig};
use axum::{
    extract::{Extension, Json},
    response::IntoResponse,
};
use std::sync::Arc;

pub async fn get_perf_config(Extension(_state): Extension<Arc<AppState>>) -> impl IntoResponse {
    let config = PerfConfig::default();
    let resp: ApiResponse<PerfConfig> = ApiResponse {
        success: true,
        message: Some("获取性能配置成功".to_string()),
        data: Some(config),
        request_id: None,
        error: None,
        error_code: None,
    };
    Json(resp)
}

pub async fn update_perf_config(
    Extension(state): Extension<Arc<AppState>>,
    Json(config): Json<PerfConfig>,
) -> impl IntoResponse {
    state.perf.update_config(config).await;
    let resp: ApiResponse<serde_json::Value> = ApiResponse {
        success: true,
        message: Some("更新性能配置成功".to_string()),
        data: None,
        request_id: None,
        error: None,
        error_code: None,
    };
    Json(resp)
}

pub async fn get_extracted_urls(Extension(state): Extension<Arc<AppState>>) -> impl IntoResponse {
    match state.recursive_proxy.get_stats().await {
        stats => {
            let resp: ApiResponse<serde_json::Value> = ApiResponse {
                success: true,
                message: Some("获取提取URL统计成功".to_string()),
                data: Some(serde_json::json!({
                    "cache_stats": stats,
                    "extraction_enabled": true
                })),
                request_id: None,
                error: None,
                error_code: None,
            };
            Json(resp)
        }
    }
}

pub async fn extract_urls_from_content(
    Extension(state): Extension<Arc<AppState>>,
    Json(payload): Json<serde_json::Value>,
) -> impl IntoResponse {
    let content = payload
        .get("content")
        .and_then(|c| c.as_str())
        .unwrap_or("");
    let content_type = payload.get("content_type").and_then(|ct| ct.as_str());

    // 暂时注释掉 extract_urls_from_content 相关逻辑，避免编译错误
    /*
        match state
            .recursive_proxy
            .extract_urls_from_content(content, content_type)
            .await
        {
            Ok(urls) => {
                let resp: ApiResponse<Vec<String>> = ApiResponse {
                    success: true,
                    message: Some(format!("成功提取到 {} 个URL", urls.len())),
                    data: Some(urls),
                    request_id: None,
                    error: None,
                    error_code: None,
                };
                Json(resp)
            }
            Err(e) => {
                let resp: ApiResponse<serde_json::Value> = ApiResponse {
                    success: false,
                    message: Some("URL提取失败".to_string()),
                    data: None,
                    request_id: None,
                    error: Some(e.to_string()),
                    error_code: Some("EXTRACTION_ERROR".to_string()),
                };
                Json(resp)
            }
        }
    */
    let resp: ApiResponse<serde_json::Value> = ApiResponse {
        success: false,
        message: Some("URL提取功能未实现".to_string()),
        data: None,
        request_id: None,
        error: Some("Not implemented".to_string()),
        error_code: Some("NOT_IMPLEMENTED".to_string()),
    };
    Json(resp)
}

pub async fn validate_extraction_rule(
    Extension(state): Extension<Arc<AppState>>,
    Json(payload): Json<serde_json::Value>,
) -> impl IntoResponse {
    // 暂时注释掉 validate_extraction_rule 相关逻辑，避免编译错误
    /*
    if let (Some(rule_data), Some(test_content)) = (
        payload.get("rule"),
        payload.get("test_content").and_then(|c| c.as_str()),
    ) {
        if let Ok(rule) =
            serde_json::from_value::<crate::recursive_proxy::ExtractionRule>(rule_data.clone())
        {
            match state
                .recursive_proxy
                .validate_extraction_rule(&rule, test_content)
                .await
            {
                Ok(extracted_urls) => {
                    let resp: ApiResponse<serde_json::Value> = ApiResponse {
                        success: true,
                        message: Some(format!(
                            "规则验证成功，提取到 {} 个URL",
                            extracted_urls.len()
                        )),
                        data: Some(serde_json::json!({
                            "extracted_urls": extracted_urls,
                            "rule_valid": true
                        })),
                        request_id: None,
                        error: None,
                        error_code: None,
                    };
                    return Json(resp);
                }
                Err(e) => {
                    let resp: ApiResponse<serde_json::Value> = ApiResponse {
                        success: false,
                        message: Some("规则验证失败".to_string()),
                        data: None,
                        request_id: None,
                        error: Some(e.to_string()),
                        error_code: Some("VALIDATION_ERROR".to_string()),
                    };
                    return Json(resp);
                }
            }
        }
    }
    */
    let resp: ApiResponse<serde_json::Value> = ApiResponse {
        success: false,
        message: Some("规则验证功能未实现".to_string()),
        data: None,
        request_id: None,
        error: Some("Not implemented".to_string()),
        error_code: Some("NOT_IMPLEMENTED".to_string()),
    };
    Json(resp)
}

pub fn routes() -> axum::Router<Arc<AppState>> {
    axum::Router::new()
        .route("/api/perf/config", axum::routing::get(get_perf_config))
        .route("/api/perf/config", axum::routing::post(update_perf_config))
        .route(
            "/api/recursive/extracted-urls",
            axum::routing::get(get_extracted_urls),
        )
        .route(
            "/api/recursive/extract",
            axum::routing::post(extract_urls_from_content),
        )
        .route(
            "/api/recursive/validate-rule",
            axum::routing::post(validate_extraction_rule),
        )
}
