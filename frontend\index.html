<!DOCTYPE html>
<html lang="zh-CN" data-theme="dark">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SM镜像系统 - 智能管理控制台</title>
    <link rel="stylesheet" href="/css/main.css?v=20241214-009">
    <link rel="stylesheet" href="/css/components.css?v=20241214-010">
    <link rel="icon" href="/favicon.ico" type="image/x-icon">
</head>

<body class="scale-container scale-100">
    <!-- 应用主容器 -->
    <div class="app-container">
        <!-- 侧边栏 -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <div class="logo-icon">🪞</div>
                    <div class="logo-text">
                        <h1>SM镜像系统</h1>
                        <span class="version">v2.0.0</span>
                    </div>
                </div>
                <button class="sidebar-toggle" id="sidebar-toggle">
                    <span class="toggle-icon">◀</span>
                </button>
            </div>

            <!-- 用户信息区 -->
            <div class="user-info" id="user-info">
                <div class="user-avatar">👤</div>
                <div class="user-details">
                    <div class="user-name">管理员</div>
                    <div class="user-status" id="auth-status">🔒 未登录</div>
                </div>
                <button class="user-menu-btn" id="user-menu-btn">⚙️</button>
            </div>



            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-group">
                    <div class="nav-group-title">主要功能</div>
                    <button class="nav-item active" data-tab="dashboard">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">仪表板</span>
                    </button>
                    <button class="nav-item" data-tab="domains">
                        <span class="nav-icon">🌐</span>
                        <span class="nav-text">域名管理</span>
                        <span class="nav-badge" id="domain-badge">0</span>
                    </button>
                    <button class="nav-item" data-tab="domain-groups">
                        <span class="nav-icon">📁</span>
                        <span class="nav-text">域名分组</span>
                        <span class="nav-badge" id="group-badge">0</span>
                    </button>
                    <button class="nav-item" data-tab="domain-pool">
                        <span class="nav-icon">🏊</span>
                        <span class="nav-text">域名池</span>
                    </button>
                    <button class="nav-item" data-tab="tasks">
                        <span class="nav-icon">🚀</span>
                        <span class="nav-text">镜像任务</span>
                        <span class="nav-badge" id="task-badge">0</span>
                    </button>
                    <button class="nav-item" data-tab="recursive-proxy">
                        <span class="nav-icon">🔄</span>
                        <span class="nav-text">递归代理</span>
                        <span class="nav-badge" id="recursive-badge">0</span>
                    </button>
                </div>

                <div class="nav-group">
                    <div class="nav-group-title">智能分析</div>
                    <button class="nav-item" data-tab="ml-analysis">
                        <span class="nav-icon">🧠</span>
                        <span class="nav-text">机器学习</span>
                    </button>
                    <button class="nav-item" data-tab="security-evasion">
                        <span class="nav-icon">🛡️</span>
                        <span class="nav-text">安全对抗</span>
                    </button>
                </div>

                <div class="nav-group">
                    <div class="nav-group-title">系统管理</div>
                    <button class="nav-item" data-tab="blacklist">
                        <span class="nav-icon">🚫</span>
                        <span class="nav-text">黑名单</span>
                    </button>
                    <button class="nav-item" data-tab="monitoring">
                        <span class="nav-icon">📈</span>
                        <span class="nav-text">性能监控</span>
                    </button>
                    <button class="nav-item" data-tab="config">
                        <span class="nav-icon">⚙️</span>
                        <span class="nav-text">系统配置</span>
                    </button>
                    <button class="nav-item" data-tab="logs">
                        <span class="nav-icon">📝</span>
                        <span class="nav-text">系统日志</span>
                    </button>
                </div>
            </nav>

            <!-- 快速操作 -->
            <div class="quick-actions">
                <div class="action-group-title">快速操作</div>
                <button class="action-btn" data-action="add-domain" title="添加域名">
                    <span class="action-icon">➕</span>
                    <span class="action-text">添加域名</span>
                </button>
                <button class="action-btn" data-action="start-task" title="启动任务">
                    <span class="action-icon">🚀</span>
                    <span class="action-text">启动任务</span>
                </button>
                <button class="action-btn" data-action="batch-add" title="批量添加">
                    <span class="action-icon">📦</span>
                    <span class="action-text">批量添加</span>
                </button>
            </div>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部工具栏 -->
            <header class="toolbar">
                <div class="toolbar-left">
                    <button class="mobile-menu-btn" id="mobile-menu-btn">☰</button>
                    <div class="breadcrumb" id="breadcrumb">
                        <span class="breadcrumb-item active">仪表板</span>
                    </div>
                </div>

                <div class="toolbar-center">
                    <div class="search-box">
                        <input type="text" placeholder="搜索域名、任务、配置..." id="global-search">
                        <button class="search-btn">🔍</button>
                    </div>
                </div>

                <div class="toolbar-right">
                    <button class="tool-btn" id="refresh-btn" title="刷新数据">🔄</button>
                    <button class="tool-btn" id="notifications-btn" title="通知">🔔</button>
                    <button class="tool-btn" id="fullscreen-btn" title="全屏">⛶</button>
                    <div class="scale-controls">
                        <button class="tool-btn" id="scale-down" title="缩小">🔍-</button>
                        <span class="scale-indicator" id="scale-indicator">100%</span>
                        <button class="tool-btn" id="scale-up" title="放大">🔍+</button>
                    </div>
                    <button class="auth-btn" id="auth-btn">🔐 登录</button>
                </div>
            </header>

            <!-- 内容区域 -->
            <div class="content-area" id="content-area">
                <!-- 仪表板 -->
                <section id="dashboard" class="content-section active">
                    <div class="section-header">
                        <h2>📊 系统仪表板</h2>
                        <div class="section-actions">
                            <button class="btn btn-outline" id="dashboard-settings">⚙️ 设置</button>
                        </div>
                    </div>

                    <!-- 关键指标卡片 -->
                    <div class="metrics-grid">
                        <div class="metric-card primary">
                            <div class="metric-icon">🌐</div>
                            <div class="metric-info">
                                <div class="metric-value" id="total-domains">0</div>
                                <div class="metric-label">总域名数</div>
                                <div class="metric-change">+0 今日新增</div>
                            </div>
                        </div>

                        <div class="metric-card success">
                            <div class="metric-icon">✅</div>
                            <div class="metric-info">
                                <div class="metric-value" id="active-domains">0</div>
                                <div class="metric-label">活跃域名</div>
                                <div class="metric-change">健康率 100%</div>
                            </div>
                        </div>

                        <div class="metric-card warning">
                            <div class="metric-icon">🚀</div>
                            <div class="metric-info">
                                <div class="metric-value" id="running-tasks">0</div>
                                <div class="metric-label">运行任务</div>
                                <div class="metric-change">0 排队中</div>
                            </div>
                        </div>

                        <div class="metric-card info">
                            <div class="metric-icon">📊</div>
                            <div class="metric-info">
                                <div class="metric-value" id="total-requests">0</div>
                                <div class="metric-label">总请求数</div>
                                <div class="metric-change">+0/小时</div>
                            </div>
                        </div>
                    </div>

                    <!-- 功能面板 -->
                    <div class="dashboard-panels">
                        <div class="panel-row">
                            <!-- 系统状态面板 -->
                            <div class="panel">
                                <div class="panel-header">
                                    <h3>🏥 系统状态</h3>
                                    <button class="panel-action" hx-get="/api/public/health"
                                        hx-target="#health-status">刷新</button>
                                </div>
                                <div class="panel-body">
                                    <div id="health-status" hx-get="/api/public/health" hx-trigger="load, every 30s">
                                        <div class="loading-spinner">检查系统状态...</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 最近活动面板 -->
                            <div class="panel">
                                <div class="panel-header">
                                    <h3>📝 最近活动</h3>
                                    <button class="panel-action">查看全部</button>
                                </div>
                                <div class="panel-body">
                                    <div class="activity-list" id="recent-activity">
                                        <div class="activity-item">
                                            <span class="activity-time">刚刚</span>
                                            <span class="activity-text">系统启动完成</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="panel-row">
                            <!-- 域名统计图表 -->
                            <div class="panel chart-panel">
                                <div class="panel-header">
                                    <h3>📈 域名趋势</h3>
                                    <select class="panel-select">
                                        <option>最近7天</option>
                                        <option>最近30天</option>
                                    </select>
                                </div>
                                <div class="panel-body">
                                    <div class="chart-container" id="domain-chart">
                                        <div class="chart-placeholder">暂无数据</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 性能监控 -->
                            <div class="panel">
                                <div class="panel-header">
                                    <h3>⚡ 性能监控</h3>
                                    <span class="status-dot healthy"></span>
                                </div>
                                <div class="panel-body">
                                    <div class="perf-metrics" hx-get="/api/perf/metrics"
                                        hx-trigger="authenticated, every 30s" hx-include="[data-auth-token]">
                                        <div class="auth-required">需要认证后查看</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 域名管理 -->
                <section id="domains" class="content-section">
                    <div class="section-header">
                        <h2>🌐 域名管理</h2>
                        <div class="section-actions">
                            <button class="btn btn-primary" data-modal="add-domain-modal">➕ 添加域名</button>
                            <button class="btn btn-outline" data-modal="import-domains-modal">📥 批量导入</button>
                            <button class="btn btn-outline" data-action="export-domains">📤 导出</button>
                        </div>
                    </div>

                    <!-- 域名过滤器 -->
                    <div class="filter-bar">
                        <div class="filter-group">
                            <label>状态:</label>
                            <select class="filter-select" id="domain-status-filter">
                                <option value="">全部</option>
                                <option value="active">活跃</option>
                                <option value="inactive">停用</option>
                                <option value="error">异常</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>类型:</label>
                            <select class="filter-select" id="domain-type-filter">
                                <option value="">全部</option>
                                <option value="downstream">下游域名</option>
                                <option value="upstream">上游域名</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <input type="text" placeholder="搜索域名..." class="filter-input" id="domain-search">
                        </div>
                        <button class="btn btn-outline" id="clear-filters">清除筛选</button>
                    </div>

                    <!-- 域名列表 -->
                    <div class="data-table-container">
                        <div id="domain-table" hx-get="/api/domains" hx-trigger="authenticated, refresh from:body"
                            hx-include="[data-auth-token]">
                            <div class="auth-required">
                                <div class="auth-prompt">
                                    <div class="auth-icon">🔐</div>
                                    <p>请先登录以查看域名列表</p>
                                    <button class="btn btn-primary" id="prompt-login">立即登录</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 域名分组管理 -->
                <section id="domain-groups" class="content-section">
                    <div class="section-header">
                        <h2>📁 域名分组管理</h2>
                        <div class="section-actions">
                            <button class="btn btn-primary" data-modal="add-group-modal">➕ 新建分组</button>
                            <button class="btn btn-outline" data-action="refresh-groups">🔄 刷新</button>
                        </div>
                    </div>

                    <!-- 分组过滤器 -->
                    <div class="filter-bar">
                        <div class="filter-group">
                            <label for="group-status-filter">状态筛选:</label>
                            <select id="group-status-filter" class="filter-select">
                                <option value="">全部状态</option>
                                <option value="active">活跃</option>
                                <option value="inactive">停用</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="group-search">搜索分组:</label>
                            <input type="text" id="group-search" class="filter-input" placeholder="输入分组名称...">
                        </div>
                        <div class="filter-actions">
                            <button class="btn btn-sm btn-outline" id="clear-group-filters">清除筛选</button>
                        </div>
                    </div>

                    <!-- 域名分组列表 -->
                    <div class="data-table-container">
                        <div id="group-table" hx-get="/api/domain-groups" hx-trigger="authenticated, refresh from:body"
                            hx-include="[data-auth-token]">
                            <div class="auth-required">
                                <div class="auth-prompt">
                                    <div class="auth-icon">🔐</div>
                                    <p>请先登录以查看域名分组</p>
                                    <button class="btn btn-primary" id="prompt-login-groups">立即登录</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 域名池管理 -->
                <section id="domain-pool" class="content-section">
                    <div class="section-header">
                        <h2>🏊 域名池管理</h2>
                        <div class="section-actions">
                            <button class="btn btn-success" data-modal="batch-add-modal">📦 批量添加</button>
                            <button class="btn btn-warning" data-action="auto-pairing">🔄 自动配对</button>
                            <button class="btn btn-info" data-action="health-check-all">🏥 健康检查</button>
                        </div>
                    </div>

                    <!-- 域名池统计 -->
                    <div class="pool-stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">🌊</div>
                            <div class="stat-info">
                                <div class="stat-value" id="pool-total">0</div>
                                <div class="stat-label">池中域名</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">🔗</div>
                            <div class="stat-info">
                                <div class="stat-value" id="pool-mapped">0</div>
                                <div class="stat-label">已映射</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">⏳</div>
                            <div class="stat-info">
                                <div class="stat-value" id="pool-pending">0</div>
                                <div class="stat-label">待配对</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">❌</div>
                            <div class="stat-info">
                                <div class="stat-value" id="pool-failed">0</div>
                                <div class="stat-label">异常</div>
                            </div>
                        </div>
                    </div>

                    <!-- 域名池内容 -->
                    <div class="pool-tabs">
                        <button class="tab-btn active" data-pool-tab="mappings">域名映射</button>
                        <button class="tab-btn" data-pool-tab="operations">批量操作</button>
                        <button class="tab-btn" data-pool-tab="health">健康状态</button>
                        <button class="tab-btn" data-pool-tab="settings">池设置</button>
                    </div>

                    <div class="pool-content">
                        <div id="mappings" class="pool-tab-content active">
                            <div hx-get="/api/domain-pool/mappings" hx-trigger="authenticated"
                                hx-include="[data-auth-token]">
                                <div class="auth-required">需要认证后查看域名映射</div>
                            </div>
                        </div>

                        <div id="operations" class="pool-tab-content">
                            <div class="operations-panel">
                                <div class="auth-required">需要认证后使用批量操作</div>
                            </div>
                        </div>

                        <div id="health" class="pool-tab-content">
                            <div hx-get="/api/domain-pool/health-status" hx-trigger="authenticated"
                                hx-include="[data-auth-token]">
                                <div class="auth-required">需要认证后查看健康状态</div>
                            </div>
                        </div>

                        <div id="settings" class="pool-tab-content">
                            <div class="pool-settings">
                                <div class="auth-required">需要认证后配置域名池设置</div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 镜像任务 -->
                <section id="tasks" class="content-section">
                    <div class="section-header">
                        <h2>🚀 镜像任务</h2>
                        <div class="section-actions">
                            <button class="btn btn-success" data-modal="start-task-modal">🚀 新建任务</button>
                            <button class="btn btn-outline" data-action="task-templates">📋 任务模板</button>
                        </div>
                    </div>

                    <!-- 任务统计 -->
                    <div class="task-stats">
                        <div class="task-stat-item">
                            <span class="stat-number" id="tasks-running">0</span>
                            <span class="stat-label">运行中</span>
                        </div>
                        <div class="task-stat-item">
                            <span class="stat-number" id="tasks-completed">0</span>
                            <span class="stat-label">已完成</span>
                        </div>
                        <div class="task-stat-item">
                            <span class="stat-number" id="tasks-failed">0</span>
                            <span class="stat-label">失败</span>
                        </div>
                        <div class="task-stat-item">
                            <span class="stat-number" id="tasks-pending">0</span>
                            <span class="stat-label">等待中</span>
                        </div>
                    </div>

                    <!-- 任务控制 -->
                    <div class="task-controls">
                        <div class="control-group">
                            <label>状态筛选:</label>
                            <select class="form-select" id="task-status-filter">
                                <option value="">全部</option>
                                <option value="running">运行中</option>
                                <option value="completed">已完成</option>
                                <option value="failed">失败</option>
                                <option value="pending">等待中</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <input type="text" placeholder="搜索任务..." class="form-input" id="task-search">
                        </div>
                        <button class="btn btn-outline" hx-get="/api/tasks" hx-target="#task-list"
                            hx-include="[data-auth-token]">
                            🔄 刷新
                        </button>
                    </div>

                    <!-- 任务列表 -->
                    <div id="task-list" hx-get="/api/tasks" hx-trigger="authenticated" hx-include="[data-auth-token]">
                        <div class="auth-required">需要认证后查看任务列表</div>
                    </div>
                </section>

                <!-- 递归代理 -->
                <section id="recursive-proxy" class="content-section">
                    <div class="section-header">
                        <h2>🔄 递归代理</h2>
                        <div class="section-actions">
                            <button class="btn btn-success" id="start-recursive-proxy">
                                <span class="btn-icon">▶️</span>
                                启动递归
                            </button>
                            <button class="btn btn-danger" id="stop-recursive-proxy" disabled>
                                <span class="btn-icon">⏹️</span>
                                停止
                            </button>
                            <button class="btn btn-outline" id="refresh-recursive">
                                <span class="btn-icon">🔄</span>
                                刷新
                            </button>
                        </div>
                    </div>

                    <!-- 递归状态概览 -->
                    <div class="recursive-dashboard">
                        <div class="status-cards">
                            <div class="status-card primary">
                                <div class="status-icon">🔄</div>
                                <div class="status-info">
                                    <div class="status-value" id="recursive-status-text">未启动</div>
                                    <div class="status-label">递归状态</div>
                                    <div class="status-indicator offline" id="recursive-status-dot"></div>
                                </div>
                            </div>

                            <div class="status-card success">
                                <div class="status-icon">🔗</div>
                                <div class="status-info">
                                    <div class="status-value" id="active-sessions">0</div>
                                    <div class="status-label">活跃会话</div>
                                    <div class="status-change">+0 新增</div>
                                </div>
                            </div>

                            <div class="status-card warning">
                                <div class="status-icon">🌐</div>
                                <div class="status-info">
                                    <div class="status-value" id="discovered-domains">0</div>
                                    <div class="status-label">发现域名</div>
                                    <div class="status-change">+0 今日</div>
                                </div>
                            </div>

                            <div class="status-card info">
                                <div class="status-icon">⚡</div>
                                <div class="status-info">
                                    <div class="status-value" id="success-rate">0%</div>
                                    <div class="status-label">成功率</div>
                                    <div class="status-change" id="avg-response-time">0ms 平均</div>
                                </div>
                            </div>
                        </div>

                        <!-- 实时监控面板 -->
                        <div class="panel-row">
                            <!-- 实时发现 -->
                            <div class="panel">
                                <div class="panel-header">
                                    <h3>🔍 实时发现</h3>
                                    <div class="panel-controls">
                                        <button class="btn btn-sm" id="clear-discoveries">清空</button>
                                        <button class="btn btn-sm" id="export-discoveries">导出</button>
                                    </div>
                                </div>
                                <div class="panel-body">
                                    <div class="discovery-feed" id="discovery-feed">
                                        <div class="discovery-item placeholder">
                                            <div class="discovery-time">等待发现...</div>
                                            <div class="discovery-domain">递归代理未启动</div>
                                            <div class="discovery-source">-</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 智能分析 -->
                            <div class="panel">
                                <div class="panel-header">
                                    <h3>🧠 智能分析</h3>
                                    <div class="panel-controls">
                                        <button class="control-btn active" data-analysis="patterns">模式</button>
                                        <button class="control-btn" data-analysis="content">内容</button>
                                        <button class="control-btn" data-analysis="strategy">策略</button>
                                    </div>
                                </div>
                                <div class="panel-body">
                                    <!-- URL模式分析 -->
                                    <div class="analysis-content active" id="patterns-analysis">
                                        <div class="analysis-stats">
                                            <div class="stat-item">
                                                <span class="stat-label">学习模式</span>
                                                <span class="stat-value" id="learned-patterns">0</span>
                                            </div>
                                            <div class="stat-item">
                                                <span class="stat-label">置信度</span>
                                                <span class="stat-value" id="pattern-confidence">0%</span>
                                            </div>
                                            <div class="stat-item">
                                                <span class="stat-label">准确率</span>
                                                <span class="stat-value" id="recommendation-accuracy">0%</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 内容分析 -->
                                    <div class="analysis-content" id="content-analysis">
                                        <div class="analysis-stats">
                                            <div class="stat-item">
                                                <span class="stat-label">分析页面</span>
                                                <span class="stat-value" id="analyzed-pages">0</span>
                                            </div>
                                            <div class="stat-item">
                                                <span class="stat-label">相似度</span>
                                                <span class="stat-value" id="similarity-threshold">75%</span>
                                            </div>
                                            <div class="stat-item">
                                                <span class="stat-label">语言</span>
                                                <span class="stat-value" id="detected-languages">中英</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 自适应策略 -->
                                    <div class="analysis-content" id="strategy-analysis">
                                        <div class="analysis-stats">
                                            <div class="stat-item">
                                                <span class="stat-label">策略数</span>
                                                <span class="stat-value" id="active-strategies">0</span>
                                            </div>
                                            <div class="stat-item">
                                                <span class="stat-label">效果</span>
                                                <span class="stat-value" id="strategy-effectiveness">0%</span>
                                            </div>
                                            <div class="stat-item">
                                                <span class="stat-label">自适应</span>
                                                <span class="stat-value" id="adaptive-enabled">关闭</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 安全对抗状态 -->
                        <div class="panel">
                            <div class="panel-header">
                                <h3>🛡️ 安全对抗</h3>
                                <span class="status-dot healthy" id="security-status"></span>
                            </div>
                            <div class="panel-body">
                                <div class="security-grid">
                                    <div class="security-item">
                                        <div class="security-icon">🔥</div>
                                        <div class="security-info">
                                            <div class="security-label">WAF绕过</div>
                                            <div class="security-status enabled" id="waf-bypass-status">启用</div>
                                            <div class="security-rate" id="waf-bypass-rate">85%</div>
                                        </div>
                                    </div>
                                    <div class="security-item">
                                        <div class="security-icon">🤖</div>
                                        <div class="security-info">
                                            <div class="security-label">验证码识别</div>
                                            <div class="security-status enabled" id="captcha-status">启用</div>
                                            <div class="security-rate" id="captcha-rate">78%</div>
                                        </div>
                                    </div>
                                    <div class="security-item">
                                        <div class="security-icon">🕷️</div>
                                        <div class="security-info">
                                            <div class="security-label">反爬虫对抗</div>
                                            <div class="security-status enabled" id="antibot-status">启用</div>
                                            <div class="security-rate" id="antibot-rate">92%</div>
                                        </div>
                                    </div>
                                    <div class="security-item">
                                        <div class="security-icon">🎭</div>
                                        <div class="security-info">
                                            <div class="security-label">行为模拟</div>
                                            <div class="security-status enabled" id="behavior-status">启用</div>
                                            <div class="security-rate" id="behavior-rate">88%</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 机器学习分析 -->
                <section id="ml-analysis" class="content-section">
                    <div class="section-header">
                        <h2>🧠 机器学习分析</h2>
                        <div class="section-actions">
                            <button class="btn btn-primary" id="train-model">🎯 训练模型</button>
                            <button class="btn btn-outline" id="export-patterns">📤 导出模式</button>
                            <button class="btn btn-outline" id="import-patterns">📥 导入模式</button>
                        </div>
                    </div>

                    <!-- ML功能标签页 -->
                    <div class="ml-tabs">
                        <button class="tab-btn active" data-ml-tab="patterns">URL模式学习</button>
                        <button class="tab-btn" data-ml-tab="content">内容分析</button>
                        <button class="tab-btn" data-ml-tab="strategy">自适应策略</button>
                        <button class="tab-btn" data-ml-tab="performance">性能分析</button>
                    </div>

                    <div class="ml-content">
                        <!-- URL模式学习 -->
                        <div id="patterns" class="ml-tab-content active">
                            <div class="ml-panel">
                                <div class="panel-header">
                                    <h3>📊 学习统计</h3>
                                </div>
                                <div class="panel-body">
                                    <div class="ml-stats-grid">
                                        <div class="ml-stat">
                                            <span class="ml-stat-value" id="total-patterns">0</span>
                                            <span class="ml-stat-label">总模式数</span>
                                        </div>
                                        <div class="ml-stat">
                                            <span class="ml-stat-value" id="high-confidence-patterns">0</span>
                                            <span class="ml-stat-label">高置信度</span>
                                        </div>
                                        <div class="ml-stat">
                                            <span class="ml-stat-value" id="pattern-accuracy">0%</span>
                                            <span class="ml-stat-label">预测准确率</span>
                                        </div>
                                        <div class="ml-stat">
                                            <span class="ml-stat-value" id="learning-progress">0%</span>
                                            <span class="ml-stat-label">学习进度</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="ml-panel">
                                <div class="panel-header">
                                    <h3>🎯 模式列表</h3>
                                    <div class="panel-controls">
                                        <select class="panel-select" id="pattern-filter">
                                            <option value="all">全部模式</option>
                                            <option value="static">静态资源</option>
                                            <option value="api">API接口</option>
                                            <option value="page">页面导航</option>
                                            <option value="pagination">分页</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="panel-body">
                                    <div class="pattern-list" id="pattern-list">
                                        <div class="pattern-item">
                                            <div class="pattern-type">静态资源</div>
                                            <div class="pattern-regex">.*\.(css|js|png|jpg)$</div>
                                            <div class="pattern-confidence">95%</div>
                                            <div class="pattern-action skip">跳过</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 内容分析 -->
                        <div id="content" class="ml-tab-content">
                            <div class="content-analysis-panel">
                                <div class="auth-required">需要认证后查看内容分析</div>
                            </div>
                        </div>

                        <!-- 自适应策略 -->
                        <div id="strategy" class="ml-tab-content">
                            <div class="strategy-panel">
                                <div class="auth-required">需要认证后查看自适应策略</div>
                            </div>
                        </div>

                        <!-- 性能分析 -->
                        <div id="performance" class="ml-tab-content">
                            <div class="performance-panel">
                                <div class="auth-required">需要认证后查看性能分析</div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 安全对抗 -->
                <section id="security-evasion" class="content-section">
                    <div class="section-header">
                        <h2>🛡️ 安全对抗</h2>
                        <div class="section-actions">
                            <button class="btn btn-success" id="enable-all-security">🛡️ 全部启用</button>
                            <button class="btn btn-warning" id="test-security">🧪 测试对抗</button>
                            <button class="btn btn-outline" id="security-settings">⚙️ 设置</button>
                        </div>
                    </div>

                    <!-- 安全功能标签页 -->
                    <div class="security-tabs">
                        <button class="tab-btn active" data-security-tab="waf">WAF绕过</button>
                        <button class="tab-btn" data-security-tab="captcha">验证码识别</button>
                        <button class="tab-btn" data-security-tab="antibot">反爬虫对抗</button>
                        <button class="tab-btn" data-security-tab="behavior">行为模拟</button>
                    </div>

                    <div class="security-content">
                        <!-- WAF绕过 -->
                        <div id="waf" class="security-tab-content active">
                            <div class="security-panel">
                                <div class="panel-header">
                                    <h3>🔥 WAF检测与绕过</h3>
                                    <div class="panel-status">
                                        <span class="status-dot healthy"></span>
                                        <span>运行中</span>
                                    </div>
                                </div>
                                <div class="panel-body">
                                    <div class="waf-stats">
                                        <div class="waf-stat">
                                            <span class="waf-stat-value" id="waf-detections">0</span>
                                            <span class="waf-stat-label">检测次数</span>
                                        </div>
                                        <div class="waf-stat">
                                            <span class="waf-stat-value" id="waf-bypasses">0</span>
                                            <span class="waf-stat-label">绕过成功</span>
                                        </div>
                                        <div class="waf-stat">
                                            <span class="waf-stat-value" id="waf-success-rate">0%</span>
                                            <span class="waf-stat-label">成功率</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 验证码识别 -->
                        <div id="captcha" class="security-tab-content">
                            <div class="captcha-panel">
                                <div class="auth-required">需要认证后查看验证码识别</div>
                            </div>
                        </div>

                        <!-- 反爬虫对抗 -->
                        <div id="antibot" class="security-tab-content">
                            <div class="antibot-panel">
                                <div class="auth-required">需要认证后查看反爬虫对抗</div>
                            </div>
                        </div>

                        <!-- 行为模拟 -->
                        <div id="behavior" class="security-tab-content">
                            <div class="behavior-panel">
                                <div class="auth-required">需要认证后查看行为模拟</div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 黑名单管理 -->
                <section id="blacklist" class="content-section">
                    <div class="section-header">
                        <h2>🚫 黑名单管理</h2>
                        <div class="section-actions">
                            <button class="btn btn-primary" data-modal="add-blacklist-modal">➕ 添加规则</button>
                            <button class="btn btn-outline" data-modal="import-blacklist-modal">📥 批量导入</button>
                        </div>
                    </div>

                    <div class="blacklist-tabs">
                        <button class="tab-btn active" data-blacklist-tab="all">全部规则</button>
                        <button class="tab-btn" data-blacklist-tab="keywords">关键词</button>
                        <button class="tab-btn" data-blacklist-tab="domains">域名</button>
                        <button class="tab-btn" data-blacklist-tab="regex">正则表达式</button>
                        <button class="tab-btn" data-blacklist-tab="countries">国别域名</button>
                    </div>

                    <div class="blacklist-content" hx-get="/api/blacklist" hx-trigger="authenticated"
                        hx-include="[data-auth-token]">
                        <div class="auth-required">需要认证后查看黑名单规则</div>
                    </div>
                </section>

                <!-- 性能监控 -->
                <section id="monitoring" class="content-section">
                    <div class="section-header">
                        <h2>📈 性能监控</h2>
                        <div class="section-actions">
                            <button class="btn btn-outline" data-action="export-report">📊 导出报告</button>
                            <button class="btn btn-outline" data-action="monitoring-settings">⚙️ 监控设置</button>
                        </div>
                    </div>

                    <div class="monitoring-grid">
                        <div class="monitor-panel">
                            <div class="panel-header">
                                <h3>📊 实时性能</h3>
                                <div class="panel-controls">
                                    <button class="control-btn active" data-timerange="1h">1小时</button>
                                    <button class="control-btn" data-timerange="6h">6小时</button>
                                    <button class="control-btn" data-timerange="24h">24小时</button>
                                </div>
                            </div>
                            <div class="panel-body" hx-get="/api/perf/metrics" hx-trigger="authenticated, every 30s"
                                hx-include="[data-auth-token]">
                                <div class="auth-required">需要认证后查看性能数据</div>
                            </div>
                        </div>

                        <div class="monitor-panel">
                            <div class="panel-header">
                                <h3>🔍 系统状态</h3>
                            </div>
                            <div class="panel-body" hx-get="/api/status/overview" hx-trigger="authenticated, every 30s"
                                hx-include="[data-auth-token]">
                                <div class="auth-required">需要认证后查看系统状态</div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 系统配置 -->
                <section id="config" class="content-section">
                    <div class="section-header">
                        <h2>⚙️ 系统配置</h2>
                        <div class="section-actions">
                            <button class="btn btn-success" id="save-config">💾 保存配置</button>
                            <button class="btn btn-warning" data-action="reload-config">🔄 重载配置</button>
                            <button class="btn btn-info" data-action="export-config">📤 导出配置</button>
                            <button class="btn btn-outline" data-action="config-backup">🗄️ 备份配置</button>
                        </div>
                    </div>

                    <div class="config-tabs">
                        <button class="tab-btn active" data-config-tab="general">常规设置</button>
                        <button class="tab-btn" data-config-tab="proxy">代理配置</button>
                        <button class="tab-btn" data-config-tab="cache">缓存设置</button>
                        <button class="tab-btn" data-config-tab="security">安全配置</button>
                        <button class="tab-btn" data-config-tab="performance">性能调优</button>
                    </div>

                    <div class="config-content" hx-get="/api/config" hx-trigger="authenticated"
                        hx-include="[data-auth-token]">
                        <div class="auth-required">需要认证后查看系统配置</div>
                    </div>
                </section>

                <!-- 系统日志 -->
                <section id="logs" class="content-section">
                    <div class="section-header">
                        <h2>📝 系统日志</h2>
                        <div class="section-actions">
                            <button class="btn btn-outline" id="clear-logs">🗑️ 清空日志</button>
                            <button class="btn btn-outline" data-action="export-logs">📤 导出日志</button>
                            <button class="btn btn-outline" id="pause-logs">⏸️ 暂停更新</button>
                        </div>
                    </div>

                    <div class="log-controls">
                        <div class="control-group">
                            <label>日志级别:</label>
                            <select class="form-select" id="log-level-filter">
                                <option value="">全部</option>
                                <option value="error">错误</option>
                                <option value="warn">警告</option>
                                <option value="info">信息</option>
                                <option value="debug">调试</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <input type="text" placeholder="搜索日志..." class="form-input" id="log-search">
                        </div>
                        <div class="control-group">
                            <label>自动刷新:</label>
                            <input type="checkbox" id="log-auto-refresh" checked>
                        </div>
                    </div>

                    <div class="log-container" id="log-container">
                        <div class="log-entry">
                            <span class="log-time">2024-12-12 12:00:00</span>
                            <span class="log-level log-info">INFO</span>
                            <span class="log-message">系统启动完成</span>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- 登录模态框 -->
    <div id="login-modal" class="modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-container">
            <div class="modal-header">
                <h3>🔐 系统登录</h3>
                <button class="modal-close" id="login-close">&times;</button>
            </div>
            <div class="modal-body">
                <form class="login-form" id="login-form">
                    <div class="form-group">
                        <label for="username">用户名</label>
                        <input type="text" id="username" name="username" value="admin" required placeholder="请输入用户名"
                            autocomplete="username">
                    </div>
                    <div class="form-group">
                        <label for="password">密码</label>
                        <div class="password-input">
                            <input type="password" id="password" name="password" value="admin" required
                                placeholder="请输入密码" autocomplete="current-password">
                            <button type="button" class="password-toggle" id="password-toggle">👁️</button>
                        </div>
                    </div>
                    <div class="form-options">
                        <label class="checkbox-label">
                            <input type="checkbox" id="remember-me">
                            <span class="checkmark"></span>
                            记住登录状态
                        </label>
                    </div>
                </form>

                <div class="login-info">
                    <div class="info-item">
                        <span class="info-icon">👤</span>
                        <div class="info-content">
                            <strong>默认账户</strong>
                            <p>用户名: admin / 密码: admin</p>
                        </div>
                    </div>
                    <div class="info-item">
                        <span class="info-icon">🔒</span>
                        <div class="info-content">
                            <strong>安全提示</strong>
                            <p>首次登录后请立即修改密码</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline" id="login-cancel">取消</button>
                <button type="submit" class="btn btn-primary" form="login-form" id="login-submit">
                    <span class="btn-text">登录</span>
                    <span class="btn-loading" style="display: none;">登录中...</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 添加域名模态框 -->
    <div id="add-domain-modal" class="modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-container">
            <div class="modal-header">
                <h3>➕ 添加域名</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form class="domain-form" id="add-domain-form">
                    <div class="form-group">
                        <label for="domain-name">域名</label>
                        <input type="text" id="domain-name" name="domain" required placeholder="example.com">
                    </div>
                    <!-- 域名方向选择 (必选) -->
                    <div class="form-group">
                        <label class="required">域名方向 <span class="required-mark">*</span></label>
                        <div class="radio-group domain-direction-selection">
                            <label class="radio-option">
                                <input type="radio" name="domain-direction" value="upstream" required>
                                <span class="radio-custom"></span>
                                <span class="radio-text">🔼 上游域名</span>
                                <span class="radio-desc">作为代理的目标服务器</span>
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="domain-direction" value="downstream" required>
                                <span class="radio-custom"></span>
                                <span class="radio-text">🔽 下游域名</span>
                                <span class="radio-desc">接收用户请求的域名</span>
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="upstream-url">上游地址</label>
                        <input type="url" id="upstream-url" name="upstream" required placeholder="https://upstream.com">
                    </div>
                    <div class="form-group">
                        <label for="domain-type">域名类型</label>
                        <select id="domain-type" name="type">
                            <option value="proxy">代理域名</option>
                            <option value="mirror">镜像域名</option>
                            <option value="redirect">重定向域名</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="domain-enabled" name="enabled" checked>
                            启用域名
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline modal-cancel">取消</button>
                <button type="submit" class="btn btn-primary" form="add-domain-form">添加域名</button>
            </div>
        </div>
    </div>

    <!-- 批量导入域名模态框 -->
    <div id="import-domains-modal" class="modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-container">
            <div class="modal-header">
                <h3>📥 批量导入域名</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="import-tabs">
                    <button class="tab-btn active" data-import-tab="text">文本导入</button>
                    <button class="tab-btn" data-import-tab="file">文件导入</button>
                    <button class="tab-btn" data-import-tab="template">模板下载</button>
                </div>

                <div class="import-content">
                    <div id="text-import" class="import-tab-content active">
                        <form id="text-import-form">
                            <div class="form-group">
                                <label>域名列表 (每行一个)</label>
                                <textarea id="domain-list" rows="10"
                                    placeholder="example1.com -> https://upstream1.com&#10;example2.com -> https://upstream2.com&#10;..."></textarea>
                            </div>
                            <div class="form-group">
                                <label>导入格式:</label>
                                <select id="import-format">
                                    <option value="simple">简单格式 (domain.com)</option>
                                    <option value="mapping">映射格式 (domain.com -> upstream.com)</option>
                                    <option value="json">JSON格式</option>
                                </select>
                            </div>
                        </form>
                    </div>

                    <div id="file-import" class="import-tab-content">
                        <form id="file-import-form">
                            <div class="form-group">
                                <label>选择文件</label>
                                <input type="file" id="import-file" accept=".txt,.csv,.json">
                                <div class="file-info">支持 .txt, .csv, .json 格式</div>
                            </div>
                        </form>
                    </div>

                    <div id="template-import" class="import-tab-content">
                        <div class="template-info">
                            <p>下载模板文件以确保正确的格式:</p>
                            <div class="template-buttons">
                                <button class="btn btn-outline" data-download="csv">CSV模板</button>
                                <button class="btn btn-outline" data-download="json">JSON模板</button>
                                <button class="btn btn-outline" data-download="txt">TXT模板</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline modal-cancel">取消</button>
                <button type="button" class="btn btn-primary" id="start-import">开始导入</button>
            </div>
        </div>
    </div>

    <!-- 批量添加模态框 -->
    <div id="batch-add-modal" class="modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-container large">
            <div class="modal-header">
                <h3>📦 批量添加到域名池</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="batch-options">
                    <div class="option-group">
                        <label>添加方式:</label>
                        <div class="radio-group">
                            <label><input type="radio" name="batch-type" value="manual" checked> 手动输入</label>
                            <label><input type="radio" name="batch-type" value="generate"> 自动生成</label>
                            <label><input type="radio" name="batch-type" value="import"> 文件导入</label>
                        </div>
                    </div>

                    <!-- 域名类型选择 (必选) -->
                    <div class="option-group">
                        <label class="required">域名类型 <span class="required-mark">*</span></label>
                        <div class="radio-group domain-type-selection">
                            <label class="radio-option">
                                <input type="radio" name="batch-domain-direction" value="upstream" required>
                                <span class="radio-custom"></span>
                                <span class="radio-text">🔼 上游域名</span>
                                <span class="radio-desc">作为代理的目标服务器</span>
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="batch-domain-direction" value="downstream" required>
                                <span class="radio-custom"></span>
                                <span class="radio-text">🔽 下游域名</span>
                                <span class="radio-desc">接收用户请求的域名</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="batch-content">
                    <div id="manual-batch" class="batch-tab-content active">
                        <textarea id="batch-domains" rows="15"
                            placeholder="输入域名列表，每行一个:&#10;domain1.com&#10;domain2.com&#10;domain3.com"></textarea>
                    </div>

                    <div id="generate-batch" class="batch-tab-content">
                        <div class="generate-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>前缀:</label>
                                    <input type="text" id="domain-prefix" placeholder="site">
                                </div>
                                <div class="form-group">
                                    <label>后缀:</label>
                                    <input type="text" id="domain-suffix" placeholder=".example.com">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>起始数字:</label>
                                    <input type="number" id="start-number" value="1" min="1">
                                </div>
                                <div class="form-group">
                                    <label>结束数字:</label>
                                    <input type="number" id="end-number" value="100" min="1">
                                </div>
                            </div>
                            <button type="button" class="btn btn-outline" id="generate-preview">预览生成</button>
                            <div id="generation-preview"></div>
                        </div>
                    </div>

                    <div id="import-batch" class="batch-tab-content">
                        <input type="file" id="batch-file" accept=".txt,.csv">
                        <div class="file-preview" id="batch-file-preview"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline modal-cancel">取消</button>
                <button type="button" class="btn btn-primary" id="confirm-batch-add">确认添加</button>
            </div>
        </div>
    </div>

    <!-- 启动任务模态框 -->
    <div id="start-task-modal" class="modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-container">
            <div class="modal-header">
                <h3>🚀 创建镜像任务</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="task-form">
                    <div class="form-group">
                        <label>任务名称:</label>
                        <input type="text" id="task-name" required placeholder="输入任务名称">
                    </div>

                    <div class="form-group">
                        <label>任务类型:</label>
                        <select id="task-type" required>
                            <option value="mirror">网站镜像</option>
                            <option value="proxy">代理转发</option>
                            <option value="redirect">重定向</option>
                            <option value="load-balance">负载均衡</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>源域名:</label>
                        <input type="text" id="source-domain" required placeholder="source.com">
                    </div>

                    <div class="form-group">
                        <label>目标域名:</label>
                        <input type="text" id="target-domain" required placeholder="target.com">
                    </div>

                    <div class="form-group">
                        <label>优先级:</label>
                        <select id="task-priority">
                            <option value="low">低</option>
                            <option value="normal" selected>普通</option>
                            <option value="high">高</option>
                            <option value="urgent">紧急</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="task-auto-start" checked>
                            创建后立即启动
                        </label>
                    </div>

                    <div class="form-group">
                        <label>任务描述:</label>
                        <textarea id="task-description" rows="3" placeholder="可选的任务描述"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline modal-cancel">取消</button>
                <button type="submit" class="btn btn-primary" form="task-form">创建任务</button>
            </div>
        </div>
    </div>

    <!-- 添加黑名单规则模态框 -->
    <div id="add-blacklist-modal" class="modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-container">
            <div class="modal-header">
                <h3>🚫 添加黑名单规则</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="blacklist-form">
                    <div class="form-group">
                        <label>规则类型:</label>
                        <select id="blacklist-type" required>
                            <option value="keyword">关键词</option>
                            <option value="domain">域名</option>
                            <option value="regex">正则表达式</option>
                            <option value="ip">IP地址</option>
                            <option value="country">国别代码</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>规则值:</label>
                        <input type="text" id="blacklist-value" required placeholder="输入规则值">
                        <div class="rule-help" id="rule-help">
                            输入要屏蔽的关键词
                        </div>
                    </div>

                    <div class="form-group">
                        <label>匹配范围:</label>
                        <div class="checkbox-group">
                            <label><input type="checkbox" name="scope" value="url" checked> URL</label>
                            <label><input type="checkbox" name="scope" value="title"> 标题</label>
                            <label><input type="checkbox" name="scope" value="content"> 内容</label>
                            <label><input type="checkbox" name="scope" value="headers"> 请求头</label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>动作:</label>
                        <select id="blacklist-action">
                            <option value="block">阻止访问</option>
                            <option value="redirect">重定向</option>
                            <option value="log">仅记录日志</option>
                        </select>
                    </div>

                    <div class="form-group" id="redirect-group" style="display: none;">
                        <label>重定向地址:</label>
                        <input type="url" id="redirect-url" placeholder="https://example.com">
                    </div>

                    <div class="form-group">
                        <label>描述:</label>
                        <textarea id="blacklist-description" rows="2" placeholder="规则描述(可选)"></textarea>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="blacklist-enabled" checked>
                            启用规则
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline modal-cancel">取消</button>
                <button type="submit" class="btn btn-primary" form="blacklist-form">添加规则</button>
            </div>
        </div>
    </div>

    <!-- 批量导入黑名单模态框 -->
    <div id="import-blacklist-modal" class="modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-container">
            <div class="modal-header">
                <h3>📥 批量导入黑名单</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="import-options">
                    <div class="option-group">
                        <label>导入方式:</label>
                        <div class="radio-group">
                            <label><input type="radio" name="import-method" value="text" checked> 文本输入</label>
                            <label><input type="radio" name="import-method" value="file"> 文件上传</label>
                            <label><input type="radio" name="import-method" value="url"> URL导入</label>
                        </div>
                    </div>
                </div>

                <div class="import-content">
                    <div id="text-blacklist-import" class="import-method-content active">
                        <textarea id="blacklist-text" rows="10"
                            placeholder="每行一个规则:&#10;keyword:spam&#10;domain:malicious.com&#10;regex:.*\\.suspicious\\..*&#10;ip:*************"></textarea>
                    </div>

                    <div id="file-blacklist-import" class="import-method-content">
                        <input type="file" id="blacklist-file" accept=".txt,.csv,.json">
                        <div class="file-info">支持 TXT、CSV、JSON 格式</div>
                    </div>

                    <div id="url-blacklist-import" class="import-method-content">
                        <input type="url" id="blacklist-url" placeholder="https://example.com/blacklist.txt">
                        <div class="url-info">从远程URL导入黑名单规则</div>
                    </div>
                </div>

                <div class="import-settings">
                    <div class="form-group">
                        <label>默认动作:</label>
                        <select id="default-action">
                            <option value="block">阻止访问</option>
                            <option value="log">仅记录日志</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="auto-enable" checked>
                            自动启用导入的规则
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline modal-cancel">取消</button>
                <button type="button" class="btn btn-primary" id="start-blacklist-import">开始导入</button>
            </div>
        </div>
    </div>

    <!-- 编辑域名模态框 -->
    <div id="edit-domain-modal" class="modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-container">
            <div class="modal-header">
                <h3>✏️ 编辑域名</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="edit-domain-form">
                    <input type="hidden" id="edit-domain-id">

                    <div class="form-group">
                        <label>域名:</label>
                        <input type="text" id="edit-domain-name" required>
                    </div>

                    <div class="form-group">
                        <label>上游地址:</label>
                        <input type="url" id="edit-upstream-url" required>
                    </div>

                    <div class="form-group">
                        <label>域名类型:</label>
                        <select id="edit-domain-type">
                            <option value="proxy">代理域名</option>
                            <option value="mirror">镜像域名</option>
                            <option value="redirect">重定向域名</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>状态:</label>
                        <select id="edit-domain-status">
                            <option value="active">活跃</option>
                            <option value="inactive">停用</option>
                            <option value="maintenance">维护中</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>描述:</label>
                        <textarea id="edit-domain-description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline modal-cancel">取消</button>
                <button type="submit" class="btn btn-primary" form="edit-domain-form">保存更改</button>
            </div>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div id="confirm-delete-modal" class="modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-container small">
            <div class="modal-header">
                <h3>⚠️ 确认删除</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="confirm-content">
                    <div class="confirm-icon">🗑️</div>
                    <p id="confirm-message">确定要删除这个项目吗？此操作无法撤销。</p>
                    <div id="confirm-details"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline modal-cancel">取消</button>
                <button type="button" class="btn btn-danger" id="confirm-delete">确认删除</button>
            </div>
        </div>
    </div>

    <!-- 系统设置模态框 -->
    <div id="settings-modal" class="modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-container large">
            <div class="modal-header">
                <h3>⚙️ 系统设置</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="settings-tabs">
                    <button class="tab-btn active" data-settings-tab="appearance">外观</button>
                    <button class="tab-btn" data-settings-tab="behavior">行为</button>
                    <button class="tab-btn" data-settings-tab="notifications">通知</button>
                    <button class="tab-btn" data-settings-tab="security">安全</button>
                    <button class="tab-btn" data-settings-tab="advanced">高级</button>
                </div>

                <div class="settings-content">
                    <div id="appearance-settings" class="settings-tab-content active">
                        <div class="setting-group">
                            <h4>主题设置</h4>
                            <div class="setting-item">
                                <label>主题模式:</label>
                                <select id="theme-mode">
                                    <option value="dark">深色模式</option>
                                    <option value="light">浅色模式</option>
                                    <option value="auto">跟随系统</option>
                                </select>
                            </div>
                            <div class="setting-item">
                                <label>界面缩放:</label>
                                <div class="scale-controls">
                                    <button type="button" class="btn btn-outline" id="settings-scale-down">-</button>
                                    <span id="settings-scale-display">100%</span>
                                    <button type="button" class="btn btn-outline" id="settings-scale-up">+</button>
                                </div>
                            </div>
                        </div>

                        <div class="setting-group">
                            <h4>布局设置</h4>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" id="compact-mode">
                                    紧凑模式
                                </label>
                            </div>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" id="sidebar-collapsed">
                                    默认折叠侧边栏
                                </label>
                            </div>
                        </div>
                    </div>

                    <div id="behavior-settings" class="settings-tab-content">
                        <div class="setting-group">
                            <h4>自动刷新</h4>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" id="auto-refresh-enabled" checked>
                                    启用自动刷新
                                </label>
                            </div>
                            <div class="setting-item">
                                <label>刷新间隔:</label>
                                <select id="refresh-interval">
                                    <option value="10">10秒</option>
                                    <option value="30" selected>30秒</option>
                                    <option value="60">1分钟</option>
                                    <option value="300">5分钟</option>
                                </select>
                            </div>
                        </div>

                        <div class="setting-group">
                            <h4>默认行为</h4>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" id="confirm-actions" checked>
                                    操作前显示确认对话框
                                </label>
                            </div>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" id="remember-filters">
                                    记住筛选条件
                                </label>
                            </div>
                        </div>
                    </div>

                    <div id="notifications-settings" class="settings-tab-content">
                        <div class="setting-group">
                            <h4>通知设置</h4>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" id="browser-notifications">
                                    浏览器通知
                                </label>
                            </div>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" id="sound-notifications">
                                    声音提醒
                                </label>
                            </div>
                        </div>

                        <div class="setting-group">
                            <h4>通知类型</h4>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" id="notify-errors" checked>
                                    错误通知
                                </label>
                            </div>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" id="notify-success">
                                    成功通知
                                </label>
                            </div>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" id="notify-warnings">
                                    警告通知
                                </label>
                            </div>
                        </div>
                    </div>

                    <div id="security-settings" class="settings-tab-content">
                        <div class="setting-group">
                            <h4>会话安全</h4>
                            <div class="setting-item">
                                <label>会话超时:</label>
                                <select id="session-timeout">
                                    <option value="15">15分钟</option>
                                    <option value="30" selected>30分钟</option>
                                    <option value="60">1小时</option>
                                    <option value="240">4小时</option>
                                </select>
                            </div>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" id="auto-logout">
                                    无活动时自动登出
                                </label>
                            </div>
                        </div>

                        <div class="setting-group">
                            <h4>访问控制</h4>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" id="ip-whitelist">
                                    启用IP白名单
                                </label>
                            </div>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" id="force-https">
                                    强制HTTPS
                                </label>
                            </div>
                        </div>
                    </div>

                    <div id="advanced-settings" class="settings-tab-content">
                        <div class="setting-group">
                            <h4>性能优化</h4>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" id="lazy-loading">
                                    懒加载
                                </label>
                            </div>
                            <div class="setting-item">
                                <label>缓存大小:</label>
                                <select id="cache-size">
                                    <option value="small">小 (10MB)</option>
                                    <option value="medium" selected>中 (50MB)</option>
                                    <option value="large">大 (100MB)</option>
                                </select>
                            </div>
                        </div>

                        <div class="setting-group">
                            <h4>调试选项</h4>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" id="debug-mode">
                                    调试模式
                                </label>
                            </div>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" id="verbose-logging">
                                    详细日志
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline" id="settings-reset">重置默认</button>
                <button type="button" class="btn btn-outline modal-cancel">取消</button>
                <button type="button" class="btn btn-primary" id="settings-save">保存设置</button>
            </div>
        </div>
    </div>

    <!-- 用户菜单模态框 -->
    <div id="user-menu-modal" class="modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-container small">
            <div class="modal-header">
                <h3>👤 用户菜单</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="user-menu-content">
                    <div class="user-profile">
                        <div class="profile-avatar">👤</div>
                        <div class="profile-info">
                            <div class="profile-name">管理员</div>
                            <div class="profile-role">系统管理员</div>
                        </div>
                    </div>

                    <div class="menu-actions">
                        <button class="menu-action-btn" data-action="change-password">
                            <span class="action-icon">🔑</span>
                            <span class="action-text">修改密码</span>
                        </button>
                        <button class="menu-action-btn" data-action="user-settings">
                            <span class="action-icon">⚙️</span>
                            <span class="action-text">用户设置</span>
                        </button>
                        <button class="menu-action-btn" data-action="activity-log">
                            <span class="action-icon">📝</span>
                            <span class="action-text">活动日志</span>
                        </button>
                        <hr class="menu-divider">
                        <button class="menu-action-btn" data-action="about">
                            <span class="action-icon">ℹ️</span>
                            <span class="action-text">关于系统</span>
                        </button>
                        <button class="menu-action-btn danger" data-action="logout">
                            <span class="action-icon">🚪</span>
                            <span class="action-text">退出登录</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 修改密码模态框 -->
    <div id="change-password-modal" class="modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-container">
            <div class="modal-header">
                <h3>🔑 修改密码</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="change-password-form">
                    <div class="form-group">
                        <label>当前密码:</label>
                        <div class="password-input">
                            <input type="password" id="current-password" required>
                            <button type="button" class="password-toggle">👁️</button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>新密码:</label>
                        <div class="password-input">
                            <input type="password" id="new-password" required minlength="6">
                            <button type="button" class="password-toggle">👁️</button>
                        </div>
                        <div class="password-strength" id="password-strength"></div>
                    </div>

                    <div class="form-group">
                        <label>确认新密码:</label>
                        <div class="password-input">
                            <input type="password" id="confirm-password" required>
                            <button type="button" class="password-toggle">👁️</button>
                        </div>
                    </div>

                    <div class="password-requirements">
                        <h5>密码要求:</h5>
                        <ul>
                            <li>至少6个字符</li>
                            <li>包含大小写字母</li>
                            <li>包含数字</li>
                            <li>包含特殊字符(推荐)</li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline modal-cancel">取消</button>
                <button type="submit" class="btn btn-primary" form="change-password-form">修改密码</button>
            </div>
        </div>
    </div>

    <!-- 关于系统模态框 -->
    <div id="about-modal" class="modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-container">
            <div class="modal-header">
                <h3>ℹ️ 关于 SM镜像系统</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="about-content">
                    <div class="about-logo">
                        <div class="logo-icon large">🪞</div>
                        <h2>SM镜像系统</h2>
                        <div class="version-info">
                            <span class="version">v2.0.0</span>
                            <span class="build">Build 20241212</span>
                        </div>
                    </div>

                    <div class="about-info">
                        <div class="info-section">
                            <h4>系统信息</h4>
                            <table class="info-table">
                                <tr>
                                    <td>版本:</td>
                                    <td>2.0.0</td>
                                </tr>
                                <tr>
                                    <td>构建日期:</td>
                                    <td>2024-12-12</td>
                                </tr>
                                <tr>
                                    <td>运行环境:</td>
                                    <td>Rust + Pingora</td>
                                </tr>
                                <tr>
                                    <td>数据库:</td>
                                    <td>SQLite</td>
                                </tr>
                            </table>
                        </div>

                        <div class="info-section">
                            <h4>功能特性</h4>
                            <ul>
                                <li>✅ 高性能反向代理</li>
                                <li>✅ 智能域名管理</li>
                                <li>✅ 域名池系统</li>
                                <li>✅ 实时性能监控</li>
                                <li>✅ 安全防护机制</li>
                                <li>✅ 响应式管理界面</li>
                            </ul>
                        </div>

                        <div class="info-section">
                            <h4>技术支持</h4>
                            <p>如遇问题，请查看文档或联系技术支持。</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline">查看文档</button>
                <button type="button" class="btn btn-primary modal-cancel">关闭</button>
            </div>
        </div>
    </div>

    <!-- 新建域名分组模态框 -->
    <div id="add-group-modal" class="modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-container">
            <div class="modal-header">
                <h3>📁 新建域名分组</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="add-group-form">
                    <div class="form-group">
                        <label for="group-name">分组名称 *</label>
                        <input type="text" id="group-name" name="name" required placeholder="输入分组名称，如：生产环境、测试环境">
                    </div>

                    <div class="form-group">
                        <label for="group-description">分组描述</label>
                        <textarea id="group-description" name="description" rows="3"
                            placeholder="描述该分组的用途和特点（可选）"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="group-domains">初始域名</label>
                        <textarea id="group-domains" name="domains" rows="4"
                            placeholder="每行一个域名，如：&#10;example.com&#10;test.example.com&#10;（可选，也可以创建后再添加）"></textarea>
                        <small class="form-help">每行输入一个域名，创建分组后也可以单独添加域名</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline modal-cancel">取消</button>
                <button type="button" class="btn btn-primary" id="save-group-btn">创建分组</button>
            </div>
        </div>
    </div>

    <!-- 编辑域名分组模态框 -->
    <div id="edit-group-modal" class="modal" style="display: none;">
        <div class="modal-overlay"></div>
        <div class="modal-container">
            <div class="modal-header">
                <h3>✏️ 编辑域名分组</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="edit-group-form">
                    <input type="hidden" id="edit-group-id" name="id">

                    <div class="form-group">
                        <label for="edit-group-name">分组名称 *</label>
                        <input type="text" id="edit-group-name" name="name" required>
                    </div>

                    <div class="form-group">
                        <label for="edit-group-description">分组描述</label>
                        <textarea id="edit-group-description" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="edit-group-active" name="is_active">
                            启用此分组
                        </label>
                        <small class="form-help">停用的分组不会参与域名解析</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline modal-cancel">取消</button>
                <button type="button" class="btn btn-primary" id="update-group-btn">保存更改</button>
            </div>
        </div>
    </div>

    <!-- 通知提示 -->
    <div id="notification-container" class="notification-container"></div>

    <!-- 隐藏的认证令牌输入框，供HTMX使用 -->
    <input type="hidden" id="auth-token" name="auth_token" data-auth-token value="disabled_auth_token">

    <!-- 加载脚本 -->
    <script src="/js/htmx.min.js?v=20241214-002"></script>
    <script>
        // 在加载其他脚本前设置认证状态，避免循环跳转
        console.log('🔓 预设认证状态 - 禁用认证系统');
        localStorage.setItem('auth_token', 'disabled_auth_token');
        localStorage.setItem('auth_user', JSON.stringify({
            username: 'admin',
            role: 'admin',
            authenticated: true
        }));

        // 创建一个简化的认证管理器，避免其他脚本报错
        window.authManager = {
            isAuthenticated: () => true,
            getToken: () => 'disabled_auth_token',
            getUser: () => ({ username: 'admin', role: 'admin' }),
            login: () => Promise.resolve(true),
            logout: () => console.log('登出功能已禁用')
        };
    </script>
    <script src="/js/theme-control.js?v=20241214-006"></script>
    <script src="/js/user-auth.js?v=20241214-006"></script>
    <script src="/js/auth.js?v=20241214-006"></script>
    <script src="/js/main.js?v=20241214-006"></script>
    <script src="/js/components.js?v=20241214-009"></script>
    <script src="/js/sidebar.js?v=20241214-008"></script>
    <script src="/js/dashboard.js?v=20241214-006"></script>
    <script src="/js/domains.js?v=20241214-006"></script>
    <script src="/js/domain-groups.js?v=20241214-001"></script>
    <script src="/js/demo-data.js?v=20241214-001"></script>
    <script src="/js/api-integration.js?v=20241214-001"></script>
    <script src="/js/recursive-proxy.js?v=20241214-001"></script>
    <script src="/js/app.js?v=20241214-006"></script>
</body>

</html>