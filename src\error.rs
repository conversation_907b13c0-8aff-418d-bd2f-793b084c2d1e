//! 应用层错误处理 - 简化版本
use anyhow::Result;

/// 应用特定的错误类型 - 简化版本
#[derive(thiserror::Error, Debug)]
pub enum AppError {
    #[error("应用配置错误: {0}")]
    AppConfig(String),

    #[error("环境变量错误: {0}")]
    Environment(String),

    #[error("启动错误: {0}")]
    Startup(String),
}

// 简化的错误转换
impl From<AppError> for anyhow::Error {
    fn from(err: AppError) -> Self {
        anyhow::anyhow!("{}", err)
    }
}

// 简化的安全错误转换
impl From<crate::security::SecurityError> for anyhow::Error {
    fn from(err: crate::security::SecurityError) -> Self {
        anyhow::anyhow!("安全错误: {}", err)
    }
}

// 便利函数 - 创建应用特定的错误
impl AppError {
    pub fn app_config(msg: impl Into<String>) -> Self {
        Self::AppConfig(msg.into())
    }

    pub fn environment(msg: impl Into<String>) -> Self {
        Self::Environment(msg.into())
    }

    pub fn startup(msg: impl Into<String>) -> Self {
        Self::Startup(msg.into())
    }
}

// 为向后兼容保留的类型别名
pub type AppResult<T> = Result<T>;

/// 便利宏 - 简化错误转换
#[macro_export]
macro_rules! app_error {
    (config: $msg:expr) => {
        AppError::app_config($msg)
    };
    (env: $msg:expr) => {
        AppError::environment($msg)
    };
    (startup: $msg:expr) => {
        AppError::startup($msg)
    };
    ($msg:expr) => {
        AppError::app_config($msg)
    };
}
