use axum::{
    extract::Extension,
    response::{Html, IntoResponse},
    Json,
};
use chrono::{TimeZone, Utc};
use serde::Serialize;
use std::fs::File;
use std::io::BufReader;
use std::sync::Arc;

use crate::api::ApiResponse;
use crate::types::AppState;
#[derive(Serialize)]
pub struct CertStatus {
    pub domain: String,
    pub not_before: chrono::DateTime<chrono::Utc>,
    pub not_after: chrono::DateTime<chrono::Utc>,
    pub days_left: i64,
    pub status: String,
}

/// 查询证书状态（真实实现，从 tls.rs 获取证书信息）
pub async fn get_cert_status(Extension(state): Extension<Arc<AppState>>) -> impl IntoResponse {
    let cert_path = "certs/cert.pem";
    let mut certs = Vec::new();
    let now = Utc::now();

    // 尝试读取证书文件
    match File::open(cert_path) {
        Ok(cert_file) => {
            let mut reader = BufReader::new(cert_file);
            // 修复：rustls_pemfile::certs 返回迭代器，需要正确处理
            #[cfg(feature = "tls-client")]
            let cert_iter = rustls_pemfile::certs(&mut reader);
            #[cfg(not(feature = "tls-client"))]
            let cert_iter: Result<Vec<Vec<u8>>, std::io::Error> = Ok(vec![]);
            let mut has_certs = false;

            #[cfg(feature = "tls-client")]
            {
                for cert_result in cert_iter {
                    match cert_result {
                        Ok(cert) => {
                            has_certs = true;
                            // 解析X.509证书
                            if let Ok(x509) = x509_parser::parse_x509_certificate(&cert) {
                                let tbs = &x509.1.tbs_certificate;
                                // 转换为 chrono DateTime
                                let not_before = match Utc
                                    .timestamp_opt(tbs.validity.not_before.timestamp(), 0)
                                {
                                    chrono::LocalResult::Single(dt) => dt,
                                    _ => continue, // 跳过无效的时间戳
                                };
                                let not_after = match Utc
                                    .timestamp_opt(tbs.validity.not_after.timestamp(), 0)
                                {
                                    chrono::LocalResult::Single(dt) => dt,
                                    _ => continue, // 跳过无效的时间戳
                                };
                                let days_left = (not_after - now).num_days();
                                let status = if days_left > 0 { "有效" } else { "已过期" };
                                let domain = tbs.subject.to_string();
                                certs.push(CertStatus {
                                    domain,
                                    not_before,
                                    not_after,
                                    days_left,
                                    status: status.to_string(),
                                });
                            }
                        }
                        Err(_) => continue,
                    }
                }
            }
            #[cfg(not(feature = "tls-client"))]
            {
                // 简化版本，不解析证书，使用默认值
                has_certs = true;
                certs.push(CertStatus {
                    domain: "localhost".to_string(),
                    not_before: now,
                    not_after: now + chrono::Duration::days(365),
                    days_left: 365,
                    status: "未解析".to_string(),
                });
            }

            if !has_certs {
                certs.push(CertStatus {
                    domain: "localhost".to_string(),
                    not_before: now,
                    not_after: now,
                    days_left: 0,
                    status: "无效/无法解析证书".to_string(),
                });
            }
        }
        Err(_) => {
            certs.push(CertStatus {
                domain: "localhost".to_string(),
                not_before: now,
                not_after: now,
                days_left: 0,
                status: "未找到证书文件".to_string(),
            });
        }
    }

    Json(ApiResponse {
        success: true,
        message: Some("证书状态获取成功".to_string()),
        data: Some(certs),
        error: None,
        request_id: None,
        error_code: None,
    })
}

/// htmx专用：返回HTML片段，展示证书状态
pub async fn cert_status_htmx(Extension(state): Extension<Arc<AppState>>) -> Html<String> {
    let cert_path = "certs/cert.pem";
    let mut html = String::from("<table><tr><th>域名</th><th>生效时间</th><th>过期时间</th><th>剩余天数</th><th>状态</th></tr>");
    let now = chrono::Utc::now();

    match std::fs::File::open(cert_path) {
        Ok(cert_file) => {
            let mut reader = std::io::BufReader::new(cert_file);
            #[cfg(feature = "tls-client")]
            let cert_iter = rustls_pemfile::certs(&mut reader);
            #[cfg(not(feature = "tls-client"))]
            let cert_iter: Result<Vec<Vec<u8>>, std::io::Error> = Ok(vec![]);

            let mut has_certs = false;

            #[cfg(feature = "tls-client")]
            {
                for cert_result in cert_iter {
                    match cert_result {
                        Ok(cert) => {
                            has_certs = true;
                            if let Ok(x509) = x509_parser::parse_x509_certificate(&cert) {
                                let tbs = &x509.1.tbs_certificate;
                                let not_before = match Utc
                                    .timestamp_opt(tbs.validity.not_before.timestamp(), 0)
                                {
                                    chrono::LocalResult::Single(dt) => dt,
                                    _ => continue, // 跳过无效的时间戳
                                };
                                let not_after = match Utc
                                    .timestamp_opt(tbs.validity.not_after.timestamp(), 0)
                                {
                                    chrono::LocalResult::Single(dt) => dt,
                                    _ => continue, // 跳过无效的时间戳
                                };
                                let days_left = (not_after - now).num_days();
                                let status = if days_left > 0 { "有效" } else { "已过期" };
                                let domain = tbs.subject.to_string();
                                html += &format!(
                                "<tr><td>{}</td><td>{}</td><td>{}</td><td>{}</td><td>{}</td></tr>",
                                domain,
                                not_before.format("%Y-%m-%d %H:%M"),
                                not_after.format("%Y-%m-%d %H:%M"),
                                days_left,
                                status
                            );
                            }
                        }
                        Err(_) => continue,
                    }
                }
            }
            #[cfg(not(feature = "tls-client"))]
            {
                // 简化版本，不解析证书
                has_certs = true;
                html += "<tr><td colspan='5'>证书解析功能未启用</td></tr>";
            }

            if !has_certs {
                html += "<tr><td colspan='5'>证书无效/无法解析</td></tr>";
            }
        }
        Err(_) => {
            html += "<tr><td colspan='5'>未找到证书文件</td></tr>";
        }
    }
    html += "</table>";
    Html(html)
}

// 上传证书（仅示例，实际应保存文件，这里只返回未实现）
pub async fn upload_certificate(Extension(_state): Extension<Arc<AppState>>) -> impl IntoResponse {
    axum::Json(ApiResponse::<()> {
        success: false,
        message: Some("证书上传未实现".to_string()),
        data: None,
        error: Some("NotImplemented".to_string()),
        request_id: None,
        error_code: None,
    })
}

// 获取单个证书（同 list，假定只支持单证书）
pub async fn get_certificate(Extension(state): Extension<Arc<AppState>>) -> impl IntoResponse {
    get_cert_status(Extension(state)).await
}

// 删除证书（仅示例，实际应删除文件，这里只返回未实现）
pub async fn delete_certificate(Extension(_state): Extension<Arc<AppState>>) -> impl IntoResponse {
    axum::Json(ApiResponse::<()> {
        success: false,
        message: Some("证书删除未实现".to_string()),
        data: None,
        error: Some("NotImplemented".to_string()),
        request_id: None,
        error_code: None,
    })
}

// 生成自签名证书（仅示例，实际应生成证书，这里只返回未实现）
pub async fn generate_self_signed(
    Extension(_state): Extension<Arc<AppState>>,
) -> impl IntoResponse {
    axum::Json(ApiResponse::<()> {
        success: false,
        message: Some("自签名证书生成未实现".to_string()),
        data: None,
        error: Some("NotImplemented".to_string()),
        request_id: None,
        error_code: None,
    })
}

// 列出所有证书（假定只支持单证书，返回 get_cert_status 结果）
pub async fn list_certificates(Extension(state): Extension<Arc<AppState>>) -> impl IntoResponse {
    get_cert_status(Extension(state)).await
}

pub fn routes() -> axum::Router<Arc<AppState>> {
    axum::Router::new()
        .route("/api/cert/status", axum::routing::get(get_cert_status))
        .route("/api/cert/upload", axum::routing::post(upload_certificate))
        .route("/api/cert/:id", axum::routing::get(get_certificate))
        .route("/api/cert/:id", axum::routing::delete(delete_certificate))
        .route(
            "/api/cert/generate_self_signed",
            axum::routing::post(generate_self_signed),
        )
        .route(
            "/api/cert/status_htmx",
            axum::routing::get(cert_status_htmx),
        )
        .route("/api/cert/list", axum::routing::get(list_certificates))
}
