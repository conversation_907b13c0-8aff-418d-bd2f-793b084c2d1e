use axum::{
    extract::{ConnectInfo, Request},
    http::{HeaderMap, StatusCode},
    middleware::Next,
    response::Response,
};
use std::net::SocketAddr;
use tracing::{debug, warn};

use crate::config::{AccessControlConfig, ApiAccessConfig};

/// IP访问控制中间件
pub async fn ip_access_control_middleware(
    ConnectInfo(addr): ConnectInfo<SocketAddr>,
    headers: HeaderMap,
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let client_ip = get_client_ip(&addr, &headers);
    let path = request.uri().path();
    
    debug!("访问控制检查: IP={}, Path={}", client_ip, path);

    // 这里应该从配置中获取访问控制规则
    // 暂时使用硬编码的规则作为示例
    let api_config = get_default_api_access_config();
    
    // 检查是否是内部IP
    if is_internal_ip(&client_ip, &api_config.internal_ips) {
        debug!("内部IP访问: {}", client_ip);
        return Ok(next.run(request).await);
    }
    
    // 检查是否是免认证的内部端点
    if is_internal_endpoint(path, &api_config.internal_endpoints) {
        debug!("内部端点访问: {}", path);
        return Ok(next.run(request).await);
    }
    
    // 外部访问需要认证
    if api_config.require_auth_for_external {
        // 检查是否有有效的认证令牌
        if !has_valid_auth_token(&headers) {
            warn!("外部IP未认证访问被拒绝: IP={}, Path={}", client_ip, path);
            return Err(StatusCode::UNAUTHORIZED);
        }
    }
    
    debug!("访问控制通过: IP={}, Path={}", client_ip, path);
    Ok(next.run(request).await)
}

/// 获取客户端真实IP
fn get_client_ip(addr: &SocketAddr, headers: &HeaderMap) -> String {
    // 优先检查代理头
    if let Some(forwarded_for) = headers.get("x-forwarded-for") {
        if let Ok(forwarded_str) = forwarded_for.to_str() {
            if let Some(first_ip) = forwarded_str.split(',').next() {
                return first_ip.trim().to_string();
            }
        }
    }
    
    if let Some(real_ip) = headers.get("x-real-ip") {
        if let Ok(real_ip_str) = real_ip.to_str() {
            return real_ip_str.to_string();
        }
    }
    
    // 回退到连接IP
    addr.ip().to_string()
}

/// 检查是否是内部IP
fn is_internal_ip(client_ip: &str, internal_ips: &[String]) -> bool {
    // 检查精确匹配
    if internal_ips.contains(&client_ip.to_string()) {
        return true;
    }
    
    // 检查localhost变体
    if client_ip == "127.0.0.1" || client_ip == "::1" || client_ip == "localhost" {
        return true;
    }
    
    // 检查私有网络范围
    if is_private_ip(client_ip) {
        return true;
    }
    
    false
}

/// 检查是否是私有IP地址
fn is_private_ip(ip: &str) -> bool {
    if let Ok(parsed_ip) = ip.parse::<std::net::IpAddr>() {
        match parsed_ip {
            std::net::IpAddr::V4(ipv4) => {
                let octets = ipv4.octets();
                // 10.0.0.0/8
                if octets[0] == 10 {
                    return true;
                }
                // **********/12
                if octets[0] == 172 && (octets[1] >= 16 && octets[1] <= 31) {
                    return true;
                }
                // ***********/16
                if octets[0] == 192 && octets[1] == 168 {
                    return true;
                }
            }
            std::net::IpAddr::V6(_) => {
                // IPv6私有地址检查可以在这里添加
                return false;
            }
        }
    }
    false
}

/// 检查是否是内部端点
fn is_internal_endpoint(path: &str, internal_endpoints: &[String]) -> bool {
    internal_endpoints.iter().any(|endpoint| path.starts_with(endpoint))
}

/// 检查是否有有效的认证令牌
fn has_valid_auth_token(headers: &HeaderMap) -> bool {
    // 检查Authorization头
    if let Some(auth_header) = headers.get("authorization") {
        if let Ok(auth_str) = auth_header.to_str() {
            // 简单检查是否有Bearer token
            if auth_str.starts_with("Bearer ") && auth_str.len() > 7 {
                // 这里应该验证JWT token的有效性
                // 暂时简单检查格式
                return true;
            }
        }
    }
    
    // 检查Cookie中的session
    if let Some(cookie_header) = headers.get("cookie") {
        if let Ok(cookie_str) = cookie_header.to_str() {
            // 检查是否有session cookie
            if cookie_str.contains("session=") || cookie_str.contains("auth_token=") {
                return true;
            }
        }
    }
    
    false
}

/// 获取默认的API访问配置
fn get_default_api_access_config() -> ApiAccessConfig {
    ApiAccessConfig {
        internal_ips: vec![
            "127.0.0.1".to_string(),
            "::1".to_string(),
            "localhost".to_string(),
        ],
        require_auth_for_external: true,
        internal_endpoints: vec![
            "/health".to_string(),
            "/metrics".to_string(),
            "/status".to_string(),
        ],
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_is_internal_ip() {
        let internal_ips = vec!["127.0.0.1".to_string(), "localhost".to_string()];
        
        assert!(is_internal_ip("127.0.0.1", &internal_ips));
        assert!(is_internal_ip("::1", &internal_ips));
        assert!(is_internal_ip("localhost", &internal_ips));
        assert!(!is_internal_ip("*******", &internal_ips));
    }

    #[test]
    fn test_is_private_ip() {
        assert!(is_private_ip("********"));
        assert!(is_private_ip("**********"));
        assert!(is_private_ip("***********"));
        assert!(!is_private_ip("*******"));
        assert!(!is_private_ip("*******"));
    }

    #[test]
    fn test_is_internal_endpoint() {
        let internal_endpoints = vec!["/health".to_string(), "/metrics".to_string()];
        
        assert!(is_internal_endpoint("/health", &internal_endpoints));
        assert!(is_internal_endpoint("/health/check", &internal_endpoints));
        assert!(is_internal_endpoint("/metrics", &internal_endpoints));
        assert!(!is_internal_endpoint("/api/users", &internal_endpoints));
    }
}
