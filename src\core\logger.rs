//! 应用层日志配置 - 简化版本

use anyhow::Result;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

/// 设置简单的日志系统
pub fn setup_logging() -> Result<()> {
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "sm=info".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();
    Ok(())
}
