/*!
# API模块

整合了原来 proxy-api crate 的所有功能：
- REST API 接口
- 中间件
- 错误处理
- HTTP响应转换
*/

use crate::{ProxyError, Result};
use axum::{
    extract::State,
    http::StatusCode,
    response::{IntoResponse, Json},
    routing::get,
    Router,
};
use axum_extra::extract::TypedHeader;
use chrono::Utc;
use headers::{authorization::Bearer, Authorization};
use proxy_config::{
    unified::ProxyConfig,
    validation::{ConfigValidator, FieldAccessor},
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::net::TcpListener;
// 统一使用 parking_lot::RwLock 避免锁竞争
use parking_lot::RwLock;

/// API服务
pub struct ApiService {
    pub router: Router, // 将router字段设为公开
    listen_addr: String,
    config: Arc<RwLock<ProxyConfig>>,
}

/// API响应结构
#[derive(Serialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
        }
    }

    pub fn error<S: Into<String>>(message: S) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(message.into()),
        }
    }
}

/// 系统状态信息
#[derive(Serialize)]
pub struct SystemStatus {
    pub status: String,
    pub uptime: u64,
    pub version: String,
    pub active_connections: u32,
}

/// 配置更新请求
#[derive(Deserialize)]
pub struct ConfigUpdateRequest {
    pub key: String,
    pub value: serde_json::Value,
}

impl ApiService {
    pub fn new(listen_addr: String, config: Arc<RwLock<ProxyConfig>>) -> Self {
        let router = Router::new()
            .route("/health", get(health_check))
            .route("/status", get(system_status))
            .route("/metrics", get(get_metrics))
            .route("/config", get(get_config).post(update_config))
            .with_state(config.clone())
            .layer(
                tower::ServiceBuilder::new()
                    .layer(tower_http::trace::TraceLayer::new_for_http())
                    .layer(tower_http::cors::CorsLayer::permissive()),
            );

        Self {
            router,
            listen_addr,
            config,
        }
    }

    /// 启动API服务
    pub async fn start(&self) -> Result<()> {
        let listener = TcpListener::bind(&self.listen_addr)
            .await
            .map_err(|e| ProxyError::config_with_source("无法绑定API监听地址", e))?;

        tracing::info!("API服务启动在: {}", self.listen_addr);

        axum::serve(listener, self.router.clone())
            .await
            .map_err(|e| ProxyError::config_with_source("API服务启动失败", e))?;

        Ok(())
    }
}

/// 健康检查端点
async fn health_check() -> impl IntoResponse {
    let mut map = HashMap::new();
    map.insert("status", "healthy".to_string());
    map.insert("timestamp", Utc::now().to_rfc3339());
    (StatusCode::OK, Json(ApiResponse::success(map)))
}

/// 系统状态端点
async fn system_status() -> impl IntoResponse {
    let status = SystemStatus {
        status: "running".to_string(),
        uptime: 3600,
        version: env!("CARGO_PKG_VERSION").to_string(),
        active_connections: 42,
    };
    (StatusCode::OK, Json(ApiResponse::success(status)))
}

/// 获取指标端点
async fn get_metrics() -> impl IntoResponse {
    let metrics = HashMap::from([
        ("http_requests_total", 1234u64),
        ("active_connections", 42u64),
        ("cache_hit_ratio", 85u64),
    ]);
    (StatusCode::OK, Json(ApiResponse::success(metrics)))
}

/// 获取配置端点
async fn get_config(state: State<Arc<RwLock<ProxyConfig>>>) -> impl IntoResponse {
    let config = state.0.read().clone();
    (StatusCode::OK, Json(ApiResponse::success(config)))
}

/// 更新配置端点
async fn update_config(
    state: State<Arc<RwLock<ProxyConfig>>>,
    TypedHeader(_auth): TypedHeader<Authorization<Bearer>>,
    Json(request): Json<ConfigUpdateRequest>,
) -> impl IntoResponse {
    // 权限校验（略，见前述伪代码）
    // 克隆当前配置，尝试变更
    let mut new_config = {
        let cfg = state.0.read();
        cfg.clone()
    };
    let value_str = request.value.to_string();
    if let Err(e) = FieldAccessor::set_field_value(&mut new_config, &request.key, &value_str) {
        return (
            StatusCode::BAD_REQUEST,
            Json(ApiResponse::<()>::error(format!("配置项设置失败: {}", e))),
        );
    }
    if let Err(errors) = ConfigValidator::validate_config(&new_config) {
        return (
            StatusCode::BAD_REQUEST,
            Json(ApiResponse::<()>::error(format!(
                "配置校验失败: {}",
                errors.join("; ")
            ))),
        );
    }

    // 更新成功，写入新配置
    *state.0.write() = new_config;

    (StatusCode::OK, Json(ApiResponse::success(())))
}
