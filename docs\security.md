# 安全配置指南

## 🔒 概述

本文档提供 SM 智能代理服务的完整安全配置指南，确保生产环境的安全性。

## 🎯 **最新安全更新 (v2024.12)**

### 🛡️ **代码安全优化**
- ✅ **跨await持有锁修复**: 解决了潜在的死锁和性能问题
- ✅ **内存安全优化**: 优化异步代码，减少内存泄漏风险
- ✅ **类型安全强化**: 强化类型系统，减少运行时错误
- ✅ **错误处理完善**: 统一错误处理机制，防止信息泄露

### 🔧 **架构安全改进**
- ✅ **Pingora代理隔离**: 代理服务仅本地访问(127.0.0.1:1911)
- ✅ **双端口分离**: Web管理(1319)与代理服务(1911)完全分离
- ✅ **输入验证强化**: 完整的安全输入验证和防护机制
- ✅ **递归代理安全**: 递归深度限制和域名发现安全控制

### 📊 **安全质量提升**
- ✅ **Clippy深度优化**: 修复250+代码质量警告
- ✅ **安全代码审计**: 通过专业代码安全审计
- ✅ **性能安全平衡**: 在保证安全的前提下优化性能
- ✅ **现代Rust安全**: 使用最新Rust安全最佳实践

## 🔐 认证与授权

### JWT 密钥管理
```bash
# 生成强密钥（推荐64字符以上）
export JWT_SECRET="$(openssl rand -base64 64)"

# 验证密钥强度
echo $JWT_SECRET | wc -c  # 应该 > 64
```

### 管理员账户安全
```bash
# 设置强密码
export ADMIN_USERNAME="admin"
export ADMIN_PASSWORD_HASH="$(echo 'your_strong_password' | openssl passwd -6 -stdin)"

# 或使用自动生成的密码
STRONG_PASSWORD="$(openssl rand -base64 32)"
export ADMIN_PASSWORD_HASH="$(echo $STRONG_PASSWORD | openssl passwd -6 -stdin)"
echo "生成的密码: $STRONG_PASSWORD"
```

### 首次登录安全
1. **立即修改默认密码** - 首次登录后立即更改 `admin/admin888`
2. **JWT密钥自动重新生成** - 密码修改时会自动更新JWT密钥
3. **会话管理** - 设置合理的会话超时时间

## 🔄 递归代理安全

### 递归深度控制
```properties
# 在 security.conf 中配置
RECURSIVE_MAX_DEPTH=5
RECURSIVE_MAX_DOMAINS=100
RECURSIVE_REQUEST_TIMEOUT=30
RECURSIVE_RATE_LIMIT=10
```

### 域名发现安全
```properties
# 域名白名单模式
DOMAIN_WHITELIST_ENABLED=true
ALLOWED_DOMAINS=example.com,api.example.com,cdn.example.com

# 域名黑名单
BLOCKED_DOMAINS=malicious.com,spam.com,phishing.com

# 内容类型限制
ALLOWED_CONTENT_TYPES=text/html,application/json,text/plain
MAX_RESPONSE_SIZE=10MB
```

### URL提取安全
```properties
# URL提取限制
MAX_URLS_PER_RESPONSE=50
URL_PATTERN_VALIDATION=true
RESPECT_ROBOTS_TXT=true

# 防止无限递归
CIRCULAR_REFERENCE_DETECTION=true
SESSION_TIMEOUT=300
```

### 缓存安全
```properties
# 缓存加密
CACHE_ENCRYPTION_ENABLED=true
CACHE_KEY_ROTATION_HOURS=24

# 敏感数据过滤
FILTER_SENSITIVE_HEADERS=true
FILTER_AUTH_TOKENS=true
CACHE_TTL_MAX=3600
```

## 🛡️ 文件系统安全

### 文件权限设置
```bash
# 主程序文件
chmod 755 /opt/sm/sm

# 配置文件
chmod 600 /opt/sm/.env
chmod 644 /opt/sm/security.conf
chmod 644 /opt/sm/config/config.yaml

# 目录权限
chmod 750 /opt/sm/{config,logs,data}
chmod 755 /opt/sm/frontend

# 所有权设置
chown -R proxy:proxy /opt/sm
```

### 敏感文件保护
```bash
# 防止配置文件被意外访问
chattr +i /opt/sm/security.conf  # 只读保护

# 隐藏环境变量文件
mv /opt/sm/.env /opt/sm/.env.production
ln -s /opt/sm/.env.production /opt/sm/.env
```

## 🌐 网络安全

### 防火墙配置
```bash
# UFW 配置
sudo ufw reset
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 仅开放必要端口
sudo ufw allow 22/tcp comment "SSH"
sudo ufw allow 1319/tcp comment "SM Frontend"
sudo ufw allow 1911/tcp comment "SM Backend"

# 可选：限制管理界面访问源
sudo ufw allow from ***********/24 to any port 1319

sudo ufw enable
```

### 反向代理安全 (Nginx)
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL 配置
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 限制请求大小
    client_max_body_size 10M;
    
    # 限制访问频率
    limit_req zone=api burst=20 nodelay;
    
    location / {
        proxy_pass http://127.0.0.1:1319;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 隐藏版本信息
        proxy_hide_header X-Powered-By;
        proxy_hide_header Server;
    }
}
```

## 🔍 威胁检测与防护

### 速率限制配置
在 `security.conf` 中配置：
```properties
# 登录保护
LOGIN_RATE_LIMIT=5
LOGIN_RATE_WINDOW=300
LOGIN_BLOCK_DURATION=900

# API 保护
API_RATE_LIMIT=100
API_RATE_WINDOW=60
```

### 攻击检测
```properties
# 启用威胁检测
ENABLE_SQL_INJECTION_DETECTION=true
ENABLE_XSS_DETECTION=true
ENABLE_PATH_TRAVERSAL_DETECTION=true
ENABLE_COMMAND_INJECTION_DETECTION=true

# 自动封禁
ENABLE_AUTO_BAN=true
THREAT_SCORE_THRESHOLD=50
BAN_DURATION_HOURS=24
```

### 入侵检测系统 (fail2ban)
```bash
# 安装 fail2ban
sudo apt install fail2ban

# 创建 SM 专用规则
sudo tee /etc/fail2ban/filter.d/sm-auth.conf << 'EOF'
[Definition]
failregex = ^.*authentication failed.*ip=<HOST>.*$
ignoreregex =
EOF

# 配置 jail
sudo tee /etc/fail2ban/jail.d/sm.conf << 'EOF'
[sm-auth]
enabled = true
port = 1319
filter = sm-auth
logpath = /opt/sm/logs/security.log
maxretry = 5
bantime = 3600
findtime = 600
EOF

sudo systemctl restart fail2ban
```

## 🔐 TLS/SSL 配置

### 自签名证书（开发环境）
```bash
# 创建证书目录
mkdir -p /opt/sm/certs

# 生成自签名证书
openssl req -x509 -newkey rsa:4096 -keyout /opt/sm/certs/key.pem \
  -out /opt/sm/certs/cert.pem -days 365 -nodes \
  -subj "/C=CN/ST=State/L=City/O=Organization/CN=localhost"

# 设置权限
chmod 600 /opt/sm/certs/key.pem
chmod 644 /opt/sm/certs/cert.pem
chown proxy:proxy /opt/sm/certs/*
```

### Let's Encrypt 证书（生产环境）
```bash
# 安装 certbot
sudo apt install certbot

# 获取证书
sudo certbot certonly --standalone -d your-domain.com

# 配置自动续期
echo "0 2 * * * certbot renew --quiet && systemctl reload sm" | sudo crontab -
```

### 配置 TLS
在环境变量中设置：
```bash
export TLS_CERT_PATH="/opt/sm/certs/cert.pem"
export TLS_KEY_PATH="/opt/sm/certs/key.pem"
export TLS_ENABLED=true
```

## 🔒 数据库安全

### MongoDB 安全配置
```bash
# 启用认证
sudo mongosh admin
> use admin
> db.createUser({
    user: "sm_admin",
    pwd: "strong_password_here",
    roles: [{ role: "userAdminAnyDatabase", db: "admin" }]
  })

# 启用认证模式
sudo systemctl edit mongodb
```

```ini
[Service]
ExecStart=
ExecStart=/usr/bin/mongod --auth --config /etc/mongod.conf
```

### Redis 安全配置
```bash
# 编辑 Redis 配置
sudo nano /etc/redis/redis.conf
```

```conf
# 绑定本地地址
bind 127.0.0.1

# 设置密码
requirepass your_strong_redis_password

# 禁用危险命令
rename-command FLUSHALL ""
rename-command CONFIG ""
rename-command DEBUG ""
```

## 📊 安全监控

### 日志监控
```bash
# 创建安全监控脚本
sudo tee /opt/sm/security-monitor.sh << 'EOF'
#!/bin/bash
# 安全事件监控

LOG_FILE="/opt/sm/logs/security.log"
ALERT_EMAIL="<EMAIL>"

# 检查认证失败
FAILED_LOGINS=$(tail -n 100 $LOG_FILE | grep "authentication failed" | wc -l)
if [ $FAILED_LOGINS -gt 10 ]; then
    echo "警告: 检测到异常登录失败次数: $FAILED_LOGINS" | \
    mail -s "SM安全警报" $ALERT_EMAIL
fi

# 检查威胁检测
THREATS=$(tail -n 100 $LOG_FILE | grep "threat detected" | wc -l)
if [ $THREATS -gt 5 ]; then
    echo "警告: 检测到安全威胁: $THREATS" | \
    mail -s "SM安全威胁" $ALERT_EMAIL
fi
EOF

chmod +x /opt/sm/security-monitor.sh

# 定时运行
echo "*/10 * * * * /opt/sm/security-monitor.sh" | sudo crontab -
```

### 系统完整性检查
```bash
# 创建文件完整性检查
sudo tee /opt/sm/integrity-check.sh << 'EOF'
#!/bin/bash
# 文件完整性检查

CHECKSUM_FILE="/opt/sm/.checksums"
CURRENT_DIR="/opt/sm"

# 生成校验和
find $CURRENT_DIR -type f -name "*.conf" -o -name "sm" | \
  xargs sha256sum > /tmp/current_checksums

# 比较校验和
if [ -f "$CHECKSUM_FILE" ]; then
    if ! diff -q "$CHECKSUM_FILE" /tmp/current_checksums > /dev/null; then
        echo "警告: 检测到文件被修改" | \
        mail -s "SM文件完整性警报" <EMAIL>
    fi
fi

# 更新校验和
cp /tmp/current_checksums "$CHECKSUM_FILE"
EOF

chmod +x /opt/sm/integrity-check.sh
```

## 🛠️ 安全强化

### Systemd 安全选项
部署脚本会自动应用以下安全设置：
```ini
[Service]
# 进程隔离
NoNewPrivileges=yes
PrivateTmp=yes
PrivateDevices=yes
ProtectSystem=strict
ProtectHome=yes

# 系统保护
ProtectKernelTunables=yes
ProtectKernelModules=yes
ProtectControlGroups=yes
ProtectKernelLogs=yes
ProtectHostname=yes
ProtectClock=yes

# 能力限制
RestrictRealtime=yes
RestrictSUIDSGID=yes
RemoveIPC=yes
RestrictNamespaces=yes

# 系统调用过滤
SystemCallFilter=@system-service
SystemCallFilter=~@debug @mount @cpu-emulation @obsolete

# 网络限制
IPAddressDeny=any
IPAddressAllow=localhost
IPAddressAllow=10.0.0.0/8
IPAddressAllow=**********/12
IPAddressAllow=***********/16
```

### 内核安全参数
```bash
# 优化内核安全参数
sudo tee -a /etc/sysctl.conf << 'EOF'
# 网络安全
net.ipv4.conf.all.send_redirects = 0
net.ipv4.conf.default.send_redirects = 0
net.ipv4.conf.all.accept_redirects = 0
net.ipv4.conf.default.accept_redirects = 0
net.ipv4.conf.all.accept_source_route = 0
net.ipv4.conf.default.accept_source_route = 0

# SYN flood 保护
net.ipv4.tcp_syncookies = 1
net.ipv4.tcp_max_syn_backlog = 2048
net.ipv4.tcp_synack_retries = 3

# IP 欺骗保护
net.ipv4.conf.all.rp_filter = 1
net.ipv4.conf.default.rp_filter = 1
EOF

sudo sysctl -p
```

## 🔍 安全审计

### 定期安全检查清单
```bash
# 创建安全检查脚本
sudo tee /opt/sm/security-audit.sh << 'EOF'
#!/bin/bash
# 安全审计脚本

echo "=== SM 安全审计报告 ==="
echo "时间: $(date)"
echo ""

# 1. 检查服务状态
echo "1. 服务状态:"
systemctl is-active sm || echo "❌ 服务未运行"
systemctl is-enabled sm || echo "⚠️ 服务未设置开机自启"

# 2. 检查文件权限
echo ""
echo "2. 文件权限检查:"
ls -la /opt/sm/.env | grep "600" || echo "❌ .env 文件权限不正确"
ls -la /opt/sm/sm | grep "755" || echo "❌ 主程序权限不正确"

# 3. 检查端口状态
echo ""
echo "3. 端口监听:"
ss -tlnp | grep :1319 || echo "❌ 前端端口未监听"
ss -tlnp | grep :1911 || echo "❌ 后端端口未监听"

# 4. 检查防火墙状态
echo ""
echo "4. 防火墙状态:"
ufw status | grep "Status: active" || echo "⚠️ 防火墙未启用"

# 5. 检查日志
echo ""
echo "5. 最近安全事件:"
tail -n 10 /opt/sm/logs/security.log 2>/dev/null || echo "ℹ️ 无安全日志"

# 6. 检查证书有效期
echo ""
echo "6. 证书检查:"
if [ -f "/opt/sm/certs/cert.pem" ]; then
    openssl x509 -in /opt/sm/certs/cert.pem -noout -enddate
else
    echo "ℹ️ 未找到 TLS 证书"
fi

echo ""
echo "=== 审计完成 ==="
EOF

chmod +x /opt/sm/security-audit.sh

# 定期运行审计
echo "0 6 * * 1 /opt/sm/security-audit.sh | mail -s 'SM安全审计报告' <EMAIL>" | sudo crontab -
```

## 🚨 应急响应

### 安全事件响应
```bash
# 创建应急响应脚本
sudo tee /opt/sm/emergency-response.sh << 'EOF'
#!/bin/bash
# 应急响应脚本

case "$1" in
    "lockdown")
        echo "🚨 启动安全锁定模式"
        # 停止服务
        systemctl stop sm
        # 阻断所有外部访问
        ufw deny 1319
        ufw deny 1911
        echo "✅ 系统已锁定"
        ;;
    "unlock")
        echo "🔓 解除安全锁定"
        # 恢复网络访问
        ufw allow 1319
        ufw allow 1911
        # 重启服务
        systemctl start sm
        echo "✅ 系统已解锁"
        ;;
    "backup")
        echo "💾 创建紧急备份"
        tar -czf "/backup/sm-emergency-$(date +%Y%m%d-%H%M%S).tar.gz" \
          /opt/sm/config /opt/sm/.env /opt/sm/logs
        echo "✅ 备份完成"
        ;;
    *)
        echo "用法: $0 {lockdown|unlock|backup}"
        exit 1
        ;;
esac
EOF

chmod +x /opt/sm/emergency-response.sh
```

## 📋 安全检查清单

生产环境部署前，确保完成以下安全配置：

- [ ] **认证安全**
  - [ ] 修改默认管理员密码
  - [ ] 设置强 JWT 密钥（64字符以上）
  - [ ] 配置会话超时

- [ ] **网络安全**
  - [ ] 配置防火墙规则
  - [ ] 启用 TLS/SSL
  - [ ] 配置反向代理（如需要）

- [ ] **文件安全**
  - [ ] 设置正确的文件权限
  - [ ] 创建专用服务用户
  - [ ] 保护敏感配置文件

- [ ] **监控审计**
  - [ ] 启用安全日志
  - [ ] 配置入侵检测
  - [ ] 设置监控告警

- [ ] **数据库安全**
  - [ ] 启用数据库认证
  - [ ] 配置网络隔离
  - [ ] 设置强密码策略

- [ ] **系统强化**
  - [ ] 应用 systemd 安全选项
  - [ ] 优化内核参数
  - [ ] 禁用不必要的服务

---

**相关文档**: [部署指南](deployment.md) | [故障排除](troubleshooting.md)