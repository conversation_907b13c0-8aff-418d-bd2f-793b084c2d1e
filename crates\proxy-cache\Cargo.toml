[package]
name = "proxy-cache"
version = "0.1.0"
edition = "2021"
description = "Unified extensions for reverse proxy (cache, auth, monitoring, API)"

[dependencies]
# 基础依赖 - 只依赖proxy-types和proxy-core，移除proxy-config依赖避免循环
proxy-types = { path = "../proxy-types" }
proxy-core = { path = "../proxy-core" }

# 标准依赖
tokio = "1.35"
async-trait = "0.1"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tracing = "0.1"
anyhow = "1.0"
thiserror = "1.0"

# 时间处理
chrono = { version = "0.4", features = ["serde", "clock"], default-features = false }

# 缓存相关
redis = { version = "0.24", features = ["tokio-comp"], optional = true, default-features = false }
dashmap = "5.5"

# HTTP和Web框架（仅API功能需要）
axum = { version = "0.7", features = ["ws"], optional = true }
axum-extra = { version = "0.9", features = ["typed-header"], optional = true }
tower = { version = "0.4", features = ["timeout"], optional = true }
tower-http = { version = "0.5", features = ["cors", "trace", "validate-request"], optional = true }
headers = { version = "0.4", optional = true }
hyper = { version = "1.1", features = ["full"], optional = true }

# 认证相关（仅认证功能需要）
jsonwebtoken = { version = "9.3", optional = true }
bcrypt = { version = "0.15", optional = true }
uuid = { version = "1.6", features = ["v4", "serde"], optional = true }
argon2 = { version = "0.5", optional = true }
once_cell = { version = "1.19", optional = true }
parking_lot = { version = "0.12", optional = true }

# 监控相关（仅监控功能需要）
prometheus = { version = "0.13", optional = true }
opentelemetry = { version = "0.22", optional = true }
opentelemetry-jaeger = { version = "0.21", optional = true }

# 数据库（可选）
mongodb = { version = "2.8", features = ["tokio-runtime", "bson-chrono-0_4"], optional = true }

# 额外依赖
bytes = "1.5"

[features]
default = ["cache"]

# 基础功能
cache = ["redis", "parking_lot"]

# 认证功能 - 只有启用时才包含相关依赖
auth = [
    "jsonwebtoken", 
    "bcrypt", 
    "uuid",
    "argon2",
    "once_cell", 
    "parking_lot"
]

# 监控功能 - 只有启用时才包含相关依赖
monitoring = [
    "prometheus", 
    "opentelemetry", 
    "opentelemetry-jaeger"
]

# API功能 - 只有启用时才包含相关依赖
api = [
    "axum", 
    "axum-extra",
    "tower", 
    "tower-http", 
    "headers",
    "hyper"
]

# 数据库支持
database = ["mongodb"]

# 完整功能集
full = ["cache", "auth", "monitoring", "api", "database"]