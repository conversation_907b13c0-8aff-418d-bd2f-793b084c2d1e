/**
 * 前后端API集成测试和演示
 * 提供完整的双向通信功能
 */

class APIIntegration {
    constructor() {
        this.baseURL = '';
        this.authToken = 'disabled_auth_token'; // 使用禁用的认证令牌
        this.websocket = null;
        this.isConnected = false;
        this.retryCount = 0;
        this.maxRetries = 3;
        
        this.init();
    }

    init() {
        console.log('🔧 初始化API集成模块...');
        this.setupEventListeners();
        this.testAPIConnectivity();
    }

    setupEventListeners() {
        // 监听页面可见性变化，自动重连WebSocket
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && !this.isConnected) {
                this.connectWebSocket();
            }
        });

        // 监听网络状态变化
        window.addEventListener('online', () => {
            console.log('🌐 网络已连接，重新测试API');
            this.testAPIConnectivity();
            this.connectWebSocket();
        });

        window.addEventListener('offline', () => {
            console.log('📡 网络已断开');
            this.disconnectWebSocket();
        });
    }

    // 测试API连通性
    async testAPIConnectivity() {
        console.log('🔍 测试API连通性...');
        
        const endpoints = [
            '/api/recursive-proxy/status',
            '/api/ml/status',
            '/api/security/status',
            '/api/realtime/stats'
        ];

        const results = {};
        
        for (const endpoint of endpoints) {
            try {
                const response = await this.makeRequest('GET', endpoint);
                results[endpoint] = {
                    status: 'success',
                    data: response
                };
                console.log(`✅ ${endpoint} - 连接成功`);
            } catch (error) {
                results[endpoint] = {
                    status: 'error',
                    error: error.message
                };
                console.log(`❌ ${endpoint} - 连接失败: ${error.message}`);
            }
        }

        // 触发连通性测试完成事件
        const event = new CustomEvent('api-connectivity-test', {
            detail: results
        });
        document.dispatchEvent(event);

        return results;
    }

    // 通用API请求方法
    async makeRequest(method, endpoint, data = null) {
        const url = `${this.baseURL}${endpoint}`;
        
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.authToken}`
            }
        };

        if (data && (method === 'POST' || method === 'PUT')) {
            options.body = JSON.stringify(data);
        }

        const response = await fetch(url, options);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    }

    // 递归代理API方法
    async getRecursiveProxyStatus() {
        return await this.makeRequest('GET', '/api/recursive-proxy/status');
    }

    async startRecursiveProxy() {
        return await this.makeRequest('POST', '/api/recursive-proxy/start');
    }

    async stopRecursiveProxy() {
        return await this.makeRequest('POST', '/api/recursive-proxy/stop');
    }

    async getRecursiveProxyStats() {
        return await this.makeRequest('GET', '/api/recursive-proxy/stats');
    }

    async getActiveSessions() {
        return await this.makeRequest('GET', '/api/recursive-proxy/sessions');
    }

    // 机器学习API方法
    async getMLStatus() {
        return await this.makeRequest('GET', '/api/ml/status');
    }

    async getURLPatterns() {
        return await this.makeRequest('GET', '/api/ml/url-patterns');
    }

    async getContentAnalysis() {
        return await this.makeRequest('GET', '/api/ml/content-analysis');
    }

    async getAdaptiveStrategies() {
        return await this.makeRequest('GET', '/api/ml/adaptive-strategies');
    }

    // 安全对抗API方法
    async getSecurityStatus() {
        return await this.makeRequest('GET', '/api/security/status');
    }

    async getWAFBypassConfig() {
        return await this.makeRequest('GET', '/api/security/waf-bypass');
    }

    async getCaptchaSolverConfig() {
        return await this.makeRequest('GET', '/api/security/captcha-solver');
    }

    async getAntiBotConfig() {
        return await this.makeRequest('GET', '/api/security/anti-bot');
    }

    // 实时数据API方法
    async getRealtimeStats() {
        return await this.makeRequest('GET', '/api/realtime/stats');
    }

    async getRealtimeEvents() {
        return await this.makeRequest('GET', '/api/realtime/events');
    }

    // WebSocket连接管理
    connectWebSocket() {
        if (this.websocket) {
            this.websocket.close();
        }

        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/api/realtime/ws`;

        console.log(`🔌 连接WebSocket: ${wsUrl}`);

        this.websocket = new WebSocket(wsUrl);

        this.websocket.onopen = () => {
            console.log('✅ WebSocket连接已建立');
            this.isConnected = true;
            this.retryCount = 0;
            
            // 触发连接成功事件
            const event = new CustomEvent('websocket-connected');
            document.dispatchEvent(event);

            // 发送初始请求
            this.sendWebSocketMessage({ action: 'get_stats' });
        };

        this.websocket.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                console.log('📨 收到WebSocket消息:', data);
                
                // 触发消息接收事件
                const messageEvent = new CustomEvent('websocket-message', {
                    detail: data
                });
                document.dispatchEvent(messageEvent);
                
            } catch (error) {
                console.error('❌ 解析WebSocket消息失败:', error);
            }
        };

        this.websocket.onclose = () => {
            console.log('🔌 WebSocket连接已关闭');
            this.isConnected = false;
            
            // 触发连接关闭事件
            const event = new CustomEvent('websocket-disconnected');
            document.dispatchEvent(event);

            // 自动重连
            if (this.retryCount < this.maxRetries) {
                this.retryCount++;
                console.log(`🔄 尝试重连WebSocket (${this.retryCount}/${this.maxRetries})`);
                setTimeout(() => this.connectWebSocket(), 5000 * this.retryCount);
            }
        };

        this.websocket.onerror = (error) => {
            console.error('❌ WebSocket错误:', error);
            
            // 触发错误事件
            const errorEvent = new CustomEvent('websocket-error', {
                detail: error
            });
            document.dispatchEvent(errorEvent);
        };
    }

    disconnectWebSocket() {
        if (this.websocket) {
            this.websocket.close();
            this.websocket = null;
            this.isConnected = false;
        }
    }

    sendWebSocketMessage(message) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify(message));
            console.log('📤 发送WebSocket消息:', message);
        } else {
            console.warn('⚠️ WebSocket未连接，无法发送消息');
        }
    }

    // 批量API测试
    async runAPITests() {
        console.log('🧪 开始API测试...');
        
        const tests = [
            { name: '递归代理状态', method: this.getRecursiveProxyStatus },
            { name: 'ML状态', method: this.getMLStatus },
            { name: '安全状态', method: this.getSecurityStatus },
            { name: '实时统计', method: this.getRealtimeStats },
            { name: 'URL模式', method: this.getURLPatterns },
            { name: 'WAF绕过配置', method: this.getWAFBypassConfig }
        ];

        const results = {};

        for (const test of tests) {
            try {
                console.log(`🔍 测试: ${test.name}`);
                const result = await test.method.call(this);
                results[test.name] = {
                    status: 'success',
                    data: result
                };
                console.log(`✅ ${test.name} - 成功`);
            } catch (error) {
                results[test.name] = {
                    status: 'error',
                    error: error.message
                };
                console.log(`❌ ${test.name} - 失败: ${error.message}`);
            }
        }

        console.log('🧪 API测试完成:', results);
        return results;
    }

    // 演示完整的双向通信流程
    async demonstrateFullCommunication() {
        console.log('🎭 开始双向通信演示...');

        try {
            // 1. 测试API连通性
            console.log('📡 步骤1: 测试API连通性');
            await this.testAPIConnectivity();

            // 2. 连接WebSocket
            console.log('🔌 步骤2: 建立WebSocket连接');
            this.connectWebSocket();

            // 等待连接建立
            await new Promise(resolve => {
                const handler = () => {
                    document.removeEventListener('websocket-connected', handler);
                    resolve();
                };
                document.addEventListener('websocket-connected', handler);
                
                // 超时处理
                setTimeout(() => {
                    document.removeEventListener('websocket-connected', handler);
                    resolve();
                }, 5000);
            });

            // 3. 执行API测试
            console.log('🧪 步骤3: 执行API测试');
            await this.runAPITests();

            // 4. 测试WebSocket双向通信
            console.log('💬 步骤4: 测试WebSocket双向通信');
            this.sendWebSocketMessage({ action: 'ping' });
            this.sendWebSocketMessage({ action: 'get_stats' });

            console.log('🎉 双向通信演示完成！');
            return true;

        } catch (error) {
            console.error('❌ 双向通信演示失败:', error);
            return false;
        }
    }
}

// 全局API集成实例
let apiIntegration;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', () => {
    apiIntegration = new APIIntegration();
    window.apiIntegration = apiIntegration;
    
    // 自动开始演示（可选）
    setTimeout(() => {
        if (window.location.search.includes('demo=true')) {
            apiIntegration.demonstrateFullCommunication();
        }
    }, 2000);
});

// 导出给其他模块使用
window.APIIntegration = APIIntegration;
