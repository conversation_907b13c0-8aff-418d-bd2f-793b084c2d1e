//! 安全的JWT密钥管理模块
//!
//! 提供加密存储、安全访问和权限控制的JWT密钥管理功能

use base64::{engine::general_purpose, Engine as _};
use hex;
use rand::RngCore;
use sha2::{Digest, Sha256};
use std::fs::{self, OpenOptions};
use std::io::{BufRead, Write};
use std::path::Path;
use tracing::{debug, error, info, warn};
use zeroize::{Zeroize, ZeroizeOnDrop};

use proxy_core::error::{ProxyError, Result};

/// 安全的JWT密钥结构 - 内存中自动清零
#[derive(Zeroize, ZeroizeOnDrop)]
pub struct SecureJwtSecret {
    /// 加密的密钥数据
    encrypted_key: Vec<u8>,
    /// 用于验证的哈希值
    key_hash: String,
}

impl SecureJwtSecret {
    /// 生成新的安全JWT密钥
    pub fn generate() -> Result<Self> {
        let mut rng = rand::thread_rng();
        let mut raw_key = [0u8; 64]; // 512位密钥
        rng.fill_bytes(&mut raw_key);

        // 创建密钥哈希用于验证
        let mut hasher = Sha256::new();
        hasher.update(raw_key);
        let key_hash = hex::encode(hasher.finalize());

        // 简单的XOR加密（实际生产环境应该使用更强的加密）
        let mut encrypted_key = raw_key.to_vec();
        let xor_key = Self::derive_xor_key();
        for (i, byte) in encrypted_key.iter_mut().enumerate() {
            *byte ^= xor_key[i % xor_key.len()];
        }

        // 清零原始密钥
        raw_key.zeroize();

        debug!("生成新的JWT密钥，哈希: {}", &key_hash[..16]); // 只记录部分哈希

        Ok(Self {
            encrypted_key,
            key_hash,
        })
    }

    /// 从加密数据恢复密钥
    pub fn from_encrypted(encrypted_data: Vec<u8>, expected_hash: String) -> Result<Self> {
        // 验证数据完整性
        let mut decrypted = encrypted_data.clone();
        let xor_key = Self::derive_xor_key();
        for (i, byte) in decrypted.iter_mut().enumerate() {
            *byte ^= xor_key[i % xor_key.len()];
        }

        // 验证哈希
        let mut hasher = Sha256::new();
        hasher.update(&decrypted);
        let computed_hash = hex::encode(hasher.finalize());

        if computed_hash != expected_hash {
            decrypted.zeroize();
            return Err(ProxyError::security_violation("JWT密钥数据已被篡改"));
        }

        decrypted.zeroize();

        Ok(Self {
            encrypted_key: encrypted_data,
            key_hash: expected_hash,
        })
    }

    /// 获取Base64编码的密钥（用于JWT库）
    pub fn to_base64(&self) -> Result<String> {
        let mut decrypted = self.encrypted_key.clone();
        let xor_key = Self::derive_xor_key();

        for (i, byte) in decrypted.iter_mut().enumerate() {
            *byte ^= xor_key[i % xor_key.len()];
        }

        let base64_key = general_purpose::STANDARD.encode(&decrypted);
        decrypted.zeroize();

        Ok(base64_key)
    }

    /// 获取密钥哈希（用于验证）
    pub fn hash(&self) -> &str {
        &self.key_hash
    }

    /// 派生XOR密钥（简单的密钥派生）
    fn derive_xor_key() -> Vec<u8> {
        // 在实际生产环境中，这应该是更复杂的密钥派生函数
        // 例如使用PBKDF2、scrypt或Argon2
        let seed = b"SM_JWT_KEY_DERIVATION_2024";
        let mut hasher = Sha256::new();
        hasher.update(seed);
        hasher.finalize().to_vec()
    }
}

/// 安全的JWT密钥管理器
pub struct SecureJwtManager {
    config_dir: String,
    key_file: String,
}

impl SecureJwtManager {
    /// 创建新的JWT密钥管理器
    pub fn new() -> Self {
        Self {
            config_dir: "config".to_string(),
            key_file: "config/jwt_key.enc".to_string(),
        }
    }

    /// 生成并保存新的JWT密钥
    pub fn generate_and_save_key(&self) -> Result<String> {
        // 生成新密钥
        let secure_key = SecureJwtSecret::generate()?;

        // 保存到加密文件
        self.save_encrypted_key(&secure_key)?;

        // 返回Base64格式的密钥
        let base64_key = secure_key.to_base64()?;

        info!("✅ 新JWT密钥已生成并安全保存");
        Ok(base64_key)
    }

    /// 加载现有的JWT密钥
    pub fn load_key(&self) -> Result<String> {
        if !Path::new(&self.key_file).exists() {
            return Err(ProxyError::config("JWT密钥文件不存在"));
        }

        let secure_key = self.load_encrypted_key()?;
        let base64_key = secure_key.to_base64()?;

        debug!("✅ JWT密钥已从安全存储加载");
        Ok(base64_key)
    }

    /// 检查密钥是否存在
    pub fn key_exists(&self) -> bool {
        Path::new(&self.key_file).exists()
    }

    /// 轮换JWT密钥（生成新密钥并备份旧密钥）
    pub fn rotate_key(&self) -> Result<String> {
        // 备份现有密钥
        if self.key_exists() {
            let backup_file = format!(
                "{}.backup.{}",
                self.key_file,
                chrono::Utc::now().timestamp()
            );
            if let Err(e) = fs::copy(&self.key_file, &backup_file) {
                warn!("无法备份现有JWT密钥: {}", e);
            } else {
                info!("✅ 现有JWT密钥已备份到: {}", backup_file);
            }
        }

        // 生成新密钥
        self.generate_and_save_key()
    }

    /// 保存加密的密钥到文件
    fn save_encrypted_key(&self, key: &SecureJwtSecret) -> Result<()> {
        // 确保目录存在
        if let Some(parent) = Path::new(&self.key_file).parent() {
            fs::create_dir_all(parent)
                .map_err(|e| ProxyError::file_system_with_source("创建配置目录失败", e))?;
        }

        // 准备加密数据
        let data = format!(
            "{}:{}",
            general_purpose::STANDARD.encode(&key.encrypted_key),
            key.key_hash
        );

        // 写入文件（使用临时文件确保原子性）
        let temp_file = format!("{}.tmp", self.key_file);
        {
            let mut file = OpenOptions::new()
                .write(true)
                .create(true)
                .truncate(true)
                .open(&temp_file)
                .map_err(|e| ProxyError::file_system_with_source("创建临时密钥文件失败", e))?;

            file.write_all(data.as_bytes())
                .map_err(|e| ProxyError::file_system_with_source("写入密钥数据失败", e))?;

            file.flush()
                .map_err(|e| ProxyError::file_system_with_source("刷新密钥文件失败", e))?;
        }

        // 原子性重命名
        fs::rename(&temp_file, &self.key_file)
            .map_err(|e| ProxyError::file_system_with_source("保存密钥文件失败", e))?;

        // 设置安全权限（仅Unix系统）
        self.secure_key_file_permissions()?;

        Ok(())
    }

    /// 从文件加载加密的密钥
    fn load_encrypted_key(&self) -> Result<SecureJwtSecret> {
        let data = fs::read_to_string(&self.key_file)
            .map_err(|e| ProxyError::file_system_with_source("读取密钥文件失败", e))?;

        let parts: Vec<&str> = data.trim().split(':').collect();
        if parts.len() != 2 {
            return Err(ProxyError::security_violation("密钥文件格式无效"));
        }

        let encrypted_key = general_purpose::STANDARD
            .decode(parts[0])
            .map_err(|_| ProxyError::security_violation("密钥数据解码失败"))?;

        let key_hash = parts[1].to_string();

        SecureJwtSecret::from_encrypted(encrypted_key, key_hash)
    }

    /// 设置密钥文件的安全权限（仅Unix系统）
    fn secure_key_file_permissions(&self) -> Result<()> {
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;

            let mut perms = fs::metadata(&self.key_file)
                .map_err(|e| ProxyError::file_system_with_source("读取密钥文件元数据失败", e))?
                .permissions();

            perms.set_mode(0o600); // 仅所有者可读写
            fs::set_permissions(&self.key_file, perms)
                .map_err(|e| ProxyError::file_system_with_source("设置密钥文件权限失败", e))?;

            info!("✅ JWT密钥文件权限已设置为 600");
        }

        #[cfg(not(unix))]
        {
            // Windows系统不需要设置文件权限
            debug!("ℹ️  跳过密钥文件权限设置 (非Unix系统)");
        }

        Ok(())
    }
}

/// 环境变量JWT密钥管理（用于向后兼容）
pub struct EnvironmentJwtManager;

impl EnvironmentJwtManager {
    /// 从环境变量获取JWT密钥，如果不存在则生成新的
    pub fn get_or_generate_key() -> Result<String> {
        if let Ok(existing_key) = std::env::var("JWT_SECRET") {
            if existing_key.len() >= 32 {
                debug!("✅ 使用现有的环境变量JWT密钥");
                return Ok(existing_key);
            } else {
                warn!("⚠️ 环境变量JWT_SECRET长度不足，生成新密钥");
            }
        }

        // 生成新密钥
        let secure_manager = SecureJwtManager::new();
        let new_key = secure_manager.generate_and_save_key()?;

        // 设置到环境变量
        std::env::set_var("JWT_SECRET", &new_key);

        info!("🔑 新JWT密钥已生成并设置到环境变量");
        Ok(new_key)
    }

    /// 安全地更新环境变量中的JWT密钥
    pub fn update_environment_key(new_key: &str) -> Result<()> {
        if new_key.len() < 32 {
            return Err(ProxyError::invalid_input("JWT密钥长度必须至少32个字符"));
        }

        std::env::set_var("JWT_SECRET", new_key);
        info!("✅ JWT密钥已安全更新到环境变量");
        Ok(())
    }
}

/// 获取或生成JWT密钥的统一接口
pub fn get_or_generate_jwt_secret() -> Result<String> {
    let secure_manager = SecureJwtManager::new();

    // 优先从安全存储加载
    if secure_manager.key_exists() {
        match secure_manager.load_key() {
            Ok(key) => {
                // 同时更新环境变量
                EnvironmentJwtManager::update_environment_key(&key)?;
                return Ok(key);
            }
            Err(e) => {
                error!("加载安全存储的JWT密钥失败: {}", e);
                warn!("将尝试从环境变量加载或生成新密钥");
            }
        }
    }

    // 回退到环境变量管理
    EnvironmentJwtManager::get_or_generate_key()
}

/// 生成新的JWT密钥并保存到安全存储
pub fn generate_new_jwt_secret() -> Result<String> {
    let secure_manager = SecureJwtManager::new();
    let new_key = secure_manager.generate_and_save_key()?;

    // 同时更新环境变量
    EnvironmentJwtManager::update_environment_key(&new_key)?;

    Ok(new_key)
}

/// 轮换JWT密钥
pub fn rotate_jwt_secret() -> Result<String> {
    let secure_manager = SecureJwtManager::new();
    let new_key = secure_manager.rotate_key()?;

    // 同时更新环境变量
    EnvironmentJwtManager::update_environment_key(&new_key)?;

    info!("🔄 JWT密钥已成功轮换");
    Ok(new_key)
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[test]
    fn test_secure_jwt_secret_generation() {
        let secret = SecureJwtSecret::generate().unwrap();
        let base64_key = secret.to_base64().unwrap();

        // 验证密钥长度
        assert!(base64_key.len() > 32);
        assert!(!secret.hash().is_empty());
    }

    #[test]
    fn test_secure_jwt_secret_roundtrip() {
        let original = SecureJwtSecret::generate().unwrap();
        let original_hash = original.hash().to_string();
        let original_b64 = original.to_base64().unwrap();

        // 模拟保存和加载
        let encrypted_data = original.encrypted_key.clone();
        let restored = SecureJwtSecret::from_encrypted(encrypted_data, original_hash).unwrap();
        let restored_b64 = restored.to_base64().unwrap();

        assert_eq!(original_b64, restored_b64);
    }

    #[tokio::test]
    async fn test_jwt_manager() {
        let temp_dir = TempDir::new().unwrap();
        let temp_path = temp_dir.path().join("jwt_key.enc");

        let mut manager = SecureJwtManager::new();
        manager.key_file = temp_path.to_string_lossy().to_string();

        // 测试生成和保存
        let key1 = manager.generate_and_save_key().unwrap();
        assert!(manager.key_exists());

        // 测试加载
        let key2 = manager.load_key().unwrap();
        assert_eq!(key1, key2);

        // 测试轮换
        let key3 = manager.rotate_key().unwrap();
        assert_ne!(key1, key3);
    }

    #[test]
    fn test_environment_jwt_manager() {
        // 清除现有环境变量
        std::env::remove_var("JWT_SECRET");

        // 测试生成新密钥
        let key = EnvironmentJwtManager::get_or_generate_key().unwrap();
        assert!(key.len() >= 32);

        // 验证环境变量已设置
        let env_key = std::env::var("JWT_SECRET").unwrap();
        assert_eq!(key, env_key);
    }
}
