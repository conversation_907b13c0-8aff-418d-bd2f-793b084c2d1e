use axum::{http::StatusCode, Router};
use std::path::PathBuf;
use tower_http::services::ServeDir;
use tracing::warn;

// 导入安全的输入验证模块
use crate::security::input_validation::{UnifiedValidator, ValidationConfig};

/// 安全的静态文件过滤器 - 增强版
fn is_safe_path(path: &std::path::Path) -> bool {
    let path_str = path.to_string_lossy();

    // 使用统一的输入验证器进行安全检查
    let validator = UnifiedValidator::new(ValidationConfig {
        strict_mode: true,
        max_path_length: 200,
        ..Default::default()
    });

    // 首先进行基础路径验证
    if validator.validate_path(&path_str, "静态文件路径").is_err() {
        warn!("🚨 SECURITY: 路径验证失败: {}", path_str);
        return false;
    }

    let path_lower = path_str.to_lowercase();

    // 🚨 黑名单：禁止访问的文件和目录 - 重点防护部署脚本
    let forbidden_patterns = [
        // 环境配置文件
        ".env",
        ".env.local",
        ".env.production",
        ".env.development",
        ".env.test",
        ".env.staging",
        // 项目配置文件
        "cargo.toml",
        "cargo.lock",
        "package.json",
        "package-lock.json",
        "yarn.lock",
        "composer.json",
        "requirements.txt",
        "pyproject.toml",
        // 编译和源码目录
        "target/",
        "src/",
        "crates/",
        "node_modules/",
        "__pycache__/",
        // 配置和文档目录
        "config/",
        "docs/",
        "documentation/",
        "readme.md",
        "readme",
        "changelog.md",
        "license",
        "license.md",
        // 🚨🚨🚨 部署和安装脚本 - 核心防护 🚨🚨🚨
        "setup.sh",     // 主要部署脚本
        "deploy.sh",    // 部署脚本
        "deploy.py",    // Python部署脚本
        "deploy.js",    // Node.js部署脚本
        "install.sh",   // 安装脚本
        "install.py",   // Python安装脚本
        "start.sh",     // 启动脚本
        "stop.sh",      // 停止脚本
        "restart.sh",   // 重启脚本
        "start-secure", // 安全启动脚本
        "update.sh",    // 更新脚本
        "migrate.sh",   // 数据库迁移脚本
        "backup.sh",    // 备份脚本
        "restore.sh",   // 恢复脚本
        "build.sh",     // 构建脚本
        "test.sh",      // 测试脚本
        "run.sh",       // 运行脚本
        "init.sh",      // 初始化脚本
        "configure.sh", // 配置脚本
        "cleanup.sh",   // 清理脚本
        // 系统服务文件
        ".service",
        "sm.service",
        "*.service",
        ".socket",
        ".timer",
        // 安全和配置文件
        "security.conf",
        "nginx.conf",
        "apache.conf",
        "httpd.conf",
        ".htaccess",
        ".htpasswd",
        "docker-compose.yml",
        "dockerfile",
        "makefile",
        // 备份和临时文件
        ".backup",
        ".bak",
        ".tmp",
        ".temp",
        ".swp",
        ".swo",
        "~",
        // 日志和数据文件
        ".log",
        "logs/",
        "log/",
        "database.db",
        "*.db",
        "*.sqlite",
        "*.sqlite3",
        "data/",
        "storage/",
        // 密钥和证书文件
        "*.key",
        "*.pem",
        "*.p12",
        "*.crt",
        "*.cert",
        "*.pfx",
        "id_rsa",
        "id_ed25519",
        ".ssh/",
        "ssl/",
        "certs/",
        "keys/",
        // 路径遍历攻击
        "..",
        "../",
        "..\\",
        "%2e%2e",
        "%2f",
        "%5c",
        // 脚本文件扩展名
        ".sh",
        ".bash",
        ".zsh",
        ".fish",
        ".ps1",
        ".bat",
        ".cmd",
        ".py",
        ".rb",
        ".pl",
        ".php",
        // 配置文件扩展名
        ".conf",
        ".config",
        ".ini",
        ".yaml",
        ".yml",
        ".toml",
        ".json", // 除非明确允许的前端json文件
    ];

    // 检查是否包含危险路径模式
    for pattern in forbidden_patterns.iter() {
        if path_lower.contains(pattern) {
            warn!(
                "🚨 SECURITY: 阻止访问危险路径模式 '{}' -> '{}'",
                pattern, path_lower
            );
            return false;
        }
    }

    // 🔒 检查是否尝试访问项目根目录的敏感文件
    let sensitive_root_files = [
        "cargo.toml",
        "cargo.lock",
        "readme.md",
        "setup.sh",
        "sm.service",
        "security.conf",
        ".gitignore",
        ".git",
    ];

    let file_name = path
        .file_name()
        .unwrap_or_default()
        .to_string_lossy()
        .to_lowercase();

    if sensitive_root_files.contains(&file_name.as_str()) {
        warn!("🚨 SECURITY: 阻止访问项目根目录敏感文件: {}", file_name);
        return false;
    }

    // ✅ 只允许特定的文件扩展名
    let allowed_extensions = [
        "html", "css", "js", "png", "jpg", "jpeg", "gif", "svg", "ico", "woff", "woff2", "ttf",
        "eot", "webp", "avif",
    ];

    if let Some(extension) = path.extension() {
        let ext = extension.to_string_lossy().to_lowercase();
        let is_allowed = allowed_extensions.contains(&ext.as_str());

        if !is_allowed {
            warn!("🚨 SECURITY: 不允许的文件扩展名: {}", ext);
        }

        is_allowed
    } else {
        // 只允许 favicon.ico 和空路径
        let allowed_no_ext = ["favicon.ico", ""];
        let result =
            allowed_no_ext.contains(&file_name.as_str()) || path_str == "/" || path_str.is_empty();

        if !result && !file_name.is_empty() {
            warn!("🚨 SECURITY: 不允许的无扩展名文件: {}", file_name);
        }

        result
    }
}

/// 额外的请求验证中间件
async fn validate_request_security(
    req: axum::extract::Request,
    next: axum::middleware::Next,
) -> Result<axum::response::Response, axum::http::StatusCode> {
    let uri = req.uri();
    let path = uri.path();

    // 使用统一验证器验证请求路径
    let validator = UnifiedValidator::new(ValidationConfig {
        strict_mode: true,
        ..Default::default()
    });

    if validator.validate_path(path, "请求路径").is_err() {
        warn!("🚨 SECURITY: 请求路径验证失败: {}", path);
        return Err(StatusCode::FORBIDDEN);
    }

    // 检查是否尝试通过查询参数绕过过滤
    if let Some(query) = uri.query() {
        let dangerous_query_patterns = [
            "../", "..\\", "%2e%2e", "setup.sh", "deploy", "install", ".sh", ".conf", ".env",
            "config/",
        ];

        for pattern in dangerous_query_patterns.iter() {
            if query.to_lowercase().contains(pattern) {
                warn!("🚨 SECURITY: 查询参数包含危险模式 '{}': {}", pattern, query);
                return Err(axum::http::StatusCode::FORBIDDEN);
            }
        }
    }

    // 检查用户代理是否可疑（可选）
    if let Some(user_agent) = req.headers().get("user-agent") {
        if let Ok(ua_str) = user_agent.to_str() {
            let suspicious_agents = ["curl", "wget", "python", "scanner", "bot"];
            let ua_lower = ua_str.to_lowercase();

            for agent in suspicious_agents.iter() {
                if ua_lower.contains(agent) {
                    warn!("🚨 SECURITY: 可疑的用户代理尝试访问: {}", ua_str);
                    // 注意：这里只是记录，不阻止，因为合法用户也可能使用这些工具
                    break;
                }
            }
        }
    }

    Ok(next.run(req).await)
}

/// 安全防护配置
#[derive(Debug, Clone)]
pub struct SecurityConfig {
    /// 是否启用严格模式（拒绝所有可疑请求）
    pub strict_mode: bool,
    /// 是否记录所有被阻止的请求
    pub log_blocked_requests: bool,
    /// 允许的最大路径深度
    pub max_path_depth: usize,
    /// 是否启用 IP 白名单（仅用于管理端点）
    pub enable_ip_whitelist: bool,
    /// 允许的管理员 IP 地址
    pub admin_ips: Vec<std::net::IpAddr>,
}

impl Default for SecurityConfig {
    fn default() -> Self {
        Self {
            strict_mode: true,
            log_blocked_requests: true,
            max_path_depth: 5,
            enable_ip_whitelist: false,
            admin_ips: vec![],
        }
    }
}

/// 高级安全检查 - 使用完整的路径遍历检测
pub fn advanced_security_check(path: &str, config: &SecurityConfig) -> Result<(), &'static str> {
    // 使用统一验证器替代简单的字符串检查
    let validator = UnifiedValidator::new(ValidationConfig {
        strict_mode: config.strict_mode,
        max_path_length: 500,
        ..Default::default()
    });

    // 完整的路径验证，替代原有的简单contains检查
    if validator.validate_path(path, "路径").is_err() {
        return Err("路径包含安全威胁");
    }

    // 检查路径深度
    let depth = path.matches('/').count();
    if depth > config.max_path_depth {
        return Err("路径深度超过限制");
    }

    Ok(())
}

/// 检查请求来源是否安全
pub fn check_request_origin(headers: &axum::http::HeaderMap, config: &SecurityConfig) -> bool {
    // 检查 Referer 头
    if let Some(referer) = headers.get("referer") {
        if let Ok(referer_str) = referer.to_str() {
            // 确保 Referer 来自同一域名
            if !referer_str.contains("localhost") && !referer_str.contains("127.0.0.1") {
                warn!("🚨 SECURITY: 可疑的 Referer: {}", referer_str);
                if config.strict_mode {
                    return false;
                }
            }
        }
    }

    // 检查 X-Forwarded-For 头是否存在异常
    if let Some(xff) = headers.get("x-forwarded-for") {
        if let Ok(xff_str) = xff.to_str() {
            // 记录代理请求
            warn!("代理请求检测: {}", xff_str);
        }
    }

    true
}

/// 创建安全的静态文件路由，严格限制访问范围
pub fn create_static_router() -> Router {
    // SPA架构：不需要StatusCode，所有路由都通过fallback_service处理

    Router::new()
        // 严格限制静态资源路径，只允许访问 frontend 目录内的特定子目录
        .nest_service(
            "/static",
            tower::ServiceBuilder::new()
                .map_request(|req: axum::extract::Request| {
                    let path = req.uri().path();
                    if !is_safe_path(&PathBuf::from(path.trim_start_matches("/static/"))) {
                        warn!("🚨 阻止访问危险路径: {}", path);
                    }
                    req
                })
                .service(ServeDir::new("frontend/static")),
        )
        .nest_service(
            "/js",
            tower::ServiceBuilder::new()
                .map_request(|req: axum::extract::Request| {
                    let path = req.uri().path();
                    if !is_safe_path(&PathBuf::from(path.trim_start_matches("/js/"))) {
                        warn!("🚨 阻止访问危险路径: {}", path);
                    }
                    req
                })
                .service(ServeDir::new("frontend/js")),
        )
        // 单独处理 favicon
        .route(
            "/favicon.ico",
            axum::routing::get_service(
                ServeDir::new("frontend").append_index_html_on_directories(false),
            ),
        )
        // SPA架构：所有路由都指向index.html，由前端JavaScript处理路由
        .route(
            "/",
            axum::routing::get_service(
                ServeDir::new("frontend").append_index_html_on_directories(true),
            ),
        )
        .route(
            "/index.html",
            axum::routing::get_service(ServeDir::new("frontend")),
        )
        // SPA路由：所有页面路径都返回index.html，让前端路由器处理
        .fallback_service(axum::routing::get_service(
            ServeDir::new("frontend").append_index_html_on_directories(true),
        ))
}
