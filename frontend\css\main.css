/* ==============================================
   SM镜像系统 - 主样式文件 v2.0
   现代化侧边栏布局 + 响应式设计 + 深色模式
   ============================================== */

/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 主色调 - 使用中性色调替代蓝色 */
    --primary-color: #64748b;
    --primary-dark: #475569;
    --primary-light: #94a3b8;

    /* 状态颜色 */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #06b6d4;

    /* 深色背景色 */
    --bg-primary: #0f0f0f;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #2a2a2a;
    --bg-card: #1e1e1e;
    --bg-hover: #333333;

    /* 文字颜色 */
    --text-primary: #ffffff;
    --text-secondary: #e0e0e0;
    --text-muted: #b0b0b0;
    --text-light: #f0f0f0;

    /* 边框颜色 */
    --border-color: #404040;
    --border-light: #555555;
    --border-dark: #2a2a2a;

    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6);

    /* 过渡动画 */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* 尺寸 */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    --toolbar-height: 60px;
    --border-radius: 8px;
    --border-radius-lg: 12px;
}

/* 基础样式 - 增加字体大小 */
html {
    font-size: 16px;
    /* 从14px增加到16px */
    scroll-behavior: smooth;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    font-size: 1rem;
    /* 明确设置body字体大小 */
}

/* 应用主容器 */
.app-container {
    display: flex;
    min-height: 100vh;
    position: relative;
}

/* ==============================================
   侧边栏样式
   ============================================== */
.sidebar {
    width: var(--sidebar-width);
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transition: width var(--transition-normal), transform var(--transition-normal);
    overflow-y: auto;
    overflow-x: hidden;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar.mobile-hidden {
    transform: translateX(-100%);
}

/* 侧边栏头部 */
.sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: var(--toolbar-height);
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-icon {
    font-size: 1.75rem;
    /* 增大图标 */
    flex-shrink: 0;
}

.logo-text h1 {
    font-size: 1.375rem;
    /* 增大标题字体 */
    font-weight: 600;
    color: var(--text-primary);
    white-space: nowrap;
}

.logo-text .version {
    font-size: 0.875rem;
    /* 增大版本号字体 */
    color: var(--text-muted);
    display: block;
    line-height: 1;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
    font-size: 1.125rem;
    /* 增大按钮字体 */
}

.sidebar-toggle:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.sidebar.collapsed .logo-text,
.sidebar.collapsed .sidebar-toggle {
    display: none;
}

/* 用户信息区 */
.user-info {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 42px;
    /* 稍微增大头像 */
    height: 42px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.user-details {
    flex: 1;
    min-width: 0;
}

.user-name {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 1rem;
    /* 增大用户名字体 */
}

.user-status {
    font-size: 0.875rem;
    /* 增大状态字体 */
    color: var(--text-muted);
}

.user-menu-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
    flex-shrink: 0;
    font-size: 1rem;
    /* 增大按钮字体 */
}

.user-menu-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.sidebar.collapsed .user-details,
.sidebar.collapsed .user-menu-btn {
    display: none;
}



/* 导航菜单 */
.nav-menu {
    flex: 1;
    padding: 1rem 0;
}

.nav-group {
    margin-bottom: 1.5rem;
}

.nav-group-title {
    font-size: 0.875rem;
    /* 增大分组标题字体 */
    color: var(--text-muted);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.05em;
    padding: 0 1rem;
    margin-bottom: 0.5rem;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1rem;
    /* 增加内边距 */
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    width: 100%;
    text-align: left;
    position: relative;
}

.nav-item:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.nav-item.active {
    background: var(--primary-color);
    color: white;
}

.nav-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: white;
}

.nav-icon {
    font-size: 1.125rem;
    /* 增大导航图标 */
    flex-shrink: 0;
}

.nav-text {
    font-size: 1rem;
    /* 增大导航文字 */
    font-weight: 500;
}

.nav-badge {
    background: var(--error-color);
    color: white;
    font-size: 0.875rem;
    /* 增大徽章字体 */
    padding: 0.125rem 0.5rem;
    /* 增加徽章内边距 */
    border-radius: 12px;
    margin-left: auto;
    min-width: 1.5rem;
    text-align: center;
}

.sidebar.collapsed .nav-group-title,
.sidebar.collapsed .nav-text,
.sidebar.collapsed .nav-badge {
    display: none;
}

/* 快速操作 */
.quick-actions {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
}

.action-group-title {
    font-size: 0.875rem;
    /* 增大操作组标题字体 */
    color: var(--text-muted);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.625rem 0;
    /* 增加内边距 */
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    width: 100%;
    text-align: left;
}

.action-btn:hover {
    color: var(--primary-color);
}

.action-icon {
    font-size: 1rem;
    /* 增大操作图标 */
    flex-shrink: 0;
}

.action-text {
    font-size: 0.875rem;
    /* 增大操作文字 */
}

.sidebar.collapsed .action-group-title,
.sidebar.collapsed .action-text {
    display: none;
}

/* ==============================================
   主内容区样式
   ============================================== */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    transition: margin-left var(--transition-normal);
}

.sidebar.collapsed+.main-content {
    margin-left: var(--sidebar-collapsed-width);
}

/* 工具栏 */
.toolbar {
    height: var(--toolbar-height);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    padding: 0 1.5rem;
    gap: 1rem;
    position: sticky;
    top: 0;
    z-index: 100;
}

.toolbar-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
    font-size: 1.125rem;
    /* 增大移动端菜单按钮 */
}

.mobile-menu-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.breadcrumb-item {
    font-size: 1rem;
    /* 增大面包屑字体 */
    color: var(--text-muted);
}

.breadcrumb-item.active {
    color: var(--text-primary);
    font-weight: 500;
}

.toolbar-center {
    flex: 1;
    display: flex;
    justify-content: center;
}

.search-box {
    position: relative;
    max-width: 400px;
    width: 100%;
}

.search-box input {
    width: 100%;
    padding: 0.625rem 2.5rem 0.625rem 1rem;
    /* 增加内边距 */
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 1rem;
    /* 增大搜索框字体 */
    transition: all var(--transition-fast);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(100, 116, 139, 0.2);
    /* 更新为新的主色调 */
}

.search-btn {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    font-size: 1rem;
    /* 增大搜索按钮字体 */
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tool-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
    font-size: 1rem;
    /* 增大工具按钮字体 */
}

.tool-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.scale-controls {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    padding: 0.25rem;
}

.scale-indicator {
    font-size: 0.875rem;
    /* 增大缩放指示器字体 */
    color: var(--text-muted);
    padding: 0 0.5rem;
    min-width: 3rem;
    text-align: center;
}

.auth-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.625rem 1.25rem;
    /* 增加内边距 */
    border-radius: var(--border-radius);
    font-size: 1rem;
    /* 增大认证按钮字体 */
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.auth-btn:hover {
    background: var(--primary-dark);
}

/* 内容区域 */
.content-area {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* 章节头部 */
.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    gap: 1rem;
}

.section-header h2 {
    font-size: 1.75rem;
    /* 增大章节标题字体 */
    font-weight: 600;
    color: var(--text-primary);
}

.section-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

/* ==============================================
   按钮样式
   ============================================== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.625rem 1.25rem;
    /* 增加内边距 */
    font-size: 1rem;
    /* 增大按钮字体 */
    font-weight: 500;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    white-space: nowrap;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-info {
    background: var(--info-color);
    color: white;
}

.btn-outline {
    background: transparent;
    color: var(--text-secondary);
    border-color: var(--border-color);
}

.btn-outline:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

/* ==============================================
   仪表板样式
   ============================================== */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.metric-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all var(--transition-fast);
}

.metric-card:hover {
    border-color: var(--border-light);
    box-shadow: var(--shadow-md);
}

.metric-card.primary {
    border-left: 4px solid var(--primary-color);
}

.metric-card.success {
    border-left: 4px solid var(--success-color);
}

.metric-card.warning {
    border-left: 4px solid var(--warning-color);
}

.metric-card.info {
    border-left: 4px solid var(--info-color);
}

.metric-icon {
    font-size: 2.25rem;
    /* 增大指标图标 */
    opacity: 0.8;
}

.metric-info {
    flex: 1;
}

.metric-value {
    font-size: 2.25rem;
    /* 增大指标数值字体 */
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
}

.metric-label {
    font-size: 1rem;
    /* 增大指标标签字体 */
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

.metric-change {
    font-size: 0.875rem;
    /* 增大变化指示字体 */
    color: var(--text-muted);
    margin-top: 0.25rem;
}

/* 仪表板面板 */
.dashboard-panels {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.panel-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.panel {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
}

.panel.chart-panel {
    grid-column: span 2;
}

.panel-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.panel-header h3 {
    font-size: 1.125rem;
    /* 增大面板标题字体 */
    font-weight: 600;
    color: var(--text-primary);
}

.panel-action {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    font-size: 1rem;
    /* 增大面板操作字体 */
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
}

.panel-action:hover {
    background: var(--bg-hover);
}

.panel-body {
    padding: 1.5rem;
}

/* ==============================================
   表格和列表样式
   ============================================== */
.filter-bar {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-group label {
    font-size: 1rem;
    /* 增大过滤器标签字体 */
    color: var(--text-secondary);
    white-space: nowrap;
}

.filter-select,
.filter-input,
.form-select,
.form-input {
    padding: 0.5rem 0.875rem;
    /* 增加内边距 */
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 1rem;
    /* 增大表单字体 */
    transition: all var(--transition-fast);
}

.filter-select:focus,
.filter-input:focus,
.form-select:focus,
.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(100, 116, 139, 0.2);
    /* 更新为新的主色调 */
}

.data-table-container {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
}

/* 认证提示 */
.auth-required {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-muted);
    font-size: 1rem;
    /* 增大认证提示字体 */
}

.auth-prompt {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.auth-icon {
    font-size: 3.5rem;
    /* 增大认证图标 */
    opacity: 0.5;
}

/* ==============================================
   模态框样式 - 居中显示和美观设计
   ============================================== */

/* 模态框遮罩层 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    opacity: 0;
    visibility: hidden;
    transition: opacity var(--transition-normal), visibility var(--transition-normal);
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* 模态框容器 */
.modal-container {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-color);
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    transform: scale(0.9) translateY(-20px);
    transition: transform var(--transition-normal);
}

.modal-overlay.show .modal-container {
    transform: scale(1) translateY(0);
}

/* 模态框头部 */
.modal-header {
    padding: 24px 24px 0;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 8px;
    border-radius: var(--border-radius);
    transition: color var(--transition-fast), background-color var(--transition-fast);
    line-height: 1;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: var(--text-primary);
    background-color: var(--bg-hover);
}

/* 模态框内容 */
.modal-body {
    padding: 0 24px 24px;
}

.modal-body p {
    color: var(--text-secondary);
    margin-bottom: 16px;
    line-height: 1.6;
}

/* 模态框底部 */
.modal-footer {
    padding: 24px;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
}

/* 表单样式 */
.modal-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.form-label.required::after {
    content: "*";
    color: var(--error-color);
    margin-left: 4px;
}

.form-control {
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: 1rem;
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(100, 116, 139, 0.1);
}

.form-control::placeholder {
    color: var(--text-muted);
}

.form-control:disabled {
    background: var(--bg-secondary);
    color: var(--text-muted);
    cursor: not-allowed;
}

/* 文本域样式 */
textarea.form-control {
    resize: vertical;
    min-height: 80px;
}

/* 选择框样式 */
.form-select {
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: 1rem;
    cursor: pointer;
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(100, 116, 139, 0.1);
}

/* 复选框和单选框样式 */
.form-check {
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-check-input {
    width: 18px;
    height: 18px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-tertiary);
    cursor: pointer;
}

.form-check-input:checked {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-label {
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 0.875rem;
}

/* 表单验证样式 */
.form-control.is-invalid {
    border-color: var(--error-color);
}

.form-control.is-valid {
    border-color: var(--success-color);
}

.invalid-feedback {
    color: var(--error-color);
    font-size: 0.875rem;
    margin-top: 4px;
}

.valid-feedback {
    color: var(--success-color);
    font-size: 0.875rem;
    margin-top: 4px;
}

/* 文件上传区域 */
.file-upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: border-color var(--transition-fast), background-color var(--transition-fast);
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background-color: var(--bg-hover);
}

.file-upload-area.dragover {
    border-color: var(--primary-color);
    background-color: rgba(100, 116, 139, 0.1);
}

.file-upload-icon {
    font-size: 2rem;
    margin-bottom: 12px;
    color: var(--text-muted);
}

.file-upload-text {
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.file-upload-hint {
    color: var(--text-muted);
    font-size: 0.875rem;
}

/* 进度条样式 */
.progress {
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
    margin-top: 12px;
}

.progress-bar {
    height: 100%;
    background: var(--primary-color);
    transition: width var(--transition-normal);
}

/* 递归代理样式 */
.recursive-dashboard {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.status-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.status-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.status-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.status-card.primary {
    border-left: 4px solid var(--primary-color);
}

.status-card.success {
    border-left: 4px solid var(--success-color);
}

.status-card.warning {
    border-left: 4px solid var(--warning-color);
}

.status-card.info {
    border-left: 4px solid var(--info-color);
}

.status-icon {
    font-size: 2rem;
    opacity: 0.8;
}

.status-info {
    flex: 1;
}

.status-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.status-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.status-change {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.status-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--success-color);
}

.status-indicator.offline {
    background: var(--error-color);
}

.status-indicator.warning {
    background: var(--warning-color);
}

/* 发现动态 */
.discovery-feed {
    max-height: 300px;
    overflow-y: auto;
    padding: 0.5rem;
}

.discovery-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s ease;
}

.discovery-item:hover {
    background: var(--hover-bg);
}

.discovery-item.placeholder {
    opacity: 0.6;
    font-style: italic;
}

.discovery-time {
    font-size: 0.8rem;
    color: var(--text-muted);
    min-width: 80px;
}

.discovery-domain {
    font-weight: 500;
    color: var(--primary-color);
    flex: 1;
}

.discovery-source {
    font-size: 0.8rem;
    color: var(--text-secondary);
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 分析内容 */
.analysis-content {
    display: none;
}

.analysis-content.active {
    display: block;
}

.analysis-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.stat-label {
    display: block;
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.stat-value {
    display: block;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* 安全对抗网格 */
.security-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.security-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.security-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.security-icon {
    font-size: 1.5rem;
    opacity: 0.8;
}

.security-info {
    flex: 1;
}

.security-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.security-status {
    font-size: 0.8rem;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    font-weight: 500;
    margin-bottom: 0.25rem;
    display: inline-block;
}

.security-status.enabled {
    background: var(--success-color);
    color: white;
}

.security-status.disabled {
    background: var(--error-color);
    color: white;
}

.security-rate {
    font-size: 0.8rem;
    color: var(--text-muted);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .modal-overlay {
        padding: 16px;
    }

    .modal-container {
        max-width: 100%;
        margin: 0;
    }

    .modal-header {
        padding: 20px 20px 0;
        margin-bottom: 20px;
    }

    .modal-title {
        font-size: 1.25rem;
    }

    .modal-body {
        padding: 0 20px 20px;
    }

    .modal-footer {
        padding: 20px;
        flex-direction: column-reverse;
        gap: 8px;
    }

    .modal-footer .btn {
        width: 100%;
        justify-content: center;
    }
}

/* 特殊模态框样式 */
.modal-sm .modal-container {
    max-width: 400px;
}

.modal-lg .modal-container {
    max-width: 800px;
}

.modal-xl .modal-container {
    max-width: 1200px;
}

/* 确认对话框样式 */
.confirm-dialog .modal-body {
    text-align: center;
    padding: 32px 24px;
}

.confirm-dialog .modal-icon {
    font-size: 3rem;
    margin-bottom: 16px;
}

.confirm-dialog.danger .modal-icon {
    color: var(--error-color);
}

.confirm-dialog.warning .modal-icon {
    color: var(--warning-color);
}

.confirm-dialog.info .modal-icon {
    color: var(--info-color);
}

/* 加载状态 */
.modal-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.modal-loading .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* ML分析样式 */
.ml-tabs,
.security-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.ml-content,
.security-content {
    min-height: 400px;
}

.ml-tab-content,
.security-tab-content {
    display: none;
}

.ml-tab-content.active,
.security-tab-content.active {
    display: block;
}

.ml-panel,
.security-panel {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 1rem;
}

.ml-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.ml-stat {
    text-align: center;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: 8px;
}

.ml-stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.ml-stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* 模式列表 */
.pattern-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.pattern-item {
    display: grid;
    grid-template-columns: 100px 1fr 80px 80px;
    gap: 1rem;
    align-items: center;
    padding: 0.75rem;
    background: var(--bg-secondary);
    border-radius: 6px;
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.pattern-item:hover {
    background: var(--hover-bg);
}

.pattern-type {
    font-size: 0.8rem;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    background: var(--primary-color);
    color: white;
    text-align: center;
}

.pattern-regex {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    color: var(--text-secondary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.pattern-confidence {
    font-weight: 600;
    color: var(--success-color);
    text-align: center;
}

.pattern-action {
    font-size: 0.8rem;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    text-align: center;
    font-weight: 500;
}

.pattern-action.skip {
    background: var(--warning-color);
    color: white;
}

.pattern-action.recurse {
    background: var(--success-color);
    color: white;
}

/* WAF统计 */
.waf-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.waf-stat {
    text-align: center;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: 8px;
}

.waf-stat-value {
    display: block;
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.waf-stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* 控制按钮 */
.control-btn {
    padding: 0.4rem 0.8rem;
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.control-btn:hover {
    background: var(--hover-bg);
    color: var(--text-primary);
}

.control-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* 面板状态 */
.panel-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* 认证提示 */
.auth-required {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 2rem;
    text-align: center;
    color: var(--text-secondary);
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 2px dashed var(--border-color);
    min-height: 200px;
}

.auth-required::before {
    content: "🔒";
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.6;
}

/* 标签按钮 */
.tab-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border-radius: 8px 8px 0 0;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
    font-weight: 500;
    border-bottom: 2px solid transparent;
}

.tab-btn:hover {
    background: var(--hover-bg);
    color: var(--text-primary);
}

.tab-btn.active {
    background: var(--card-bg);
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .status-cards {
        grid-template-columns: 1fr;
    }

    .panel-row {
        flex-direction: column;
    }

    .security-grid {
        grid-template-columns: 1fr;
    }

    .analysis-stats {
        grid-template-columns: 1fr;
    }

    .ml-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .pattern-item {
        grid-template-columns: 1fr;
        gap: 0.5rem;
        text-align: center;
    }

    .ml-tabs,
    .security-tabs {
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .tab-btn {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    .radio-option {
        padding: 0.75rem;
        gap: 0.5rem;
    }

    .radio-text {
        font-size: 0.9rem;
    }

    .radio-desc {
        font-size: 0.8rem;
    }
}

/* 域名方向选择样式 */
.domain-direction-selection,
.domain-type-selection {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 0.5rem;
}

.radio-option {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: var(--bg-secondary);
}

.radio-option:hover {
    border-color: var(--primary-color);
    background: var(--hover-bg);
    transform: translateY(-1px);
}

.radio-option input[type="radio"] {
    display: none;
}

.radio-custom {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    position: relative;
    flex-shrink: 0;
    transition: all 0.2s ease;
    margin-top: 2px;
}

.radio-custom::after {
    content: '';
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: var(--primary-color);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    transition: transform 0.2s ease;
}

.radio-option input[type="radio"]:checked+.radio-custom {
    border-color: var(--primary-color);
    background: var(--primary-color-light);
}

.radio-option input[type="radio"]:checked+.radio-custom::after {
    transform: translate(-50%, -50%) scale(1);
}

.radio-text {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.radio-desc {
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.radio-option input[type="radio"]:checked~.radio-text {
    color: var(--primary-color);
}

.radio-option input[type="radio"]:checked~.radio-desc {
    color: var(--text-primary);
}

/* 必选标记样式 */
.required-mark {
    color: #e74c3c;
    font-weight: bold;
    margin-left: 0.25rem;
}

.form-group label.required {
    font-weight: 600;
}

/* 表单验证错误样式 */
.form-group.error .radio-option {
    border-color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
}

.form-group.error .radio-custom {
    border-color: #e74c3c;
}

.error-message {
    color: #e74c3c;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.error-message::before {
    content: "⚠️";
    font-size: 0.75rem;
}