# API 接口文档

## 📋 基础信息

- **Base URL**: `http://localhost:1319/api`
- **认证方式**: JWT Token
- **数据格式**: JSON
- **字符编码**: UTF-8
- **版本**: v2024.12 (最新更新)

## 🎯 **最新更新**

### 新增功能
- ✅ **递归代理API**: 完整的递归代理管理接口
- ✅ **机器学习API**: URL模式识别和内容分析接口
- ✅ **域名池管理**: 上游/下游域名池管理
- ✅ **实时监控**: 递归会话状态和统计监控
- ✅ **安全优化**: 修复关键安全问题，提升API安全性

## 🔐 认证

### 登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin888"
}
```

**响应**:
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "message": "登录成功"
}
```

### 使用 Token
在后续请求中添加认证头：
```http
Authorization: Bearer <your-jwt-token>
```

## 🏥 健康检查

### 服务状态
```http
GET /api/health
```

**响应**:
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "uptime": 3600,
  "timestamp": "2025-06-10T10:00:00Z"
}
```

## 📊 性能监控

### 获取性能状态
```http
GET /api/perf/status
Accept: text/html
```

**响应**: HTMX 格式的 HTML 片段，包含实时性能数据

### 性能指标详情
```http
GET /api/perf/metrics
```

**响应**:
```json
{
  "cpu_usage": 15.2,
  "memory_usage": 256.8,
  "requests_per_second": 120,
  "response_time_avg": 45.2,
  "active_connections": 25
}
```

## 💾 缓存管理

### 获取缓存状态
```http
GET /api/cache/status
```

**响应**:
```json
{
  "enabled": true,
  "hit_rate": 85.6,
  "total_requests": 1000,
  "cache_hits": 856,
  "cache_misses": 144,
  "memory_usage": "128MB",
  "entries_count": 450
}
```

### 清空缓存
```http
DELETE /api/cache/clear
```

**响应**:
```json
{
  "success": true,
  "message": "缓存已清空",
  "cleared_entries": 450
}
```

### 缓存配置
```http
POST /api/cache/config
Content-Type: application/json

{
  "max_size": "256MB",
  "ttl": 3600,
  "enabled": true
}
```

## 🚫 黑名单管理

### 获取黑名单
```http
GET /api/blacklist
```

**响应**:
```json
{
  "blacklist": [
    {
      "ip": "*************",
      "reason": "恶意攻击",
      "added_at": "2025-06-10T09:00:00Z",
      "expires_at": "2025-06-11T09:00:00Z"
    }
  ],
  "total": 1
}
```

### 添加 IP 到黑名单
```http
POST /api/blacklist
Content-Type: application/json

{
  "ip": "*************",
  "reason": "恶意攻击",
  "duration": 3600
}
```

**响应**:
```json
{
  "success": true,
  "message": "IP 已添加到黑名单",
  "ip": "*************"
}
```

### 从黑名单移除 IP
```http
DELETE /api/blacklist
Content-Type: application/json

{
  "ip": "*************"
}
```

## 🌐 域名管理

### 获取域名映射
```http
GET /api/domains
```

**响应**:
```json
{
  "domains": [
    {
      "id": "1",
      "domain": "api.example.com",
      "upstream": "backend",
      "enabled": true,
      "ssl_enabled": true,
      "created_at": "2025-06-10T08:00:00Z"
    }
  ],
  "total": 1
}
```

### 添加域名映射
```http
POST /api/domains
Content-Type: application/json

{
  "domain": "api.example.com",
  "upstream": "backend",
  "ssl_enabled": true,
  "headers": {
    "X-Custom-Header": "value"
  }
}
```

### 更新域名映射
```http
PUT /api/domains/{id}
Content-Type: application/json

{
  "domain": "api.example.com",
  "upstream": "backend-v2",
  "enabled": true
}
```

### 删除域名映射
```http
DELETE /api/domains/{id}
```

## 🛣️ 路由规则管理

### 获取路由规则
```http
GET /api/routes
```

**响应**:
```json
{
  "routes": [
    {
      "id": "1",
      "path": "/api/*",
      "upstream": "backend",
      "methods": ["GET", "POST"],
      "headers": {
        "X-Service": "api"
      },
      "enabled": true
    }
  ],
  "total": 1
}
```

### 添加路由规则
```http
POST /api/routes
Content-Type: application/json

{
  "path": "/api/v2/*",
  "upstream": "backend-v2",
  "methods": ["GET", "POST", "PUT", "DELETE"],
  "priority": 100,
  "headers": {
    "X-API-Version": "v2"
  }
}
```

### 删除路由规则
```http
DELETE /api/routes/{id}
```

## 🔄 递归代理管理

### 获取递归代理状态
```http
GET /api/recursive-proxy/status
```

**响应**:
```json
{
  "success": true,
  "data": {
    "enabled": true,
    "running": true,
    "active_sessions": 5,
    "total_requests": 1250,
    "success_rate": 95.2,
    "avg_response_time": 245,
    "discovered_domains": 128,
    "cache_hit_rate": 78.5
  }
}
```

### 启动递归代理
```http
POST /api/recursive-proxy/start
```

**响应**:
```json
{
  "success": true,
  "message": "递归代理服务已启动"
}
```

### 停止递归代理
```http
POST /api/recursive-proxy/stop
```

### 获取递归代理统计
```http
GET /api/recursive-proxy/stats
```

**响应**:
```json
{
  "success": true,
  "data": {
    "total_sessions": 1250,
    "successful_sessions": 1190,
    "failed_sessions": 60,
    "avg_depth": 2.3,
    "max_depth_reached": 15,
    "total_discovered_urls": 5420,
    "unique_domains": 128,
    "cache_entries": 892,
    "processing_time_avg": 245
  }
}
```

### 获取活跃会话
```http
GET /api/recursive-proxy/sessions
```

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "session_id": "sess_123456",
      "original_url": "https://example.com",
      "client_ip": "*************",
      "start_time": "2025-06-10T10:30:00Z",
      "current_depth": 2,
      "discovered_count": 15,
      "status": "processing"
    }
  ]
}
```

### 获取递归配置
```http
GET /api/recursive-proxy/config
```

**响应**:
```json
{
  "success": true,
  "data": {
    "enabled": true,
    "max_depth": 5,
    "max_discovered_domains": 100,
    "request_timeout": 30,
    "cache_enabled": true,
    "cache_ttl": 3600,
    "user_agent_rotation": true,
    "respect_robots_txt": true
  }
}
```

### 更新递归配置
```http
PUT /api/recursive-proxy/config
Content-Type: application/json

{
  "max_depth": 3,
  "max_discovered_domains": 50,
  "request_timeout": 20,
  "cache_ttl": 1800
}
```

## 🧠 机器学习API

### 获取ML状态
```http
GET /api/ml/status
```

**响应**:
```json
{
  "success": true,
  "data": {
    "url_patterns_learned": 0,
    "content_analysis_cache_size": 0,
    "adaptive_strategies_active": 0,
    "learning_enabled": false
  }
}
```

### 获取URL模式
```http
GET /api/ml/url-patterns
```

**响应**:
```json
{
  "success": true,
  "data": {
    "patterns": []
  }
}
```

### 获取内容分析
```http
GET /api/ml/content-analysis
```

### 获取自适应策略
```http
GET /api/ml/adaptive-strategies
```

## 🌐 域名池管理

### 获取域名池状态
```http
GET /api/domain-pool/status
```

**响应**:
```json
{
  "success": true,
  "data": {
    "upstream_count": 25,
    "downstream_count": 15,
    "active_mappings": 12,
    "total_requests": 5420,
    "success_rate": 94.8
  }
}
```

### 获取上游域名列表
```http
GET /api/domain-pool/upstream
```

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": "up_001",
      "domain": "api.example.com",
      "status": "active",
      "health_score": 95,
      "last_check": "2025-06-10T10:25:00Z",
      "response_time": 120
    }
  ]
}
```

### 获取下游域名列表
```http
GET /api/domain-pool/downstream
```

### 添加域名到池
```http
POST /api/domain-pool/domains
Content-Type: application/json

{
  "domain": "new-api.example.com",
  "type": "upstream",
  "priority": 1,
  "health_check_enabled": true
}
```

### 删除域名
```http
DELETE /api/domain-pool/domains/{domain_id}
```

## ⚙️ 配置管理

### 获取系统配置
```http
GET /api/config
```

**响应**:
```json
{
  "server": {
    "listen": "0.0.0.0:8080",
    "workers": 4
  },
  "upstreams": [
    {
      "name": "backend",
      "servers": [
        {
          "addr": "127.0.0.1:3000",
          "weight": 1
        }
      ],
      "load_balance": "round_robin"
    }
  ],
  "security": {
    "rate_limit": {
      "requests_per_minute": 60
    }
  }
}
```

### 保存系统配置
```http
POST /api/config
Content-Type: application/json

{
  "server": {
    "listen": "0.0.0.0:8080",
    "workers": 4
  },
  "upstreams": [
    {
      "name": "backend",
      "servers": [
        {
          "addr": "127.0.0.1:3000",
          "weight": 1
        }
      ]
    }
  ]
}
```

## 🔒 证书管理

### 获取证书状态
```http
GET /api/cert
```

**响应**:
```json
{
  "certificates": [
    {
      "domain": "example.com",
      "issuer": "Let's Encrypt",
      "valid_from": "2025-01-01T00:00:00Z",
      "valid_until": "2025-12-31T23:59:59Z",
      "status": "valid"
    }
  ],
  "auto_renewal": true
}
```

### 上传证书
```http
POST /api/cert/upload
Content-Type: multipart/form-data

cert_file: <certificate.pem>
key_file: <private.key>
domain: example.com
```

## 🔍 日志和审计

### 获取访问日志
```http
GET /api/logs/access?limit=100&offset=0
```

**响应**:
```json
{
  "logs": [
    {
      "timestamp": "2025-06-10T10:30:00Z",
      "ip": "***********",
      "method": "GET",
      "path": "/api/health",
      "status": 200,
      "response_time": 12,
      "user_agent": "curl/7.68.0"
    }
  ],
  "total": 1,
  "page": 1
}
```

### 获取错误日志
```http
GET /api/logs/error?limit=50
```

### 获取安全事件
```http
GET /api/logs/security
```

## 📈 统计分析

### 获取流量统计
```http
GET /api/stats/traffic?period=24h
```

**响应**:
```json
{
  "period": "24h",
  "total_requests": 10000,
  "unique_visitors": 500,
  "bandwidth_usage": "1.2GB",
  "avg_response_time": 45.2,
  "status_codes": {
    "200": 8500,
    "404": 1000,
    "500": 500
  }
}
```

### 获取上游服务器状态
```http
GET /api/stats/upstreams
```

**响应**:
```json
{
  "upstreams": [
    {
      "name": "backend",
      "servers": [
        {
          "addr": "127.0.0.1:3000",
          "status": "healthy",
          "requests": 5000,
          "failures": 5,
          "response_time": 42.1
        }
      ]
    }
  ]
}
```

## 🚨 错误代码

| 状态码 | 错误类型 | 描述 |
|--------|----------|------|
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 未认证或 Token 无效 |
| 403 | Forbidden | 权限不足 |
| 404 | Not Found | 资源不存在 |
| 409 | Conflict | 资源冲突（如重复添加） |
| 422 | Unprocessable Entity | 参数验证失败 |
| 500 | Internal Server Error | 服务器内部错误 |

### 错误响应格式
```json
{
  "success": false,
  "error": "INVALID_PARAMETER",
  "message": "域名格式不正确",
  "details": {
    "field": "domain",
    "value": "invalid-domain"
  }
}
```

## 📝 使用示例

### 完整的配置流程
```bash
# 1. 登录获取 Token
TOKEN=$(curl -s -X POST http://localhost:1319/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin888"}' | jq -r '.token')

# 2. 添加上游服务器
curl -X POST http://localhost:1319/api/config \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "upstreams": [
      {
        "name": "my-backend",
        "servers": [{"addr": "127.0.0.1:3000", "weight": 1}]
      }
    ]
  }'

# 3. 添加域名映射
curl -X POST http://localhost:1319/api/domains \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "domain": "api.mysite.com",
    "upstream": "my-backend",
    "ssl_enabled": true
  }'

# 4. 添加路由规则
curl -X POST http://localhost:1319/api/routes \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "path": "/api/*",
    "upstream": "my-backend",
    "methods": ["GET", "POST"]
  }'
```

## 🔄 Webhooks

### 配置变更通知
```http
POST /api/webhooks/config
Content-Type: application/json

{
  "url": "https://your-app.com/webhook",
  "events": ["config.changed", "domain.added"],
  "secret": "your-webhook-secret"
}
```

## 📱 WebSocket 连接

### 实时监控
```javascript
const ws = new WebSocket('ws://localhost:1319/api/ws/monitor');
ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('实时监控数据:', data);
};
```

---

**相关文档**: [部署指南](deployment.md) | [安全配置](security.md)