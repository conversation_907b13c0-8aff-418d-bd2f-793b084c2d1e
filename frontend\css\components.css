/* ==============================================
   SM镜像系统 - 组件样式文件 v2.0
   包含所有UI组件的详细样式
   ============================================== */

/* ==============================================
   域名池统计卡片
   ============================================== */
.pool-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all var(--transition-fast);
}

.stat-card:hover {
    border-color: var(--border-light);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    font-size: 2rem;
    opacity: 0.8;
}

.stat-info {
    flex: 1;
}

.stat-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

/* ==============================================
   任务统计
   ============================================== */
.task-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
}

.task-stat-item {
    text-align: center;
    flex: 1;
}

.task-stat-item .stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
}

.task-stat-item .stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
}

/* ==============================================
   任务控制面板
   ============================================== */
.task-controls,
.log-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    flex-wrap: wrap;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.control-group label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    white-space: nowrap;
}

/* ==============================================
   监控面板
   ============================================== */
.monitoring-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.monitor-panel {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
}

.panel-controls {
    display: flex;
    gap: 0.25rem;
}

.control-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    transition: all var(--transition-fast);
}

.control-btn.active,
.control-btn:hover {
    background: var(--primary-color);
    color: white;
}

.panel-select {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* ==============================================
   表格样式
   ============================================== */
.table-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-tertiary);
}

.table-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.result-count {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.table-controls {
    display: flex;
    gap: 0.5rem;
}

.data-table {
    overflow-x: auto;
}

.data-table table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    background: var(--bg-tertiary);
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.data-table tr:hover {
    background: var(--bg-hover);
}

.domain-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.domain-name {
    font-weight: 500;
    color: var(--text-primary);
}

.domain-desc {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.status-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.status-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-badge.status-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.status-badge.status-error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.status-badge.status-unknown {
    background: rgba(148, 163, 184, 0.1);
    color: var(--text-muted);
}

.type-badge {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.75rem;
}

.action-buttons {
    display: flex;
    gap: 0.25rem;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* ==============================================
   空状态
   ============================================== */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-muted);
}

.empty-icon {
    font-size: 4rem;
    opacity: 0.5;
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 1.25rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.empty-state p {
    margin-bottom: 1.5rem;
}

/* ==============================================
   性能监控组件
   ============================================== */
.health-status {
    padding: 1rem;
}

.health-indicator {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.status-text {
    font-weight: 500;
    color: var(--text-primary);
}

.health-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.health-item {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
}

.health-item .label {
    color: var(--text-secondary);
}

.health-item .value {
    color: var(--text-primary);
    font-weight: 500;
}

.perf-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.perf-item {
    text-align: center;
}

.perf-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.perf-value {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.perf-bar {
    width: 100%;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: 2px;
    overflow: hidden;
}

.perf-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width var(--transition-normal);
}

/* ==============================================
   图表容器
   ============================================== */
.chart-container {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-placeholder {
    color: var(--text-muted);
    font-style: italic;
}

/* ==============================================
   活动日志
   ============================================== */
.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    gap: 0.75rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.875rem;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-time {
    color: var(--text-muted);
    font-size: 0.75rem;
    min-width: 4rem;
}

.activity-text {
    color: var(--text-secondary);
}

/* ==============================================
   日志容器
   ============================================== */
.log-container {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    height: 400px;
    overflow-y: auto;
    padding: 1rem;
    font-family: 'Courier New', monospace;
}

.log-entry {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    line-height: 1.4;
}

.log-time {
    color: var(--text-muted);
    min-width: 9rem;
    font-size: 0.75rem;
}

.log-level {
    min-width: 3rem;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
}

.log-level.log-info {
    color: var(--info-color);
}

.log-level.log-warn {
    color: var(--warning-color);
}

.log-level.log-error {
    color: var(--error-color);
}

.log-level.log-debug {
    color: var(--text-muted);
}

.log-message {
    color: var(--text-primary);
    flex: 1;
}

/* ==============================================
   导入模态框样式
   ============================================== */
.import-methods {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.import-method {
    flex: 1;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    text-align: center;
    transition: all var(--transition-fast);
}

.import-method:hover {
    border-color: var(--border-light);
}

.import-method.active {
    border-color: var(--primary-color);
    background: rgba(59, 130, 246, 0.05);
}

.import-method h4 {
    font-size: 1rem;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.import-method p {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.import-content {
    display: none;
}

.import-content.active {
    display: block;
}

.import-content textarea {
    width: 100%;
    min-height: 200px;
    padding: 1rem;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    resize: vertical;
}

.file-upload {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 2rem;
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    text-align: center;
}

.file-upload button {
    flex-shrink: 0;
}

#file-name {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* ==============================================
   用户菜单
   ============================================== */
.dropdown-menu {
    position: fixed;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    min-width: 180px;
    z-index: 10000;
    overflow: hidden;
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.menu-item:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.menu-icon {
    font-size: 0.875rem;
    width: 1rem;
    text-align: center;
}

.menu-text {
    font-size: 0.875rem;
}

.menu-divider {
    height: 1px;
    background: var(--border-color);
    margin: 0.25rem 0;
}

/* ==============================================
   复选框样式
   ============================================== */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkmark {
    position: relative;
    display: inline-block;
    width: 16px;
    height: 16px;
}

/* ==============================================
   通知系统
   ============================================== */
#notification-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 10000;
    pointer-events: none;
}

.notification {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 0.5rem;
    box-shadow: var(--shadow-lg);
    max-width: 400px;
    pointer-events: auto;
    transform: translateX(100%);
    opacity: 0;
    transition: all var(--transition-normal);
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.error {
    border-left: 4px solid var(--error-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

.notification.info {
    border-left: 4px solid var(--info-color);
}

.notification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.notification-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    font-size: 1rem;
}

.notification-message {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.4;
}

/* ==============================================
   工具提示
   ============================================== */
[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-primary);
    color: var(--text-primary);
    padding: 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1000;
    box-shadow: var(--shadow-md);
    pointer-events: none;
}

/* ==============================================
   配置表单样式
   ============================================== */
.config-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

.config-content {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-section {
    margin-bottom: 2rem;
}

.form-section h4 {
    font-size: 1.125rem;
    color: var(--text-primary);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

/* ==============================================
   黑名单标签页
   ============================================== */
.blacklist-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
}

.blacklist-content {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    min-height: 400px;
}

/* ==============================================
   域名池标签页
   ============================================== */
.pool-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
}

.pool-tab-content {
    display: none;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    min-height: 400px;
}

.pool-tab-content.active {
    display: block;
}

.operations-panel,
.pool-settings {
    text-align: center;
    padding: 2rem;
    color: var(--text-muted);
}

/* ==============================================
   错误消息
   ============================================== */
.error-message {
    color: var(--error-color);
    text-align: center;
    padding: 1rem;
    font-style: italic;
}

/* ==============================================
   全屏功能
   ============================================== */
.fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 99999 !important;
    background: var(--bg-primary) !important;
}

/* ==============================================
   登录表单优化
   ============================================== */
.login-form {
    width: 100%;
}

.form-options {
    margin: 1rem 0;
}

.login-info {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.info-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
}

.info-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
    opacity: 0.8;
}

.info-content h4,
.info-content strong {
    color: var(--text-primary);
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.info-content p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0;
}

/* ==============================================
   响应式优化
   ============================================== */
@media (max-width: 1024px) {
    .form-row {
        grid-template-columns: 1fr;
    }

    .perf-grid {
        grid-template-columns: 1fr;
    }

    .task-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .task-stat-item .stat-number {
        font-size: 1.5rem;
    }
}

@media (max-width: 768px) {

    .pool-stats-grid,
    .monitoring-grid {
        grid-template-columns: 1fr;
    }

    .import-methods {
        flex-direction: column;
    }

    .table-actions {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .data-table {
        font-size: 0.875rem;
    }

    .data-table th,
    .data-table td {
        padding: 0.5rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }
}

@media (max-width: 480px) {
    .control-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .log-container {
        height: 300px;
    }

    .log-entry {
        flex-direction: column;
        gap: 0.25rem;
    }

    .log-time {
        min-width: auto;
    }

    .notification {
        max-width: calc(100vw - 2rem);
    }
}

/* ==============================================
   侧边栏切换修复 - 添加状态保护
   ============================================== */
.sidebar {
    transition: transform var(--transition-normal) ease-in-out,
        width var(--transition-normal) ease-in-out !important;
}

/* 确保收缩状态的样式优先级 */
.sidebar.collapsed {
    transform: translateX(-100%) !important;
    width: 0 !important;
}

/* 桌面端侧边栏收缩状态 */
@media (min-width: 769px) {
    .sidebar.collapsed {
        transform: translateX(0) !important;
        width: 60px !important;
    }

    .sidebar.collapsed .nav-text,
    .sidebar.collapsed .brand-text {
        opacity: 0 !important;
        visibility: hidden !important;
    }

    .sidebar.collapsed .nav-item {
        justify-content: center !important;
    }
}

/* 移动端确保完全隐藏 */
@media (max-width: 768px) {
    .sidebar.collapsed {
        transform: translateX(-100%) !important;
    }
}

/* 切换按钮状态修复 */
.sidebar-toggle {
    transition: all var(--transition-fast) !important;
    min-width: 40px !important;
    min-height: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.sidebar-toggle:focus {
    outline: 2px solid var(--primary-color) !important;
    outline-offset: 2px !important;
}

/* Body状态修复 */
body.sidebar-collapsed .main-content {
    margin-left: 0 !important;
}

@media (min-width: 769px) {
    body.sidebar-collapsed .main-content {
        margin-left: 60px !important;
    }
}

/* 强制重置任何可能的冲突样式 */
.sidebar * {
    pointer-events: auto !important;
}

.sidebar.collapsed * {
    pointer-events: auto !important;
}

/* =====================================
   现代化模态框样式
   ===================================== */

/* 模态框遮罩层 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1050;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(4px);
}

/* 模态框显示状态 */
.modal.show {
    opacity: 1;
    visibility: visible;
}

/* 模态框背景遮罩 */
.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
}

/* 模态框容器 */
.modal-container {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    width: 500px;
    background: var(--bg-color);
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15),
        0 8px 24px rgba(0, 0, 0, 0.1);
    transform: scale(0.9) translateY(-20px);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

/* 模态框显示动画 */
.modal.show .modal-container {
    transform: scale(1) translateY(0);
}

/* 不同尺寸的模态框 */
.modal-container.modal-sm {
    width: 360px;
}

.modal-container.modal-lg {
    width: 720px;
}

/* 模态框头部 */
.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px 16px;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* 关闭按钮 */
.modal-close {
    background: none;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.modal-close:hover {
    background: var(--hover-bg);
    color: var(--text-primary);
}

/* 模态框主体 */
.modal-body {
    padding: 20px 24px;
    max-height: 60vh;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.modal-body::-webkit-scrollbar {
    width: 6px;
}

.modal-body::-webkit-scrollbar-track {
    background: transparent;
}

.modal-body::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* 模态框底部 */
.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 24px 20px;
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

/* =====================================
   确认对话框样式
   ===================================== */

.confirm-dialog {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 8px 0;
}

.confirm-icon {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-secondary);
    font-size: 20px;
}

.confirm-message {
    flex: 1;
    font-size: 15px;
    line-height: 1.5;
    color: var(--text-primary);
}

/* =====================================
   加载对话框样式
   ===================================== */

.loading-dialog {
    text-align: center;
    padding: 20px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    margin: 0 auto 16px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-message {
    color: var(--text-secondary);
    font-size: 14px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* =====================================
   表单模态框样式
   ===================================== */

.modal-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.form-group label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-color);
    color: var(--text-primary);
    font-size: 14px;
    transition: all 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-color-alpha);
}

.form-group textarea {
    min-height: 80px;
    resize: vertical;
}

/* 复选框样式 */
.checkbox-label {
    display: flex !important;
    flex-direction: row !important;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    user-select: none;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 3px;
    position: relative;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked+.checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked+.checkmark::after {
    content: '';
    position: absolute;
    top: 1px;
    left: 5px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* =====================================
   单选按钮组样式
   ===================================== */

.radio-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 8px;
}

.radio-option {
    display: flex !important;
    align-items: flex-start;
    gap: 12px;
    padding: 18px;
    border: 3px solid var(--border-color);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: var(--bg-color);
    user-select: none;
    position: relative;
    overflow: hidden;
}

.radio-option:hover {
    border-color: var(--primary-color);
    background: var(--bg-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 选中状态的强烈视觉效果 */
.radio-option:has(input[type="radio"]:checked) {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(100, 116, 139, 0.4);
}

.radio-option:has(input[type="radio"]:checked)::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(100%);
    }
}

.radio-option input[type="radio"] {
    display: none;
}

.radio-button {
    width: 24px;
    height: 24px;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
    margin-top: 2px;
    background: var(--bg-color);
}

.radio-option input[type="radio"]:checked+.radio-button {
    border-color: white;
    background: white;
    box-shadow: 0 0 0 3px var(--primary-color), 0 4px 12px rgba(100, 116, 139, 0.3);
    transform: scale(1.1);
}

.radio-option input[type="radio"]:checked+.radio-button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 12px;
    background: var(--primary-color);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(1);
    animation: radioCheck 0.3s ease-out;
}

@keyframes radioCheck {
    0% {
        transform: translate(-50%, -50%) scale(0);
    }

    50% {
        transform: translate(-50%, -50%) scale(1.3);
    }

    100% {
        transform: translate(-50%, -50%) scale(1);
    }
}

.radio-option input[type="radio"]:checked~.radio-label .radio-title {
    color: white;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.radio-option input[type="radio"]:checked~.radio-label .radio-desc {
    color: rgba(255, 255, 255, 0.9);
}

.radio-label {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 6px;
    transition: all 0.3s ease;
}

.radio-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.radio-desc {
    font-size: 14px;
    color: var(--text-muted);
    line-height: 1.5;
    transition: all 0.3s ease;
}

/* 添加选中状态的图标 */
.radio-option input[type="radio"]:checked~.radio-label .radio-title::after {
    content: '✓';
    margin-left: auto;
    font-size: 18px;
    font-weight: bold;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    animation: checkmark 0.3s ease-out;
}

@keyframes checkmark {
    0% {
        transform: scale(0) rotate(180deg);
        opacity: 0;
    }

    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 480px) {
    .radio-option {
        padding: 12px;
        gap: 10px;
    }

    .radio-button {
        width: 18px;
        height: 18px;
    }

    .radio-title {
        font-size: 14px;
    }

    .radio-desc {
        font-size: 12px;
    }
}

/* =====================================
   按钮样式增强
   ===================================== */

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    min-height: 36px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-color-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--primary-color-alpha);
}

.btn-outline {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

.btn-outline:hover:not(:disabled) {
    background: var(--hover-bg);
    border-color: var(--primary-color);
}

.btn-sm {
    padding: 6px 12px;
    font-size: 13px;
    min-height: 32px;
}

.btn-lg {
    padding: 12px 24px;
    font-size: 16px;
    min-height: 44px;
}

/* =====================================
   文本颜色工具类
   ===================================== */

.text-info {
    color: #3498db;
}

.text-warning {
    color: #f39c12;
}

.text-error {
    color: #e74c3c;
}

.text-success {
    color: #27ae60;
}

/* =====================================
   响应式设计
   ===================================== */

@media (max-width: 768px) {
    .modal-container {
        width: 95vw;
        margin: 20px;
        max-height: 85vh;
    }

    .modal-container.modal-sm {
        width: 90vw;
    }

    .modal-container.modal-lg {
        width: 95vw;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding-left: 16px;
        padding-right: 16px;
    }

    .confirm-dialog {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .modal-footer {
        flex-direction: column-reverse;
        gap: 8px;
    }

    .modal-footer .btn {
        width: 100%;
    }
}

/* =====================================
   深色模式适配
   ===================================== */

[data-theme="dark"] .modal-overlay {
    background: rgba(0, 0, 0, 0.7);
}

[data-theme="dark"] .modal-container {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3),
        0 8px 24px rgba(0, 0, 0, 0.2);
}