//! 统一配置系统 - 简化的配置接口
//!
//! 这个模块提供：
//! 1. 统一的配置接口
//! 2. 配置格式转换
//! 3. 配置验证和合并

use crate::ConfigError;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

// 定义基础配置结构
#[derive(Debug, Clone, Serialize, Deserialize, Default, PartialEq)]
pub struct ProxyConfig {
    pub server: ServerConfig,
    pub upstreams: Vec<UpstreamConfig>,
    pub routes: Vec<RouteConfig>,
    pub cache: Option<CacheConfig>,
    pub security: Option<SecurityConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default, PartialEq)]
pub struct ServerConfig {
    // 保持兼容性的原有字段
    pub listen: String,
    pub workers: Option<usize>,

    // 新增双端口配置字段
    pub frontend_addr: Option<String>, // 前端服务地址 (API + 静态文件)
    pub backend_addr: Option<String>,  // 后端代理核心地址
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct UpstreamConfig {
    pub name: String,
    pub servers: Vec<ServerEntry>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct ServerEntry {
    pub addr: String,
    // 移除权重配置，不再支持负载均衡
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct RouteConfig {
    pub path: String,
    pub target: String,
    pub methods: Option<Vec<String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default, PartialEq)]
pub struct CacheConfig {
    pub max_size: Option<u64>,
    pub default_ttl: Option<u64>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default, PartialEq)]
pub struct SecurityConfig {
    pub tls_min_version: Option<String>,
    pub tls_ciphers: Option<Vec<String>>,
    pub jwt_secret: Option<String>,
    pub rate_limit: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default, PartialEq)]
pub struct CorsConfig {
    pub enabled: bool,
    pub origins: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default, PartialEq)]
pub struct HealthCheckConfig {
    pub enabled: bool,
    pub path: String,
    pub interval: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default, PartialEq)]
pub struct RateLimitConfig {
    pub enabled: bool,
    pub requests_per_minute: u32,
}

// 类型别名
pub type UnifiedConfig = ProxyConfig;
pub type Route = RouteConfig;
pub type ProxyServer = ServerConfig;

/// 配置转换器 - 统一所有转换功能
pub struct ConfigConverter;

impl ConfigConverter {
    /// 验证配置的完整性
    pub fn validate_config(config: &ProxyConfig) -> Result<(), ConfigError> {
        crate::validation::ConfigValidator::validate_config(config)
            .map_err(|errs| ConfigError::ValidationError(errs.join("; ")))
    }

    /// 智能配置合并
    pub fn merge_configs(
        base: &ProxyConfig,
        overlay: &ProxyConfig,
    ) -> Result<ProxyConfig, ConfigError> {
        let mut merged = base.clone();

        // 使用宏简化合并逻辑
        macro_rules! merge_field {
            ($field:ident) => {
                if overlay.$field != Default::default() {
                    merged.$field = overlay.$field.clone();
                }
            };
        }

        merge_field!(server);

        // 智能合并集合类型
        Self::merge_collections(&mut merged.upstreams, &overlay.upstreams, |u| &u.name);
        Self::merge_collections(&mut merged.routes, &overlay.routes, |r| &r.path);

        merge_field!(cache);
        merge_field!(security);

        Self::validate_config(&merged)?;
        Ok(merged)
    }

    /// 通用集合合并函数
    fn merge_collections<T, K>(base: &mut Vec<T>, overlay: &[T], key_fn: fn(&T) -> &K)
    where
        T: Clone,
        K: PartialEq,
    {
        for overlay_item in overlay {
            let key = key_fn(overlay_item);
            if let Some(pos) = base.iter().position(|item| key_fn(item) == key) {
                base[pos] = overlay_item.clone();
            } else {
                base.push(overlay_item.clone());
            }
        }
    }

    /// 从环境变量构建配置覆盖
    pub fn from_env_overrides(prefix: &str) -> HashMap<String, String> {
        std::env::vars()
            .filter_map(|(key, value)| {
                key.strip_prefix(prefix)
                    .map(|config_key| (config_key.to_lowercase().replace('_', "."), value))
            })
            .collect()
    }

    /// 应用环境变量覆盖
    pub fn apply_env_overrides(
        config: &mut ProxyConfig,
        overrides: &HashMap<String, String>,
    ) -> Result<(), ConfigError> {
        use crate::validation::FieldAccessor;

        for (key, value) in overrides {
            FieldAccessor::set_field_value(config, key, value)?;
        }

        Self::validate_config(config)
    }
}

// 移除重复的ConfigLoader实现，统一使用loader.rs中的版本
// 重新导出loader模块中的ConfigLoader
pub use crate::loader::ConfigLoader;

/// 简化的配置管理器
pub struct ConfigManager {
    config: ProxyConfig,
    version: u64,
}

impl ConfigManager {
    /// 创建配置管理器
    pub fn new(config: ProxyConfig) -> Result<Self, ConfigError> {
        ConfigConverter::validate_config(&config)?;
        Ok(Self { config, version: 1 })
    }

    /// 获取当前配置
    pub fn config(&self) -> &ProxyConfig {
        &self.config
    }

    /// 获取配置版本
    pub fn version(&self) -> u64 {
        self.version
    }

    /// 原子更新配置
    pub fn update(&mut self, new_config: ProxyConfig) -> Result<u64, ConfigError> {
        ConfigConverter::validate_config(&new_config)?;
        self.config = new_config;
        self.version += 1;
        Ok(self.version)
    }

    /// 合并更新配置
    pub fn merge_update(&mut self, overlay: ProxyConfig) -> Result<u64, ConfigError> {
        let merged = ConfigConverter::merge_configs(&self.config, &overlay)?;
        self.config = merged;
        self.version += 1;
        Ok(self.version)
    }
}

// 便利函数 - 使用统一的ConfigLoader
pub async fn load_config(path: &str) -> Result<ProxyConfig, ConfigError> {
    // 使用同步版本的ConfigLoader，避免重复实现
    ConfigLoader::load_from_file(path)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_config_validation() {
        let config = ProxyConfig::default();
        assert!(ConfigConverter::validate_config(&config).is_ok());
    }

    #[test]
    fn test_env_overrides() {
        std::env::set_var("PROXY_SERVER_WORKERS", "8");
        let overrides = ConfigConverter::from_env_overrides("PROXY_");
        assert_eq!(overrides.get("server.workers"), Some(&"8".to_string()));
        std::env::remove_var("PROXY_SERVER_WORKERS");
    }

    #[test]
    fn test_config_manager() {
        let config = ProxyConfig::default();
        let mut manager = ConfigManager::new(config).unwrap();
        assert_eq!(manager.version(), 1);

        let new_config = ProxyConfig::default();
        let version = manager.update(new_config).unwrap();
        assert_eq!(version, 2);
    }
}
