# Cargo配置 - 针对1核1G服务器优化

[build]
# 单线程编译，避免内存不足
jobs = 1

[net]
# 网络优化
retry = 5
# timeout = 120  # 注释掉：此配置项在新版本Cargo中已废弃
# 如需设置网络超时，请使用环境变量: CARGO_NET_TIMEOUT=120

[profile.release]
# 优化编译配置，减少内存使用
opt-level = 2          # 适中的优化级别
debug = false          # 不包含调试信息
strip = true           # 去除符号表，减小二进制大小
lto = "thin"           # 轻量级链接时优化
codegen-units = 1      # 减少代码生成单元，节省内存
panic = "abort"        # 使用abort而不是unwind，减小二进制大小

[profile.dev]
# 开发配置，快速编译
opt-level = 0
debug = true
strip = false
lto = false
codegen-units = 16

# 针对MongoDB的特殊优化
[profile.release.package.mongodb]
opt-level = 2          # MongoDB包使用适中优化

[profile.release.package.bson]
opt-level = 2          # BSON包使用适中优化

# 针对reqwest的优化
[profile.release.package.reqwest]
opt-level = 2

# 针对tokio的优化
[profile.release.package.tokio]
opt-level = 2
