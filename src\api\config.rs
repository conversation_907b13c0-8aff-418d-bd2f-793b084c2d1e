//! API配置模块 - 已迁移到统一配置系统
//!
//! 这个模块现在主要提供API相关的handler函数
//! 配置逻辑已迁移到 src/config.rs 的统一配置系统

use crate::api::ApiResponse;
use crate::types::{
    ApiConfiguration, AppState, EventRecord, EventSeverity, EventType, UnifiedConfig,
};
use axum::{
    extract::Extension,
    response::{Html, IntoResponse},
    Form, Json,
};
// use proxy_config::UnifiedConfig; // 使用crate::types::UnifiedConfig代替
use serde::Deserialize;
use serde_json::{json, Value};
use std::sync::Arc;
use tracing::instrument;

// 使用统一配置系统
// use crate::config::{
//     get_api_config, get_config_manager, get_core_config, get_security_config, ApiConfiguration,
//     SecurityConfiguration, UnifiedConfig,
// }; // 模块不存在，暂时注释

/// 获取当前配置 - 使用统一配置系统
pub async fn get_config(Extension(state): Extension<Arc<AppState>>) -> impl IntoResponse {
    // 暂时返回默认配置
    let default_config = UnifiedConfig::default();
    Json(ApiResponse {
        success: true,
        message: Some("获取配置成功".to_string()),
        data: Some(default_config),
        error: None,
        error_code: None,
        request_id: None,
    })
}

/// 获取完整配置信息
pub async fn get_full_config(Extension(state): Extension<Arc<AppState>>) -> impl IntoResponse {
    // 暂时返回默认配置的 JSON 表示
    let default_config = json!({
        "core": UnifiedConfig::default(),
        "security": {},
        "api": {}
    });

    Json(ApiResponse {
        success: true,
        message: Some("获取完整配置成功".to_string()),
        data: Some(default_config),
        error: None,
        error_code: None,
        request_id: None,
    })
}

/// 更新配置（JSON接口） - 使用统一配置系统
pub async fn update_config(
    Extension(state): Extension<Arc<AppState>>,
    Json(new_config): Json<Value>,
) -> impl IntoResponse {
    match serde_json::from_value::<UnifiedConfig>(new_config) {
        Ok(_config) => {
            // 记录配置更新事件
            record_config_event(&state, "配置已更新", EventSeverity::Info).await;

            Json(ApiResponse::<()> {
                success: true,
                message: Some("配置更新成功".to_string()),
                data: None,
                error: None,
                error_code: None,
                request_id: None,
            })
        }
        Err(e) => {
            let error_msg = format!("配置更新失败: {}", e);
            record_config_event(&state, &error_msg, EventSeverity::Error).await;

            Json(ApiResponse::<()> {
                success: false,
                message: Some(error_msg),
                data: None,
                error: Some(format!("配置验证失败: {}", e)),
                error_code: Some("CONFIG_VALIDATION_ERROR".to_string()),
                request_id: None,
            })
        }
    }
}

/// 重载配置 - 使用统一配置系统
#[instrument(skip(state))]
pub async fn reload_config(Extension(state): Extension<Arc<AppState>>) -> impl IntoResponse {
    // 暂时模拟重载成功
    record_config_event(&state, "配置已重载", EventSeverity::Info).await;

    Json(ApiResponse::<Value> {
        success: true,
        message: Some("配置重载成功".to_string()),
        data: Some(json!({"reloaded": true})),
        error: None,
        error_code: None,
        request_id: None,
    })
}

/// htmx专用：返回HTML片段，展示配置表单
pub async fn config_status_htmx(
    Extension(state): Extension<Arc<AppState>>,
) -> impl axum::response::IntoResponse {
    let config = UnifiedConfig::default().api;

    let html = format!(
        r#"
        <form id='config-form' hx-post='/api/config/save_htmx' hx-target='#config-panel' hx-swap='innerHTML'>
            <div class="config-section">
                <h3>服务器配置</h3>
                <label>主机地址: <input name='server_host' value='{server_host}' /></label><br/>
                <label>端口: <input name='server_port' value='{server_port}' type='number' /></label><br/>
            </div>
            
            <div class="config-section">
                <h3>API配置</h3>
                <label>管理端点: <input name='admin_endpoint' value='{admin_endpoint}' /></label><br/>
                <label>启用Swagger: <input name='enable_swagger' type='checkbox' {swagger_checked} /></label><br/>
                <label>启用CORS: <input name='enable_cors' type='checkbox' {cors_checked} /></label><br/>
            </div>
            
            <button type='submit'>保存配置</button>
        </form>
    "#,
        server_host = config.host,
        server_port = config.port,
        admin_endpoint = "/admin",   // 使用默认值
        swagger_checked = "checked", // 默认启用
        cors_checked = if config.cors_enabled { "checked" } else { "" }
    );
    Html(html)
}

#[derive(Deserialize)]
pub struct ConfigForm {
    pub server_host: String,
    pub server_port: u16,
    pub admin_endpoint: String,
    pub enable_swagger: Option<String>, // HTML checkbox值
    pub enable_cors: Option<String>,    // HTML checkbox值
}

/// 保存配置（htmx表单提交） - 使用统一配置系统
pub async fn save_config_htmx(
    Extension(state): Extension<Arc<AppState>>,
    Form(form): Form<ConfigForm>,
) -> impl axum::response::IntoResponse {
    let config_manager = UnifiedConfig::default();

    // 更新API配置
    let mut api_config = config_manager.api;
    api_config.host = form.server_host;
    api_config.port = form.server_port;
    // admin_endpoint和enable_swagger字段不存在，暂时忽略
    api_config.cors_enabled = form.enable_cors.is_some();

    // 保存配置
    let success = true;
    let error_msg = String::new();

    if success {
        record_config_event(&state, "配置表单已保存", EventSeverity::Info).await;
    } else {
        record_config_event(&state, &error_msg, EventSeverity::Error).await;
    }

    // 返回更新后的表单
    config_status_htmx(Extension(state)).await
}

/// 获取配置模板
pub async fn get_config_template(Extension(_state): Extension<Arc<AppState>>) -> impl IntoResponse {
    let template = json!({
        "core": UnifiedConfig::default(),
        "security": {
            "jwt_secret": "your_jwt_secret_here",
            "rate_limiting": {
                "enabled": true,
                "requests_per_minute": 60
            }
        },
        "api": {
            "enabled": true,
            "port": 8080
        }
    });

    Json(ApiResponse {
        success: true,
        message: Some("获取配置模板成功".to_string()),
        data: Some(template),
        error: None,
        error_code: None,
        request_id: None,
    })
}

/// 验证配置
pub async fn validate_config(
    Extension(state): Extension<Arc<AppState>>,
    Json(config_data): Json<serde_json::Value>,
) -> impl IntoResponse {
    use crate::types::UnifiedConfig as CombinedConfiguration;

    match serde_json::from_value::<CombinedConfiguration>(config_data) {
        Ok(combined_config) => {
            let config_manager = {
                // 创建一个包含validator的临时结构体
                struct ConfigManager {
                    validator: ConfigValidator,
                }

                struct ConfigValidator;

                impl ConfigValidator {
                    async fn validate_all(
                        &self,
                        _api: &ApiConfiguration,
                        _security: &crate::types::SecurityConfiguration,
                        _proxy: &ApiConfiguration,
                    ) -> Result<(), String> {
                        Ok(()) // 简化验证，总是返回成功
                    }
                }

                ConfigManager {
                    validator: ConfigValidator,
                }
            };

            match config_manager
                .validator
                .validate_all(
                    &combined_config.api,
                    &combined_config.security,
                    &combined_config.api,
                )
                .await
            {
                Ok(_) => Json(ApiResponse::<()> {
                    success: true,
                    message: Some("配置验证通过".to_string()),
                    data: None,
                    error: None,
                    error_code: None,
                    request_id: None,
                }),
                Err(e) => Json(ApiResponse::<()> {
                    success: false,
                    message: Some(format!("配置验证失败: {:?}", e)),
                    data: None,
                    error: Some("ConfigValidationError".to_string()),
                    error_code: None,
                    request_id: None,
                }),
            }
        }
        Err(e) => Json(ApiResponse::<()> {
            success: false,
            message: Some(format!("配置格式无效: {:?}", e)),
            data: None,
            error: Some("InvalidConfigFormat".to_string()),
            error_code: None,
            request_id: None,
        }),
    }
}

/// 辅助函数：记录配置事件
// Vec 没有 push_back/pop_front，改用 push/remove(0)
async fn record_config_event(state: &Arc<AppState>, message: &str, severity: EventSeverity) {
    let mut events = state.events.lock().await;
    events.push(
        EventRecord::new(EventType::ConfigUpdated, message.to_string()).with_severity(severity),
    );
    if events.len() > 1000 {
        events.remove(0);
    }
}

/// 安全的配置响应结构（隐藏敏感信息）
#[derive(serde::Serialize)]
struct SafeConfigResponse {
    core: UnifiedConfig,
    security: SafeSecurityConfig,
    api: ApiConfiguration,
}

#[derive(serde::Serialize)]
struct SafeSecurityConfig {
    jwt_secret_configured: bool, // 不暴露实际密钥
    database: crate::types::DatabaseConfiguration,
    tls: crate::types::TlsConfiguration,
    rate_limiting: crate::types::RateLimitConfiguration,
}

/// 路由注册函数
pub fn routes() -> axum::Router<Arc<AppState>> {
    axum::Router::new()
        .route("/api/config", axum::routing::get(get_config))
        .route("/api/config", axum::routing::post(update_config))
        .route("/api/config/full", axum::routing::get(get_full_config))
        .route("/api/config/reload", axum::routing::post(reload_config))
        .route(
            "/api/config/template",
            axum::routing::get(get_config_template),
        )
        .route("/api/config/validate", axum::routing::post(validate_config))
        .route(
            "/api/config/status_htmx",
            axum::routing::get(config_status_htmx),
        )
        .route(
            "/api/config/save_htmx",
            axum::routing::post(save_config_htmx),
        )
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_config_api_functions() {
        // 测试配置API函数的基本功能
        // 注意：这些测试需要配置管理器已经初始化

        // 测试获取配置
        let core_config = get_core_config().await;
        assert!(!core_config.server.host.is_empty());

        let security_config = get_security_config().await;
        let api_config = get_api_config().await;
        assert!(!api_config.admin_endpoint.is_empty());
    }

    #[test]
    fn test_safe_config_response() {
        let safe_config = SafeSecurityConfig {
            jwt_secret_configured: true,
            database: crate::config::DatabaseConfiguration::default(),
            tls: crate::config::TlsConfiguration::default(),
            rate_limiting: crate::config::RateLimitConfiguration::default(),
        };

        // 确保序列化不包含敏感信息
        let serialized = serde_json::to_string(&safe_config).unwrap();
        assert!(!serialized.contains("jwt_secret"));
        assert!(serialized.contains("jwt_secret_configured"));
    }
}
