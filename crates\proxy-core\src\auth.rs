use chrono::{DateTime, Utc};
use serde_json::{json, Value};
use std::borrow::Cow;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime};
// 统一使用 parking_lot::RwLock 避免锁竞争
use parking_lot::RwLock;
use tracing::{debug, error, info, warn};
use uuid::Uuid;

use async_trait::async_trait;
use serde::{Deserialize, Serialize};

/// 安全的日志记录器 - 内存优化版本
pub struct SecureLogger {
    max_message_length: usize,
    sensitive_patterns: Vec<regex::Regex>,
    request_id_generator: RequestIdGenerator,
}

impl SecureLogger {
    pub fn new() -> Self {
        let sensitive_patterns = vec![
            // 密码相关
            regex::Regex::new(r"(?i)(password|pwd|pass|secret|key|token)[:=]\s*[^\s]+").unwrap(),
            // 邮箱地址
            regex::Regex::new(r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}").unwrap(),
            // 信用卡号
            regex::Regex::new(r"\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b").unwrap(),
            // 手机号
            regex::Regex::new(r"\b1[3-9]\d{9}\b").unwrap(),
            // IP地址的部分遮蔽
            regex::Regex::new(r"\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b").unwrap(),
        ];

        Self {
            max_message_length: 2048,
            sensitive_patterns,
            request_id_generator: RequestIdGenerator::new(),
        }
    }

    /// 安全地记录请求日志 - 内存优化版本
    pub fn log_request(
        &self,
        method: &str,
        uri: &str,
        client_ip: &str,
        user_agent: Option<&str>,
        request_size: Option<usize>,
    ) -> String {
        let request_id = self.request_id_generator.generate();

        // 使用Cow减少不必要的字符串分配
        let safe_method = self.sanitize_log_input_cow(method, 10);
        let safe_uri = self.sanitize_url_cow(uri);
        let safe_ip = self.mask_ip_address_cow(client_ip);
        let safe_user_agent = user_agent
            .map(|ua| self.sanitize_log_input_cow(ua, 200))
            .unwrap_or_else(|| Cow::Borrowed("unknown"));

        // 使用结构化日志减少字符串格式化
        info!(
            request_id = %request_id,
            method = %safe_method,
            uri = %safe_uri,
            client_ip = %safe_ip,
            user_agent = %safe_user_agent,
            request_size = request_size,
            timestamp = %Utc::now().to_rfc3339(),
            "HTTP request received"
        );

        request_id
    }

    /// 安全地记录响应日志 - 内存优化版本
    pub fn log_response(
        &self,
        request_id: &str,
        status_code: u16,
        response_size: Option<usize>,
        duration_ms: u64,
        error_message: Option<&str>,
    ) {
        let safe_request_id = self.sanitize_log_input_cow(request_id, 36);
        let safe_error = error_message
            .map(|err| self.sanitize_log_input_cow(err, 500))
            .unwrap_or_else(|| Cow::Borrowed("none"));

        info!(
            request_id = %safe_request_id,
            status_code = status_code,
            response_size = response_size,
            duration_ms = duration_ms,
            error = %safe_error,
            timestamp = %Utc::now().to_rfc3339(),
            "HTTP response sent"
        );
    }

    /// 安全地记录安全事件 - 内存优化版本
    pub fn log_security_event(
        &self,
        event_type: &str,
        client_ip: &str,
        user_id: Option<&str>,
        description: &str,
        severity: SecuritySeverity,
    ) {
        let event_id = Uuid::new_v4().to_string();
        let safe_event_type = self.sanitize_log_input_cow(event_type, 50);
        let safe_ip = self.mask_ip_address_cow(client_ip);
        let safe_user_id = user_id
            .map(|id| self.sanitize_log_input_cow(id, 100))
            .unwrap_or_else(|| Cow::Borrowed("anonymous"));
        let safe_description = self.sanitize_log_input_cow(description, 1000);

        match severity {
            SecuritySeverity::Low => debug!(
                event_id = %event_id,
                event_type = %safe_event_type,
                client_ip = %safe_ip,
                user_id = %safe_user_id,
                description = %safe_description,
                severity = "low",
                timestamp = %Utc::now().to_rfc3339(),
                "Security event"
            ),
            SecuritySeverity::Medium => warn!(
                event_id = %event_id,
                event_type = %safe_event_type,
                client_ip = %safe_ip,
                user_id = %safe_user_id,
                description = %safe_description,
                severity = "medium",
                timestamp = %Utc::now().to_rfc3339(),
                "Security event"
            ),
            SecuritySeverity::High => error!(
                event_id = %event_id,
                event_type = %safe_event_type,
                client_ip = %safe_ip,
                user_id = %safe_user_id,
                description = %safe_description,
                severity = "high",
                timestamp = %Utc::now().to_rfc3339(),
                "Critical security event"
            ),
        }
    }

    /// 安全地记录缓存操作 - 内存优化版本
    pub fn log_cache_operation(
        &self,
        operation: &str,
        cache_key: &str,
        hit: bool,
        size: Option<usize>,
        ttl: Option<u64>,
    ) {
        let safe_operation = self.sanitize_log_input_cow(operation, 20);
        let safe_cache_key = self.sanitize_cache_key_cow(cache_key);

        debug!(
            operation = %safe_operation,
            cache_key = %safe_cache_key,
            hit = hit,
            size = size,
            ttl = ttl,
            timestamp = %Utc::now().to_rfc3339(),
            "Cache operation"
        );
    }

    /// 安全地记录错误信息 - 内存优化版本
    pub fn log_error(
        &self,
        request_id: Option<&str>,
        error_code: &str,
        error_message: &str,
        context: Option<&str>,
    ) {
        let safe_request_id = request_id
            .map(|id| self.sanitize_log_input_cow(id, 36))
            .unwrap_or_else(|| Cow::Borrowed("none"));
        let safe_error_code = self.sanitize_log_input_cow(error_code, 50);
        let safe_error_message = self.sanitize_log_input_cow(error_message, 500);
        let safe_context = context
            .map(|ctx| self.sanitize_log_input_cow(ctx, 200))
            .unwrap_or_else(|| Cow::Borrowed("none"));

        error!(
            request_id = %safe_request_id,
            error_code = %safe_error_code,
            error_message = %safe_error_message,
            context = %safe_context,
            timestamp = %Utc::now().to_rfc3339(),
            "Error occurred"
        );
    }

    /// 清理和验证日志输入 - 内存优化版本（返回Cow）
    fn sanitize_log_input_cow(&self, input: &str, max_length: usize) -> Cow<'_, str> {
        if input.is_empty() {
            return Cow::Borrowed("empty");
        }

        // 快速路径：如果输入已经安全且不需要截断，直接借用
        if input.len() <= max_length && self.is_input_safe(input) {
            return Cow::Borrowed(input);
        }

        // 需要处理的情况：截断或清理
        let truncated = if input.len() > max_length {
            &input[..max_length]
        } else {
            input
        };

        // 移除控制字符和潜在的注入字符
        let cleaned: String = truncated
            .chars()
            .filter(|&c| {
                // 保留可打印字符，但排除潜在危险字符
                c.is_ascii_graphic() || c == ' ' || c == '\t'
            })
            .collect();

        // 移除敏感信息并转义
        let masked = self.mask_sensitive_data(&cleaned);
        let escaped = self.escape_log_string(&masked);

        Cow::Owned(escaped)
    }

    /// 快速检查输入是否安全 - 避免不必要的处理
    fn is_input_safe(&self, input: &str) -> bool {
        // 检查是否包含控制字符或危险字符
        !input
            .chars()
            .any(|c| c.is_control() && c != '\t' && c != '\n')
            && !self.contains_sensitive_patterns(input)
    }

    /// 快速检查是否包含敏感模式
    fn contains_sensitive_patterns(&self, input: &str) -> bool {
        // 简单的字符串包含检查，比正则表达式快
        const SENSITIVE_KEYWORDS: &[&str] = &["password", "pwd", "secret", "token", "key", "@"];

        let lower = input.to_lowercase();
        SENSITIVE_KEYWORDS
            .iter()
            .any(|&keyword| lower.contains(keyword))
    }

    /// 遮蔽敏感数据 - 内存优化版本
    fn mask_sensitive_data(&self, input: &str) -> String {
        // 如果没有敏感模式，直接返回克隆（避免正则处理）
        if !self.contains_sensitive_patterns(input) {
            return input.to_string();
        }

        let mut result = input.to_string();

        for pattern in &self.sensitive_patterns {
            result = pattern
                .replace_all(&result, |caps: &regex::Captures| {
                    let matched = caps.get(0).unwrap().as_str();
                    if matched.len() > 4 {
                        format!("{}****", &matched[..4])
                    } else {
                        "****".to_string()
                    }
                })
                .to_string();
        }

        result
    }

    /// 遮蔽IP地址（保留前两段）- 内存优化版本
    fn mask_ip_address_cow(&self, ip: &str) -> Cow<'_, str> {
        if ip.is_empty() {
            return Cow::Borrowed("unknown");
        }

        // IPv4处理
        if let Some(dot_pos) = ip.find('.') {
            if let Some(second_dot) = ip[dot_pos + 1..].find('.') {
                let second_dot_abs = dot_pos + 1 + second_dot;
                if ip[second_dot_abs + 1..].contains('.') {
                    // 看起来是IPv4，进行遮蔽
                    let parts: Vec<&str> = ip.split('.').collect();
                    if parts.len() == 4 && parts.iter().all(|part| part.parse::<u8>().is_ok()) {
                        return Cow::Owned(format!("{}.{}.***.**", parts[0], parts[1]));
                    }
                }
            }
        }

        // IPv6或其他格式的简单处理
        if ip.len() > 8 {
            Cow::Owned(format!("{}****", &ip[..4]))
        } else {
            Cow::Borrowed("masked")
        }
    }

    /// 清理URL，移除敏感参数 - 内存优化版本
    fn sanitize_url_cow(&self, url: &str) -> Cow<'_, str> {
        if url.is_empty() {
            return Cow::Borrowed("empty");
        }

        // 限制URL长度
        let url_to_process = if url.len() > 500 { &url[..500] } else { url };

        // 快速检查是否包含敏感参数
        const SENSITIVE_PARAMS: &[&str] = &["password", "pwd", "token", "key", "secret", "auth"];

        let needs_cleaning = SENSITIVE_PARAMS.iter().any(|&param| {
            url_to_process.contains(&format!("{}=", param))
                || url_to_process.contains(&format!("{}%3D", param))
        });

        if !needs_cleaning {
            if url.len() <= 500 {
                return Cow::Borrowed(url);
            } else {
                return Cow::Owned(url_to_process.to_string());
            }
        }

        // 需要清理敏感参数
        let mut cleaned = url_to_process.to_string();

        for param in SENSITIVE_PARAMS {
            let patterns = [
                format!("{}=[^&]*", param),
                format!("{}%3D[^&]*", param), // URL编码的=
            ];

            for pattern in &patterns {
                if let Ok(regex) = regex::Regex::new(pattern) {
                    cleaned = regex
                        .replace_all(&cleaned, &format!("{}=***", param))
                        .to_string();
                }
            }
        }

        Cow::Owned(self.escape_log_string(&cleaned))
    }

    /// 清理缓存键 - 内存优化版本
    fn sanitize_cache_key_cow(&self, key: &str) -> Cow<'_, str> {
        if key.is_empty() {
            return Cow::Borrowed("empty");
        }

        let key_to_process = if key.len() > 100 { &key[..100] } else { key };

        // 快速检查是否需要遮蔽
        let needs_masking = key_to_process.contains('@')
            || key_to_process.contains("user")
            || key_to_process.contains("id");

        if !needs_masking {
            if key.len() <= 100 {
                return Cow::Borrowed(key);
            } else {
                return Cow::Owned(key_to_process.to_string());
            }
        }

        // 需要遮蔽
        let masked = format!(
            "{}***",
            &key_to_process[..std::cmp::min(8, key_to_process.len())]
        );
        Cow::Owned(self.escape_log_string(&masked))
    }

    /// 转义字符串以防止日志注入 - 内存优化版本
    fn escape_log_string(&self, input: &str) -> String {
        // 快速检查是否需要转义
        if !input
            .chars()
            .any(|c| matches!(c, '\n' | '\r' | '\t' | '\\' | '"'))
        {
            return input.to_string();
        }

        input
            .replace('\n', "\\n")
            .replace('\r', "\\r")
            .replace('\t', "\\t")
            .replace('\\', "\\\\")
            .replace('"', "\\\"")
    }

    /// 原有方法保持向后兼容
    fn sanitize_log_input(&self, input: &str, max_length: usize) -> String {
        self.sanitize_log_input_cow(input, max_length).into_owned()
    }

    fn mask_ip_address(&self, ip: &str) -> String {
        self.mask_ip_address_cow(ip).into_owned()
    }

    fn sanitize_url(&self, url: &str) -> String {
        self.sanitize_url_cow(url).into_owned()
    }

    fn sanitize_cache_key(&self, key: &str) -> String {
        self.sanitize_cache_key_cow(key).into_owned()
    }
}

/// 安全事件严重程度
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum SecuritySeverity {
    Low,
    Medium,
    High,
}

/// 请求ID生成器 - 内存和性能优化版本
pub struct RequestIdGenerator {
    counter: std::sync::atomic::AtomicU64,
    node_id: u32,
    start_time: u64,
}

impl RequestIdGenerator {
    pub fn new() -> Self {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        // 使用进程ID和主机名生成更唯一的节点ID
        let mut hasher = DefaultHasher::new();
        std::process::id().hash(&mut hasher);

        // 尝试获取主机名，失败则使用随机值
        if let Ok(hostname) = std::env::var("COMPUTERNAME").or_else(|_| std::env::var("HOSTNAME")) {
            hostname.hash(&mut hasher);
        } else {
            std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos()
                .hash(&mut hasher);
        }

        let node_id = (hasher.finish() & 0xFFFFFFFF) as u32;

        // 记录启动时间（秒）用于生成更短的ID
        let start_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        Self {
            counter: std::sync::atomic::AtomicU64::new(0),
            node_id,
            start_time,
        }
    }

    /// 生成高性能的请求ID - 优化版本
    pub fn generate(&self) -> String {
        let count = self
            .counter
            .fetch_add(1, std::sync::atomic::Ordering::Relaxed);

        // 使用相对时间戳减少ID长度
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        let relative_time = now.saturating_sub(self.start_time);

        // 生成更紧凑的ID: {节点ID的16进制前4位}-{相对时间}-{计数器}
        format!(
            "{:04x}-{:x}-{:x}",
            self.node_id & 0xFFFF,
            relative_time,
            count
        )
    }

    /// 生成UUID格式的请求ID（当需要更高唯一性时）
    pub fn generate_uuid(&self) -> String {
        uuid::Uuid::new_v4().to_string()
    }

    /// 获取当前计数器值（用于监控）
    pub fn current_count(&self) -> u64 {
        self.counter.load(std::sync::atomic::Ordering::Relaxed)
    }

    /// 重置计数器（用于测试或重启场景）
    pub fn reset_counter(&self) {
        self.counter.store(0, std::sync::atomic::Ordering::Relaxed);
    }
}

impl Default for SecureLogger {
    fn default() -> Self {
        Self::new()
    }
}

/// 全局日志记录器实例
static LOGGER: once_cell::sync::Lazy<SecureLogger> = once_cell::sync::Lazy::new(SecureLogger::new);

/// 便捷的日志记录函数
pub fn log_request(
    method: &str,
    uri: &str,
    client_ip: &str,
    user_agent: Option<&str>,
    request_size: Option<usize>,
) -> String {
    LOGGER.log_request(method, uri, client_ip, user_agent, request_size)
}

pub fn log_response(
    request_id: &str,
    status_code: u16,
    response_size: Option<usize>,
    duration_ms: u64,
    error_message: Option<&str>,
) {
    LOGGER.log_response(
        request_id,
        status_code,
        response_size,
        duration_ms,
        error_message,
    )
}

pub fn log_security_event(
    event_type: &str,
    client_ip: &str,
    user_id: Option<&str>,
    description: &str,
    severity: SecuritySeverity,
) {
    LOGGER.log_security_event(event_type, client_ip, user_id, description, severity)
}

pub fn log_cache_operation(
    operation: &str,
    cache_key: &str,
    hit: bool,
    size: Option<usize>,
    ttl: Option<u64>,
) {
    LOGGER.log_cache_operation(operation, cache_key, hit, size, ttl)
}

pub fn log_error(
    request_id: Option<&str>,
    error_code: &str,
    error_message: &str,
    context: Option<&str>,
) {
    LOGGER.log_error(request_id, error_code, error_message, context)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sanitize_log_input() {
        let logger = SecureLogger::new();

        // 正常输入
        assert_eq!(
            logger.sanitize_log_input("normal input", 100),
            "normal input"
        );

        // 包含敏感信息
        assert!(logger
            .sanitize_log_input("password=secret123", 100)
            .contains("****"));
        assert!(logger
            .sanitize_log_input("<EMAIL>", 100)
            .contains("****"));

        // 长度截断
        let long_input = "a".repeat(200);
        let result = logger.sanitize_log_input(&long_input, 50);
        assert!(result.len() <= 50);

        // 控制字符过滤
        let input_with_controls = "normal\ntext\rwith\x00control\tchars";
        let result = logger.sanitize_log_input(input_with_controls, 100);
        assert!(!result.contains('\x00'));
        assert!(result.contains("\\n"));
        assert!(result.contains("\\r"));
    }

    #[test]
    fn test_mask_ip_address() {
        let logger = SecureLogger::new();

        // IPv4地址
        assert_eq!(logger.mask_ip_address("*************"), "192.168.***.**");
        assert_eq!(logger.mask_ip_address("********"), "10.0.***.**");

        // 非IP地址
        assert_eq!(logger.mask_ip_address("localhost"), "loca****");
        assert_eq!(logger.mask_ip_address("short"), "masked");
        assert_eq!(logger.mask_ip_address(""), "unknown");
    }

    #[test]
    fn test_sanitize_url() {
        let logger = SecureLogger::new();

        // 正常URL
        assert_eq!(
            logger.sanitize_url("https://example.com/path"),
            "https://example.com/path"
        );

        // 包含敏感参数的URL
        let url_with_secret = "https://api.example.com/login?username=user&password=secret123";
        let result = logger.sanitize_url(url_with_secret);
        assert!(result.contains("password=***"));
        assert!(!result.contains("secret123"));

        // 长URL截断
        let long_url = format!("https://example.com/{}", "a".repeat(600));
        let result = logger.sanitize_url(&long_url);
        assert!(result.len() <= 500);
    }

    #[test]
    fn test_sanitize_cache_key() {
        let logger = SecureLogger::new();

        // 正常缓存键
        assert_eq!(logger.sanitize_cache_key("simple_key"), "simple_key");

        // 包含敏感信息的缓存键
        assert!(logger
            .sanitize_cache_key("user:<EMAIL>")
            .starts_with("user:123***"));
        assert!(logger
            .sanitize_cache_key("userid_12345_session")
            .starts_with("userid_1***"));

        // 长缓存键截断
        let long_key = "a".repeat(200);
        let result = logger.sanitize_cache_key(&long_key);
        assert!(result.len() <= 100);
    }

    #[test]
    fn test_escape_log_string() {
        let logger = SecureLogger::new();

        // 正常字符串
        assert_eq!(logger.escape_log_string("normal string"), "normal string");

        // 需要转义的字符
        assert_eq!(logger.escape_log_string("line1\nline2"), "line1\\nline2");
        assert_eq!(
            logger.escape_log_string("text\rwith\tspecial"),
            "text\\rwith\\tspecial"
        );
        assert_eq!(logger.escape_log_string("quote\"here"), "quote\\\"here");
        assert_eq!(logger.escape_log_string("back\\slash"), "back\\\\slash");
    }

    #[test]
    fn test_request_id_generator() {
        let generator = RequestIdGenerator::new();

        let id1 = generator.generate();
        let id2 = generator.generate();

        // 应该生成不同的ID
        assert_ne!(id1, id2);

        // 都应该包含进程ID前缀
        assert!(id1.contains('-'));
        assert!(id2.contains('-'));

        // 第二个ID的序号应该更大
        let parts1: Vec<&str> = id1.split('-').collect();
        let parts2: Vec<&str> = id2.split('-').collect();
        assert_eq!(parts1[0], parts2[0]); // 相同的前缀

        let num1 = u64::from_str_radix(parts1[1], 16).unwrap();
        let num2 = u64::from_str_radix(parts2[1], 16).unwrap();
        assert!(num2 > num1);
    }

    #[test]
    fn test_security_severity() {
        // 测试枚举比较
        assert!(SecuritySeverity::Low != SecuritySeverity::Medium);
        assert!(SecuritySeverity::Medium != SecuritySeverity::High);

        // 测试克隆和复制
        let severity = SecuritySeverity::High;
        let copied = severity;
        assert_eq!(severity, copied);
    }

    #[tokio::test]
    async fn test_concurrent_logging() {
        use tokio::task::JoinSet;

        let mut set = JoinSet::new();

        // 启动多个并发日志记录任务
        for i in 0..100 {
            set.spawn(async move {
                let request_id = log_request(
                    "GET",
                    &format!("/api/test/{}", i),
                    "*************",
                    Some("test-agent"),
                    Some(1024),
                );

                log_response(&request_id, 200, Some(2048), 50, None);

                log_security_event(
                    "login_attempt",
                    "*************",
                    Some(&format!("user{}", i)),
                    "Successful login",
                    SecuritySeverity::Low,
                );

                request_id
            });
        }

        let mut request_ids = Vec::new();
        while let Some(result) = set.join_next().await {
            request_ids.push(result.unwrap());
        }

        // 所有请求ID都应该是唯一的
        request_ids.sort();
        request_ids.dedup();
        assert_eq!(request_ids.len(), 100);
    }

    #[test]
    fn test_memory_efficiency() {
        let logger = SecureLogger::new();

        // 测试Cow的零分配优化
        let safe_input = "safe_string_123";
        let result = logger.sanitize_log_input_cow(safe_input, 100);
        match result {
            Cow::Borrowed(s) => assert_eq!(s, safe_input), // 应该是借用
            Cow::Owned(_) => panic!("Should be borrowed for safe input"),
        }

        // 测试需要处理的情况
        let unsafe_input = "password=secret";
        let result = logger.sanitize_log_input_cow(unsafe_input, 100);
        match result {
            Cow::Borrowed(_) => panic!("Should be owned for unsafe input"),
            Cow::Owned(s) => assert!(s.contains("****")), // 应该是拥有的
        }
    }
}
