use crate::security::SecurityError;
use axum::{
    extract::{Request, State},
    http::{HeaderMap, HeaderName, HeaderValue, StatusCode},
    middleware::Next,
    response::Response,
};
use base64::{engine::general_purpose, Engine as _};
use regex::Regex;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tracing::{error, warn};

/// HTTP头部安全配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HeaderSecurityConfig {
    /// 是否启用头部注入检测
    pub enable_header_injection_detection: bool,
    /// 是否启用CSRF保护
    pub enable_csrf_protection: bool,
    /// CSRF令牌名称
    pub csrf_token_name: String,
    /// 允许的头部字符集
    pub allowed_header_chars: String,
    /// 禁止的头部模式
    pub forbidden_header_patterns: Vec<String>,
    /// 安全头部设置
    pub security_headers: HashMap<String, String>,
    /// 是否移除危险头部
    pub remove_dangerous_headers: bool,
    /// 危险头部列表
    pub dangerous_headers: Vec<String>,
}

impl Default for HeaderSecurityConfig {
    fn default() -> Self {
        let mut security_headers = HashMap::new();
        security_headers.insert("X-Content-Type-Options".to_string(), "nosniff".to_string());
        security_headers.insert("X-Frame-Options".to_string(), "DENY".to_string());
        security_headers.insert("X-XSS-Protection".to_string(), "1; mode=block".to_string());
        security_headers.insert(
            "Strict-Transport-Security".to_string(),
            "max-age=31536000; includeSubDomains".to_string(),
        );
        security_headers.insert(
            "Referrer-Policy".to_string(),
            "strict-origin-when-cross-origin".to_string(),
        );
        security_headers.insert("Content-Security-Policy".to_string(), 
            "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'".to_string());

        Self {
            enable_header_injection_detection: true,
            enable_csrf_protection: true,
            csrf_token_name: "csrf_token".to_string(),
            allowed_header_chars:
                "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_.:;=+/ "
                    .to_string(),
            forbidden_header_patterns: vec![
                r"[\r\n]".to_string(),      // CRLF注入
                r"<script".to_string(),     // XSS
                r"javascript:".to_string(), // JavaScript URL
                r"data:".to_string(),       // Data URL
                r"vbscript:".to_string(),   // VBScript URL
            ],
            security_headers,
            remove_dangerous_headers: true,
            dangerous_headers: vec![
                "X-Forwarded-Host".to_string(),
                "X-Forwarded-Server".to_string(),
                "X-Forwarded-Proto".to_string(),
                "X-Original-URL".to_string(),
                "X-Rewrite-URL".to_string(),
            ],
        }
    }
}

/// 头部注入检测器
pub struct HeaderInjectionDetector {
    forbidden_patterns: Vec<Regex>,
    allowed_chars: Vec<char>,
}

impl HeaderInjectionDetector {
    pub fn new(config: &HeaderSecurityConfig) -> Result<Self, SecurityError> {
        let forbidden_patterns: Result<Vec<_>, _> = config
            .forbidden_header_patterns
            .iter()
            .map(|pattern| Regex::new(pattern))
            .collect();
        let forbidden_patterns =
            forbidden_patterns.map_err(|e| SecurityError::MaliciousRequest {
                reason: format!("正则表达式错误: {}", e),
            })?;
        Ok(Self {
            forbidden_patterns,
            allowed_chars: config.allowed_header_chars.chars().collect(),
        })
    }

    /// 检测头部值是否包含注入
    pub fn detect_injection(&self, value: &str) -> bool {
        // 检查是否包含非法字符
        for ch in value.chars() {
            if !self.allowed_chars.contains(&ch) {
                warn!("检测到非法字符: {}", ch);
                return true;
            }
        }

        // 检查是否匹配禁止模式
        for pattern in &self.forbidden_patterns {
            if pattern.is_match(value) {
                warn!("检测到禁止模式匹配: {}", pattern.as_str());
                return true;
            }
        }

        false
    }

    /// 检测头部名称是否安全
    pub fn is_safe_header_name(&self, name: &str) -> bool {
        // 头部名称只允许字母、数字和连字符
        name.chars().all(|c| c.is_ascii_alphanumeric() || c == '-')
    }
}

/// CSRF保护器
#[derive(Clone)]
pub struct CSRFProtector {
    config: HeaderSecurityConfig,
    secret_key: String,
}

impl CSRFProtector {
    pub fn new(config: HeaderSecurityConfig, secret_key: String) -> Self {
        Self { config, secret_key }
    }

    /// 生成CSRF令牌
    pub fn generate_token(&self, session_id: &str) -> String {
        #[cfg(feature = "security-extra")]
        {
            use hmac::{Hmac, Mac};
            use sha2::Sha256;

            type HmacSha256 = Hmac<Sha256>;

            let timestamp = chrono::Utc::now().timestamp();
            let data = format!("{}:{}", session_id, timestamp);

            let mut mac = match HmacSha256::new_from_slice(self.secret_key.as_bytes()) {
                Ok(mac) => mac,
                Err(_) => {
                    warn!("CSRF令牌生成失败: 无法创建HMAC");
                    return String::new(); // 返回空字符串作为错误标识
                }
            };
            mac.update(data.as_bytes());

            let result = mac.finalize();
            let token = format!("{}:{}", timestamp, hex::encode(result.into_bytes()));

            general_purpose::STANDARD.encode(token)
        }
        #[cfg(not(feature = "security-extra"))]
        {
            // 简化版本，不使用HMAC
            let timestamp = chrono::Utc::now().timestamp();
            let token = format!("{}:{}", session_id, timestamp);
            general_purpose::STANDARD.encode(token)
        }
    }

    /// 验证CSRF令牌
    pub fn verify_token(&self, token: &str, session_id: &str) -> bool {
        #[cfg(feature = "security-extra")]
        {
            use hmac::{Hmac, Mac};
            use sha2::Sha256;

            type HmacSha256 = Hmac<Sha256>;

            // 解码令牌
            let decoded = match general_purpose::STANDARD.decode(token) {
                Ok(d) => String::from_utf8_lossy(&d).into_owned(),
                Err(_) => return false,
            };

            // 解析时间戳和签名
            let parts: Vec<&str> = decoded.split(':').collect();
            if parts.len() != 2 {
                return false;
            }

            let timestamp: i64 = match parts[0].parse() {
                Ok(t) => t,
                Err(_) => return false,
            };

            let signature = parts[1];

            // 检查令牌是否过期（1小时）
            let current_time = chrono::Utc::now().timestamp();
            if current_time - timestamp > 3600 {
                warn!("CSRF令牌已过期");
                return false;
            }

            // 验证签名
            let data = format!("{}:{}", session_id, timestamp);
            let mut mac = match HmacSha256::new_from_slice(self.secret_key.as_bytes()) {
                Ok(mac) => mac,
                Err(_) => {
                    warn!("CSRF令牌验证失败: 无法创建HMAC");
                    return false;
                }
            };
            mac.update(data.as_bytes());

            let expected = hex::encode(mac.finalize().into_bytes());

            if expected != signature {
                warn!("CSRF令牌签名验证失败");
                return false;
            }

            true
        }
        #[cfg(not(feature = "security-extra"))]
        {
            // 简化版本验证
            let decoded = match general_purpose::STANDARD.decode(token) {
                Ok(d) => String::from_utf8_lossy(&d).into_owned(),
                Err(_) => return false,
            };

            // 简单检查格式
            let parts: Vec<&str> = decoded.split(':').collect();
            if parts.len() != 2 {
                return false;
            }

            let timestamp: i64 = match parts[0].parse() {
                Ok(t) => t,
                Err(_) => return false,
            };

            // 检查令牌是否过期（1小时）
            let current_time = chrono::Utc::now().timestamp();
            if current_time - timestamp > 3600 {
                return false;
            }

            // 简单验证session_id匹配
            parts[0] == session_id
        }
    }

    /// 从请求中提取CSRF令牌
    pub fn extract_token_from_request(&self, headers: &HeaderMap) -> Option<String> {
        // 首先检查头部
        if let Some(header_value) = headers.get(&self.config.csrf_token_name) {
            if let Ok(token) = header_value.to_str() {
                return Some(token.to_string());
            }
        }

        // 检查X-CSRF-Token头部
        if let Some(header_value) = headers.get("X-CSRF-Token") {
            if let Ok(token) = header_value.to_str() {
                return Some(token.to_string());
            }
        }

        None
    }

    /// 检查请求是否需要CSRF保护
    pub fn requires_csrf_protection(&self, method: &str, path: &str) -> bool {
        // GET、HEAD、OPTIONS请求通常不需要CSRF保护
        if matches!(method, "GET" | "HEAD" | "OPTIONS") {
            return false;
        }

        // API端点通常需要CSRF保护
        if path.starts_with("/api/") {
            return true;
        }

        // 表单提交需要CSRF保护
        if path.contains("/submit") || path.contains("/update") || path.contains("/delete") {
            return true;
        }

        false
    }
}

/// 头部安全中间件
pub async fn header_security_middleware(
    State(state): State<Arc<HeaderSecurityState>>,
    mut request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let headers = request.headers();
    let method = request.method().as_str();
    let path = request.uri().path();

    // 检测头部注入
    if state.config.enable_header_injection_detection
        && check_header_injection(headers, &state.detector).is_err()
    {
        error!("检测到头部注入攻击: {}", path);
        return Err(StatusCode::BAD_REQUEST);
    }

    // CSRF保护
    if state.config.enable_csrf_protection
        && state.csrf_protector.requires_csrf_protection(method, path)
        && verify_csrf_token(&state.csrf_protector, headers, &get_session_id(headers)).is_err()
    {
        error!("CSRF令牌验证失败: {}", path);
        return Err(StatusCode::FORBIDDEN);
    }

    // 移除危险头部
    if state.config.remove_dangerous_headers {
        remove_dangerous_headers(&mut request, &state.config.dangerous_headers);
    }

    // 处理请求
    let mut response = next.run(request).await;

    // 添加安全头部
    add_security_headers(&mut response, &state.config.security_headers);

    Ok(response)
}

/// 头部安全状态
#[derive(Clone)]
pub struct HeaderSecurityState {
    pub config: HeaderSecurityConfig,
    pub detector: Arc<HeaderInjectionDetector>,
    pub csrf_protector: CSRFProtector,
}

impl HeaderSecurityState {
    pub fn new(config: HeaderSecurityConfig, secret_key: String) -> Result<Self, SecurityError> {
        let detector = Arc::new(HeaderInjectionDetector::new(&config)?);
        let csrf_protector = CSRFProtector::new(config.clone(), secret_key);

        Ok(Self {
            config,
            detector,
            csrf_protector,
        })
    }
}

/// 检查头部注入
fn check_header_injection(
    headers: &HeaderMap,
    detector: &HeaderInjectionDetector,
) -> Result<(), ()> {
    for (name, value) in headers.iter() {
        // 检查头部名称
        if !detector.is_safe_header_name(name.as_str()) {
            warn!("检测到不安全的头部名称: {}", name);
            return Err(());
        }

        // 检查头部值
        if let Ok(value_str) = value.to_str() {
            if detector.detect_injection(value_str) {
                warn!("检测到头部注入: {} = {}", name, value_str);
                return Err(());
            }
        }
    }

    Ok(())
}

/// 验证CSRF令牌
fn verify_csrf_token(
    csrf_protector: &CSRFProtector,
    headers: &HeaderMap,
    session_id: &str,
) -> Result<(), ()> {
    let token = csrf_protector
        .extract_token_from_request(headers)
        .ok_or(())?;

    if !csrf_protector.verify_token(&token, session_id) {
        return Err(());
    }

    Ok(())
}

/// 获取会话ID
fn get_session_id(headers: &HeaderMap) -> String {
    // 从Cookie中提取会话ID
    if let Some(cookie_header) = headers.get("cookie") {
        if let Ok(cookie_str) = cookie_header.to_str() {
            for cookie in cookie_str.split(';') {
                let cookie = cookie.trim();
                if cookie.starts_with("session_id=") {
                    return cookie.split('=').nth(1).unwrap_or("").to_string();
                }
            }
        }
    }

    // 如果没有会话ID，使用默认值
    "anonymous".to_string()
}

/// 移除危险头部
fn remove_dangerous_headers(request: &mut Request, dangerous_headers: &[String]) {
    let headers = request.headers_mut();

    for dangerous_header in dangerous_headers {
        if let Ok(header_name) = HeaderName::from_bytes(dangerous_header.as_bytes()) {
            headers.remove(&header_name);
        }
    }
}

/// 添加安全头部
fn add_security_headers(response: &mut Response, security_headers: &HashMap<String, String>) {
    let headers = response.headers_mut();

    for (name, value) in security_headers {
        if let (Ok(header_name), Ok(header_value)) = (
            HeaderName::from_bytes(name.as_bytes()),
            HeaderValue::from_str(value),
        ) {
            headers.insert(header_name, header_value);
        }
    }
}

/// 安全头部预设
pub struct SecurityHeaderPresets;

impl SecurityHeaderPresets {
    /// 获取基础安全头部
    pub fn basic() -> HashMap<String, String> {
        let mut headers = HashMap::new();
        headers.insert("X-Content-Type-Options".to_string(), "nosniff".to_string());
        headers.insert("X-Frame-Options".to_string(), "DENY".to_string());
        headers.insert("X-XSS-Protection".to_string(), "1; mode=block".to_string());
        headers
    }

    /// 获取严格安全头部
    pub fn strict() -> HashMap<String, String> {
        let mut headers = Self::basic();
        headers.insert(
            "Strict-Transport-Security".to_string(),
            "max-age=31536000; includeSubDomains; preload".to_string(),
        );
        headers.insert(
            "Content-Security-Policy".to_string(),
            "default-src 'none'; script-src 'self'; style-src 'self'; img-src 'self'".to_string(),
        );
        headers.insert("Referrer-Policy".to_string(), "no-referrer".to_string());
        headers.insert(
            "Permissions-Policy".to_string(),
            "geolocation=(), microphone=(), camera=()".to_string(),
        );
        headers
    }

    /// 获取开发环境头部
    pub fn development() -> HashMap<String, String> {
        let mut headers = Self::basic();
        headers.insert(
            "Content-Security-Policy".to_string(),
            "default-src 'self' 'unsafe-inline' 'unsafe-eval'".to_string(),
        );
        headers
    }
}
