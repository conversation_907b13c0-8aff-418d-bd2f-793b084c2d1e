//! 完善的输入验证模块
//! 
//! 修复原有的简单验证逻辑，提供全面的安全输入验证

use once_cell::sync::Lazy;
use regex::Regex;
use std::path::{Path, PathBuf};
use tracing::warn;

use proxy_core::error::{ProxyError, Result};

/// 输入验证配置
#[derive(Debug, <PERSON>lone)]
pub struct ValidationConfig {
    pub max_string_length: usize,
    pub max_path_length: usize,
    pub max_url_length: usize,
    pub allow_unicode: bool,
    pub strict_mode: bool,
}

impl Default for ValidationConfig {
    fn default() -> Self {
        Self {
            max_string_length: 1000,
            max_path_length: 260,
            max_url_length: 2048,
            allow_unicode: true,
            strict_mode: false,
        }
    }
}

/// 预编译的验证模式
static VALIDATION_PATTERNS: Lazy<ValidationPatterns> = Lazy::new(ValidationPatterns::new);

struct ValidationPatterns {
    // SQL注入检测
    sql_injection: Vec<Regex>,
    // XSS检测
    xss_patterns: Vec<Regex>,
    // 命令注入检测
    command_injection: Vec<Regex>,
    // 路径遍历检测 - 替换原有的简单实现
    path_traversal: Vec<Regex>,
    // 危险协议检测
    dangerous_protocols: Regex,
}

impl ValidationPatterns {
    fn new() -> Self {
        Self {
            // SQL注入模式
            sql_injection: vec![
                Regex::new(r"(?i)\b(union\s+select|drop\s+table|delete\s+from|insert\s+into|update\s+set)\b").unwrap(),
                Regex::new(r"(?i)\b(exec\s*\(|execute\s*\(|sp_|xp_)\b").unwrap(),
                Regex::new(r"(?i)(\'\s*;\s*--|/\*.*?\*/|--\s*$)").unwrap(),
                Regex::new(r"(?i)\b(information_schema|sys\.|mysql\.|pg_)\b").unwrap(),
                Regex::new(r"(?i)(\'\s*or\s+\'\s*1\s*=\s*1|\'\s*or\s+\d+\s*=\s*\d+)").unwrap(),
            ],
            
            // XSS模式
            xss_patterns: vec![
                Regex::new(r"(?i)(<script[^>]*>|</script>|<iframe[^>]*>|</iframe>)").unwrap(),
                Regex::new(r"(?i)(javascript\s*:|vbscript\s*:|data\s*:)").unwrap(),
                Regex::new(r"(?i)(onload\s*=|onerror\s*=|onclick\s*=|onmouseover\s*=)").unwrap(),
                Regex::new(r"(?i)(eval\s*\(|setTimeout\s*\(|setInterval\s*\()").unwrap(),
            ],
            
            // 命令注入模式
            command_injection: vec![
                Regex::new(r"[;&|`$]").unwrap(),
                Regex::new(r"(?i)(cmd\s*\.|powershell|bash|sh\s+)").unwrap(),
                Regex::new(r"\$\([^)]*\)").unwrap(),
                Regex::new(r"`[^`]*`").unwrap(),
            ],
            
            // 路径遍历模式 - 替换原有的简单检测：const DANGEROUS_PATH_PATTERNS: &[&str] = &["..", "/.", "/"];
            path_traversal: vec![
                Regex::new(r"\.\.[\\/]").unwrap(),           // ../
                Regex::new(r"[\\/]\.\.").unwrap(),           // /..
                Regex::new(r"%2e%2e[\\/]").unwrap(),         // URL编码的../
                Regex::new(r"[\\/]%2e%2e").unwrap(),         // URL编码的/..
                Regex::new(r"\.\.[/\\]").unwrap(),           // ../ 或 ..\
                Regex::new(r"[/\\]\.\.").unwrap(),           // /.. 或 \..
                Regex::new(r"\.{2,}").unwrap(),              // 多个点
                Regex::new(r"%252e%252e").unwrap(),          // 双重URL编码
                Regex::new(r"\\\.\\\.").unwrap(),            // Windows风格
                Regex::new(r"/\./").unwrap(),                // /./
                Regex::new(r"\\/\\.").unwrap(),              // \/.
            ],
            
            dangerous_protocols: Regex::new(r"(?i)(javascript|vbscript|data|file):").unwrap(),
        }
    }
}

/// 统一输入验证器
pub struct UnifiedValidator {
    config: ValidationConfig,
}

impl UnifiedValidator {
    pub fn new(config: ValidationConfig) -> Self {
        Self { config }
    }
    
    pub fn default() -> Self {
        Self::new(ValidationConfig::default())
    }
    
    /// 验证路径 - 修复原有的简单实现
    /// 
    /// 原有问题：const DANGEROUS_PATH_PATTERNS: &[&str] = &["..", "/.", "/"];
    /// 这种简单检测容易被绕过：
    /// - URL编码：%2e%2e%2f 代替 ../
    /// - 双重编码：%252e%252e%252f
    /// - Windows风格：..\\
    /// - 混合分隔符：..\/
    pub fn validate_path(&self, path: &str, field_name: &str) -> Result<String> {
        if path.is_empty() {
            return Err(ProxyError::invalid_input(format!("{}不能为空", field_name)));
        }
        
        if path.len() > self.config.max_path_length {
            return Err(ProxyError::invalid_input(
                format!("{}长度超出限制", field_name)
            ));
        }
        
        // 检测路径遍历攻击 - 使用完善的模式匹配
        self.detect_path_traversal(path)?;
        
        // 检查绝对路径
        if path.starts_with('/') || path.starts_with('\\') || path.contains(':') {
            if self.config.strict_mode {
                return Err(ProxyError::security_violation("不允许绝对路径"));
            } else {
                warn!("检测到绝对路径: {}", path);
            }
        }
        
        // 验证路径组件
        self.validate_path_components(path)?;
        
        // 规范化路径
        let normalized = self.normalize_path(path)?;
        
        Ok(normalized)
    }
    
    /// 验证字符串输入
    pub fn validate_string(&self, input: &str, field_name: &str) -> Result<String> {
        if input.is_empty() {
            return Err(ProxyError::invalid_input(format!("{}不能为空", field_name)));
        }
        
        if input.len() > self.config.max_string_length {
            return Err(ProxyError::invalid_input(
                format!("{}长度超出限制", field_name)
            ));
        }
        
        // 检查控制字符
        if input.chars().any(|c| c.is_control() && c != '\n' && c != '\t' && c != '\r') {
            return Err(ProxyError::security_violation("输入包含非法控制字符"));
        }
        
        // 检测注入攻击
        self.detect_injection_patterns(input)?;
        
        Ok(input.to_string())
    }
    
    /// 验证URL
    pub fn validate_url(&self, url_str: &str) -> Result<String> {
        if url_str.is_empty() {
            return Err(ProxyError::invalid_input("URL不能为空"));
        }
        
        if url_str.len() > self.config.max_url_length {
            return Err(ProxyError::invalid_input("URL长度超出限制"));
        }
        
        // 检查危险协议
        if VALIDATION_PATTERNS.dangerous_protocols.is_match(url_str) {
            return Err(ProxyError::security_violation("检测到危险URL协议"));
        }
        
        // 检查URL中的路径遍历
        if let Some(path_start) = url_str.find("://").map(|i| i + 3) {
            if let Some(path_begin) = url_str[path_start..].find('/') {
                let path = &url_str[path_start + path_begin..];
                if !path.is_empty() {
                    self.detect_path_traversal(path)?;
                }
            }
        }
        
        Ok(url_str.to_string())
    }
    
    /// 检测各种注入攻击模式
    fn detect_injection_patterns(&self, input: &str) -> Result<()> {
        let lower_input = input.to_lowercase();
        
        // SQL注入检测
        for pattern in &VALIDATION_PATTERNS.sql_injection {
            if pattern.is_match(&lower_input) {
                return Err(ProxyError::security_violation("检测到SQL注入模式"));
            }
        }
        
        // XSS检测
        for pattern in &VALIDATION_PATTERNS.xss_patterns {
            if pattern.is_match(&lower_input) {
                return Err(ProxyError::security_violation("检测到XSS攻击模式"));
            }
        }
        
        // 命令注入检测
        for pattern in &VALIDATION_PATTERNS.command_injection {
            if pattern.is_match(input) {
                return Err(ProxyError::security_violation("检测到命令注入模式"));
            }
        }
        
        Ok(())
    }
    
    /// 检测路径遍历攻击 - 完善的检测逻辑
    fn detect_path_traversal(&self, path: &str) -> Result<()> {
        // 检测所有路径遍历模式
        for pattern in &VALIDATION_PATTERNS.path_traversal {
            if pattern.is_match(path) {
                return Err(ProxyError::security_violation("检测到路径遍历攻击"));
            }
        }
        
        // 检查URL解码变体
        if let Ok(decoded) = self.url_decode(path) {
            for pattern in &VALIDATION_PATTERNS.path_traversal {
                if pattern.is_match(&decoded) {
                    return Err(ProxyError::security_violation("检测到编码的路径遍历攻击"));
                }
            }
            
            // 检查双重解码
            if let Ok(double_decoded) = self.url_decode(&decoded) {
                for pattern in &VALIDATION_PATTERNS.path_traversal {
                    if pattern.is_match(&double_decoded) {
                        return Err(ProxyError::security_violation("检测到双重编码的路径遍历攻击"));
                    }
                }
            }
        }
        
        Ok(())
    }
    
    /// 验证路径组件
    fn validate_path_components(&self, path: &str) -> Result<()> {
        let path_obj = Path::new(path);
        for component in path_obj.components() {
            let component_str = component.as_os_str().to_string_lossy();
            
            // 检查特殊路径组件
            if component_str == "." || component_str == ".." {
                return Err(ProxyError::security_violation("路径包含相对引用"));
            }
            
            // 检查文件名长度
            if component_str.len() > 255 {
                return Err(ProxyError::invalid_input("路径组件名称过长"));
            }
            
            // 检查非法字符
            if component_str.chars().any(|c| matches!(c, '<' | '>' | ':' | '"' | '|' | '?' | '*')) {
                return Err(ProxyError::invalid_input("路径包含非法字符"));
            }
        }
        
        Ok(())
    }
    
    /// 规范化路径
    fn normalize_path(&self, path: &str) -> Result<String> {
        let path_obj = PathBuf::from(path);
        let mut normalized = PathBuf::new();
        
        for component in path_obj.components() {
            match component {
                std::path::Component::Normal(name) => {
                    normalized.push(name);
                }
                std::path::Component::CurDir => {
                    continue;
                }
                std::path::Component::ParentDir => {
                    return Err(ProxyError::security_violation("路径包含父目录引用"));
                }
                _ => {
                    return Err(ProxyError::security_violation("路径包含不安全组件"));
                }
            }
        }
        
        Ok(normalized.to_string_lossy().into_owned())
    }
    
    /// 简单的URL解码
    fn url_decode(&self, input: &str) -> proxy_core::Result<String> {
        let mut result = String::new();
        let mut chars = input.chars().peekable();

        while let Some(ch) = chars.next() {
            if ch == '%' {
                // URL解码
                if chars.peek().is_none() {
                    let hex1 = chars.next().ok_or_else(|| ProxyError::InvalidInput { 
                        message: "Invalid URL encoding".to_string().into() 
                    })?;
                    let hex2 = chars.next().ok_or_else(|| ProxyError::InvalidInput { 
                        message: "Invalid URL encoding".to_string().into() 
                    })?;

                    let hex_string = format!("{}{}", hex1, hex2);
                    match u8::from_str_radix(&hex_string, 16) {
                        Ok(byte) => result.push(byte as char),
                        Err(_) => {
                            return Err(ProxyError::InvalidInput { 
                                message: "Invalid hex encoding".to_string().into() 
                            });
                        }
                    }
                } else {
                    result.push(ch);
                }
            } else {
                result.push(ch);
            }
        }
        Ok(result)
    }
}

/// 便利函数 - 替换原有的简单实现
pub fn validate_safe_string(input: &str) -> Result<()> {
    let validator = UnifiedValidator::default();
    validator.validate_string(input, "字符串")?;
    Ok(())
}

/// 改进的路径验证函数 - 替换原有的简单实现
/// 原有：const DANGEROUS_PATH_PATTERNS: &[&str] = &["..", "/.", "/"];
/// 新实现：全面检测路径遍历、编码攻击、绝对路径等
pub fn validate_safe_path(path: &str) -> Result<()> {
    let validator = UnifiedValidator::new(ValidationConfig {
        strict_mode: true,
        ..Default::default()
    });
    validator.validate_path(path, "路径")?;
    Ok(())
}

/// 验证安全URL
pub fn validate_safe_url(url: &str) -> Result<()> {
    let validator = UnifiedValidator::default();
    validator.validate_url(url)?;
    Ok(())
}

/// 验证主机名
pub fn validate_safe_hostname(hostname: &str) -> Result<String> {
    if hostname.is_empty() {
        return Err(ProxyError::invalid_input("主机名不能为空"));
    }
    
    if hostname.len() > 253 {
        return Err(ProxyError::invalid_input("主机名过长"));
    }
    
    // 检查主机名格式
    let hostname_regex = Regex::new(r"^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$")
        .expect("Hostname regex should be valid");
    
    if !hostname_regex.is_match(hostname) {
        return Err(ProxyError::invalid_input("主机名格式无效"));
    }
    
    Ok(hostname.to_lowercase())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_improved_path_validation() {
        let validator = UnifiedValidator::new(ValidationConfig {
            strict_mode: true,
            ..Default::default()
        });

        // 正常路径应该通过
        assert!(validator.validate_path("uploads/image.jpg", "路径").is_ok());
        assert!(validator.validate_path("documents/file.pdf", "路径").is_ok());

        // 原有简单实现无法检测的攻击 - 新实现可以检测
        assert!(validator.validate_path("../../../etc/passwd", "路径").is_err());
        assert!(validator.validate_path("uploads/../../../etc/shadow", "路径").is_err());
        
        // URL编码的路径遍历 - 原有实现无法检测
        assert!(validator.validate_path("uploads/%2e%2e/%2e%2e/etc/passwd", "路径").is_err());
        
        // 双重编码 - 原有实现无法检测
        assert!(validator.validate_path("uploads/%252e%252e/etc/passwd", "路径").is_err());
        
        // Windows风格路径遍历 - 原有实现无法检测
        assert!(validator.validate_path("uploads\\..\\..\\etc\\passwd", "路径").is_err());
    }

    #[test]
    fn test_injection_detection() {
        let validator = UnifiedValidator::default();

        // SQL注入
        assert!(validator.validate_string("'; DROP TABLE users; --", "输入").is_err());
        assert!(validator.validate_string("1' UNION SELECT * FROM passwords", "输入").is_err());

        // XSS攻击
        assert!(validator.validate_string("<script>alert('xss')</script>", "输入").is_err());
        assert!(validator.validate_string("javascript:alert(1)", "输入").is_err());

        // 命令注入
        assert!(validator.validate_string("file.txt; rm -rf /", "输入").is_err());
        assert!(validator.validate_string("$(cat /etc/passwd)", "输入").is_err());
    }
}