#!/bin/bash
# SM代理系统 - 部署性能对比脚本
# 对比优化版本与传统版本的部署性能

set -euo pipefail

# 颜色输出
RED='\033[1;31m'
GREEN='\033[1;32m'
YELLOW='\033[1;33m'
BLUE='\033[1;34m'
CYAN='\033[1;36m'
WHITE='\033[1;37m'
PURPLE='\033[1;35m'
NC='\033[0m'

PROJECT_NAME="sm"

echo -e "${GREEN}📊 SM代理系统 - 部署性能对比${NC}"
echo -e "${BLUE}═══════════════════════════════════════${NC}"
echo -e "${CYAN}🔍 对比优化版本与传统版本的部署性能${NC}"
echo ""

# 检查是否有必要的文件
if [ ! -f "setup.sh" ] || [ ! -f "install.sh" ]; then
    echo -e "${RED}❌ 缺少必要的部署脚本${NC}"
    exit 1
fi

# 警告信息
echo -e "${YELLOW}⚠️  注意事项:${NC}"
echo -e "${WHITE}• 此脚本将执行两次完整部署用于性能对比${NC}"
echo -e "${WHITE}• 每次部署前会清理现有安装${NC}"
echo -e "${WHITE}• 建议在测试环境中运行${NC}"
echo -e "${WHITE}• 需要sudo权限和稳定的网络连接${NC}"
echo ""

read -p "是否继续性能对比测试？(y/N): " confirm
if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}👋 取消性能对比测试${NC}"
    exit 0
fi

# 清理函数
cleanup_deployment() {
    echo -e "${BLUE}🧹 清理现有部署...${NC}"
    
    # 停止服务
    sudo systemctl stop "$PROJECT_NAME" 2>/dev/null || true
    sudo systemctl disable "$PROJECT_NAME" 2>/dev/null || true
    
    # 清理文件
    sudo rm -rf "/opt/$PROJECT_NAME" "/usr/local/$PROJECT_NAME" 2>/dev/null || true
    sudo rm -f "/etc/systemd/system/$PROJECT_NAME.service" 2>/dev/null || true
    sudo rm -f "/tmp/sm_deploy_state" "/tmp/sm_install.log" 2>/dev/null || true
    
    # 清理编译缓存
    rm -rf target/ 2>/dev/null || true
    
    # 重新加载systemd
    sudo systemctl daemon-reload 2>/dev/null || true
    
    echo -e "${GREEN}✅ 清理完成${NC}"
}

# 测试函数
run_deployment_test() {
    local mode="$1"
    local description="$2"
    
    echo ""
    echo -e "${BLUE}🚀 开始测试: $description${NC}"
    echo -e "${CYAN}═══════════════════════════════════════${NC}"
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    # 根据模式设置环境变量
    if [ "$mode" = "legacy" ]; then
        export USE_LEGACY_DEPLOY=1
        echo -e "${YELLOW}🔄 使用传统部署模式${NC}"
    else
        unset USE_LEGACY_DEPLOY
        echo -e "${GREEN}⚡ 使用优化部署模式${NC}"
    fi
    
    # 执行部署
    echo -e "${BLUE}📋 执行部署...${NC}"
    if sudo ./setup.sh >/dev/null 2>&1; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        echo -e "${GREEN}✅ 部署成功${NC}"
        echo -e "${WHITE}⏱️  耗时: ${CYAN}${duration}秒${NC}"
        
        # 验证部署结果
        local verification_score=0
        
        # 检查服务状态
        if systemctl is-active "$PROJECT_NAME" >/dev/null 2>&1; then
            ((verification_score++))
        fi
        
        # 检查可执行文件
        if [ -x "/opt/$PROJECT_NAME/$PROJECT_NAME" ] || [ -x "target/release/$PROJECT_NAME" ]; then
            ((verification_score++))
        fi
        
        # 检查配置文件
        if [ -f "/opt/$PROJECT_NAME/config/config.yaml" ] || [ -f "config.simple.yaml" ]; then
            ((verification_score++))
        fi
        
        echo -e "${WHITE}✅ 验证得分: ${CYAN}${verification_score}/3${NC}"
        
        # 返回结果
        echo "$duration:$verification_score"
    else
        echo -e "${RED}❌ 部署失败${NC}"
        echo "FAILED:0"
    fi
}

# 主测试流程
echo -e "${BLUE}🔄 开始性能对比测试...${NC}"

# 测试1: 传统模式
cleanup_deployment
echo -e "${YELLOW}📊 测试 1/2: 传统部署模式${NC}"
legacy_result=$(run_deployment_test "legacy" "传统部署模式")

# 等待一段时间
sleep 5

# 测试2: 优化模式  
cleanup_deployment
echo -e "${YELLOW}📊 测试 2/2: 优化部署模式${NC}"
optimized_result=$(run_deployment_test "optimized" "优化部署模式")

# 分析结果
echo ""
echo -e "${WHITE}📊 性能对比结果${NC}"
echo -e "${CYAN}═══════════════════════════════════════${NC}"

# 解析结果
legacy_time=$(echo "$legacy_result" | cut -d: -f1)
legacy_score=$(echo "$legacy_result" | cut -d: -f2)
optimized_time=$(echo "$optimized_result" | cut -d: -f1)
optimized_score=$(echo "$optimized_result" | cut -d: -f2)

echo -e "${WHITE}传统模式:${NC}"
echo -e "${WHITE}  耗时: ${CYAN}${legacy_time}秒${NC}"
echo -e "${WHITE}  验证得分: ${CYAN}${legacy_score}/3${NC}"
echo ""
echo -e "${WHITE}优化模式:${NC}"
echo -e "${WHITE}  耗时: ${CYAN}${optimized_time}秒${NC}"
echo -e "${WHITE}  验证得分: ${CYAN}${optimized_score}/3${NC}"
echo ""

# 计算性能提升
if [ "$legacy_time" != "FAILED" ] && [ "$optimized_time" != "FAILED" ]; then
    if [ "$legacy_time" -gt 0 ] && [ "$optimized_time" -gt 0 ]; then
        local time_saved=$((legacy_time - optimized_time))
        local improvement_percent=$(( (time_saved * 100) / legacy_time ))
        
        echo -e "${WHITE}性能提升:${NC}"
        echo -e "${WHITE}  时间节省: ${GREEN}${time_saved}秒${NC}"
        echo -e "${WHITE}  提升幅度: ${GREEN}${improvement_percent}%${NC}"
        
        if [ $improvement_percent -gt 0 ]; then
            echo -e "${GREEN}🎉 优化版本性能更优！${NC}"
        elif [ $improvement_percent -eq 0 ]; then
            echo -e "${YELLOW}⚖️  两个版本性能相当${NC}"
        else
            echo -e "${RED}⚠️  优化版本性能略低${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  无法计算性能提升（时间数据异常）${NC}"
    fi
else
    echo -e "${RED}❌ 部分测试失败，无法进行性能对比${NC}"
fi

echo ""
echo -e "${BLUE}📋 测试建议:${NC}"
echo -e "${WHITE}• 优化模式适合生产环境部署${NC}"
echo -e "${WHITE}• 传统模式适合调试和故障排除${NC}"
echo -e "${WHITE}• 建议在不同硬件配置下多次测试${NC}"
echo ""
echo -e "${GREEN}🎯 性能对比测试完成！${NC}"
