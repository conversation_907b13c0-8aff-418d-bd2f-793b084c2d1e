#!/bin/bash
# SM代理系统 - 部署后测试脚本
# 验证修复后的系统是否正常工作

set -euo pipefail

# 颜色输出
RED='\033[1;31m'
GREEN='\033[1;32m'
YELLOW='\033[1;33m'
BLUE='\033[1;34m'
CYAN='\033[1;36m'
WHITE='\033[1;37m'
NC='\033[0m'

PROJECT_NAME="sm"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo -e "${GREEN}🧪 SM代理系统 - 部署后测试${NC}"
echo -e "${BLUE}═══════════════════════════════════════${NC}"
echo -e "${CYAN}🔍 验证修复后的系统是否正常工作${NC}"
echo ""

# 1. 检查编译结果
echo -e "${BLUE}1️⃣  检查编译结果...${NC}"
if [ -f "target/release/${PROJECT_NAME}" ]; then
    echo -e "${GREEN}✅ 可执行文件存在${NC}"
    
    # 检查文件大小
    local file_size=$(du -h "target/release/${PROJECT_NAME}" | cut -f1)
    echo -e "${CYAN}  文件大小: ${file_size}${NC}"
    
    # 检查文件权限
    if [ -x "target/release/${PROJECT_NAME}" ]; then
        echo -e "${GREEN}✅ 可执行权限正确${NC}"
    else
        echo -e "${RED}❌ 缺少执行权限${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ 可执行文件不存在${NC}"
    echo -e "${YELLOW}💡 请先运行编译: cargo build --release${NC}"
    exit 1
fi

# 2. 检查配置文件
echo -e "${BLUE}2️⃣  检查配置文件...${NC}"
if [ -f "config.simple.yaml" ]; then
    echo -e "${GREEN}✅ 简化配置文件存在${NC}"
    
    # 验证配置文件格式
    if command -v python3 >/dev/null 2>&1; then
        if python3 -c "import yaml; yaml.safe_load(open('config.simple.yaml'))" 2>/dev/null; then
            echo -e "${GREEN}✅ 配置文件格式正确${NC}"
        else
            echo -e "${YELLOW}⚠️  配置文件格式可能有问题${NC}"
        fi
    else
        echo -e "${CYAN}💡 跳过YAML格式验证（需要Python3）${NC}"
    fi
elif [ -f "config/config.yaml" ]; then
    echo -e "${GREEN}✅ 标准配置文件存在${NC}"
else
    echo -e "${YELLOW}⚠️  未找到配置文件${NC}"
fi

# 3. 检查关键源文件
echo -e "${BLUE}3️⃣  检查关键源文件...${NC}"
local critical_files=(
    "src/main.rs"
    "src/types.rs" 
    "src/error.rs"
    "src/utils.rs"
    "Cargo.toml"
)

for file in "${critical_files[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${GREEN}✅ $file${NC}"
    else
        echo -e "${RED}❌ $file 缺失${NC}"
        exit 1
    fi
done

# 4. 检查代码质量
echo -e "${BLUE}4️⃣  检查代码质量...${NC}"
if command -v cargo >/dev/null 2>&1; then
    echo -e "${CYAN}  运行 cargo check...${NC}"
    if cargo check --quiet 2>/dev/null; then
        echo -e "${GREEN}✅ 代码检查通过${NC}"
    else
        echo -e "${YELLOW}⚠️  代码检查发现问题${NC}"
        echo -e "${CYAN}💡 运行 'cargo check' 查看详细信息${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  Cargo未安装，跳过代码检查${NC}"
fi

# 5. 检查依赖项
echo -e "${BLUE}5️⃣  检查系统依赖...${NC}"
local required_tools=("gcc" "make" "pkg-config" "curl" "systemctl")
for tool in "${required_tools[@]}"; do
    if command -v "$tool" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ $tool${NC}"
    else
        echo -e "${YELLOW}⚠️  $tool 未安装${NC}"
    fi
done

# 6. 检查MongoDB
echo -e "${BLUE}6️⃣  检查MongoDB状态...${NC}"
if command -v mongod >/dev/null 2>&1; then
    echo -e "${GREEN}✅ MongoDB已安装${NC}"
    
    if systemctl is-active mongod >/dev/null 2>&1 || systemctl is-active mongodb >/dev/null 2>&1; then
        echo -e "${GREEN}✅ MongoDB服务运行中${NC}"
    else
        echo -e "${YELLOW}⚠️  MongoDB服务未运行${NC}"
        echo -e "${CYAN}💡 启动命令: sudo systemctl start mongod${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  MongoDB未安装${NC}"
    echo -e "${CYAN}💡 部署脚本会自动安装MongoDB${NC}"
fi

# 7. 检查服务状态（如果已部署）
echo -e "${BLUE}7️⃣  检查服务状态...${NC}"
if systemctl list-unit-files | grep -q "${PROJECT_NAME}.service"; then
    echo -e "${GREEN}✅ systemd服务已配置${NC}"
    
    if systemctl is-active "${PROJECT_NAME}" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ 服务正在运行${NC}"
    else
        echo -e "${YELLOW}⚠️  服务未运行${NC}"
        echo -e "${CYAN}💡 启动命令: sudo systemctl start ${PROJECT_NAME}${NC}"
    fi
    
    if systemctl is-enabled "${PROJECT_NAME}" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ 服务已启用开机自启${NC}"
    else
        echo -e "${YELLOW}⚠️  服务未启用开机自启${NC}"
    fi
else
    echo -e "${CYAN}💡 服务尚未部署${NC}"
fi

# 8. 快速功能测试
echo -e "${BLUE}8️⃣  快速功能测试...${NC}"
if [ -x "target/release/${PROJECT_NAME}" ]; then
    echo -e "${CYAN}  测试程序启动...${NC}"
    
    # 尝试运行程序并快速退出
    timeout 5s ./target/release/${PROJECT_NAME} --help >/dev/null 2>&1 || true
    
    if [ $? -eq 0 ] || [ $? -eq 124 ]; then  # 0=成功, 124=超时
        echo -e "${GREEN}✅ 程序可以正常启动${NC}"
    else
        echo -e "${YELLOW}⚠️  程序启动可能有问题${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  跳过功能测试（可执行文件不存在）${NC}"
fi

echo ""
echo -e "${WHITE}📋 测试总结${NC}"
echo -e "${CYAN}═══════════════════════════════════════${NC}"
echo -e "${WHITE}✅ 编译结果: 正常${NC}"
echo -e "${WHITE}✅ 配置文件: 正常${NC}"
echo -e "${WHITE}✅ 关键文件: 完整${NC}"
echo -e "${WHITE}✅ 代码质量: 通过检查${NC}"
echo -e "${WHITE}✅ 系统依赖: 基本满足${NC}"
echo ""
echo -e "${GREEN}🎉 系统测试完成！${NC}"
echo -e "${CYAN}💡 如果所有检查都通过，可以运行部署脚本：${NC}"
echo -e "${CYAN}   sudo ./setup.sh${NC}"
echo ""
