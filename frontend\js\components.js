/**
 * SM镜像系统组件JavaScript文件
 * 处理模态框、表单、通知等UI组件
 */

class ComponentManager {
    constructor() {
        this.modals = new Map();
        this.notifications = [];

        this.init();
    }

    init() {
        this.setupModalTriggers();
        this.setupFormHandlers();
        this.setupNotificationContainer();
    }

    /**
     * 设置模态框触发器
     */
    setupModalTriggers() {
        console.log('🔗 设置模态框触发器');
        document.addEventListener('click', (event) => {
            const trigger = event.target.closest('[data-modal]');
            if (trigger) {
                event.preventDefault();
                const modalId = trigger.getAttribute('data-modal');
                console.log(`🖱️ 点击模态框触发器: ${modalId}`);
                this.showModal(modalId);
            }
        });
    }

    /**
     * 显示模态框
     */
    showModal(modalId) {
        console.log(`🔧 显示模态框: ${modalId}`);
        const modalContent = this.getModalContent(modalId);
        if (!modalContent) {
            console.error(`❌ 未找到模态框内容: ${modalId}`);
            return;
        }

        const modal = this.createModal(modalId, modalContent);
        this.modals.set(modalId, modal);

        document.body.appendChild(modal);

        // 添加动画
        requestAnimationFrame(() => {
            modal.classList.add('show');
            console.log(`✅ 模态框已显示: ${modalId}`);
        });

        // 绑定关闭事件
        this.bindModalEvents(modal, modalId);
    }

    /**
     * 获取模态框内容
     */
    getModalContent(modalId) {
        const contents = {
            'add-domain-modal': {
                title: '添加域名',
                body: `
                    <form id="add-domain-form" hx-post="/api/domains">
                        <input type="hidden" name="auth_token" value="disabled_auth_token">
                        <div class="config-field">
                            <label for="domain">域名</label>
                            <input type="text" id="domain" name="domain" class="form-input"
                                   placeholder="example.com" required>
                            <div class="help-text">请输入要添加的域名，不包含协议</div>
                        </div>
                        
                        <div class="config-field">
                            <label>域名类型</label>
                            <div class="radio-group">
                                <label class="radio-option">
                                    <input type="radio" name="type" value="upstream" required>
                                    <span class="radio-button"></span>
                                    <span class="radio-label">
                                        <span class="radio-title">🔼 上游域名</span>
                                        <span class="radio-desc">作为源站，提供内容</span>
                                    </span>
                                </label>
                                <label class="radio-option">
                                    <input type="radio" name="type" value="downstream" required>
                                    <span class="radio-button"></span>
                                    <span class="radio-label">
                                        <span class="radio-title">🔽 下游域名</span>
                                        <span class="radio-desc">作为镜像站，接收流量</span>
                                    </span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="config-field">
                            <label for="domain-description">描述</label>
                            <textarea id="domain-description" name="description" 
                                    class="form-textarea" placeholder="域名用途描述（可选）"></textarea>
                        </div>
                    </form>
                `,
                footer: `
                    <button type="button" class="btn btn-secondary" data-modal-close>取消</button>
                    <button type="submit" form="add-domain-form" class="btn btn-primary">添加域名</button>
                `
            },

            'start-task-modal': {
                title: '启动镜像任务',
                body: `
                    <form id="start-task-form" hx-post="/api/tasks">
                        <input type="hidden" name="auth_token" value="disabled_auth_token">
                        <div class="config-field">
                            <label for="task-name">任务名称</label>
                            <input type="text" id="task-name" name="name" class="form-input"
                                   placeholder="镜像任务_001" required>
                        </div>
                        
                        <div class="config-field">
                            <label for="max-depth">最大深度</label>
                            <input type="number" id="max-depth" name="max_depth" class="form-input" 
                                   value="3" min="1" max="10" required>
                            <div class="help-text">递归镜像的最大深度（1-10）</div>
                        </div>
                        
                        <div class="config-field">
                            <label for="concurrent-tasks">并发任务数</label>
                            <input type="number" id="concurrent-tasks" name="concurrent_tasks" 
                                   class="form-input" value="5" min="1" max="50" required>
                            <div class="help-text">同时运行的任务数量（1-50）</div>
                        </div>
                        
                        <div class="config-field">
                            <label>
                                <input type="checkbox" name="enable_cache" checked> 启用缓存
                            </label>
                        </div>
                        
                        <div class="config-field">
                            <label>
                                <input type="checkbox" name="follow_redirects" checked> 跟随重定向
                            </label>
                        </div>
                    </form>
                `,
                footer: `
                    <button type="button" class="btn btn-secondary" data-modal-close>取消</button>
                    <button type="submit" form="start-task-form" class="btn btn-success">启动任务</button>
                `
            },

            'add-blacklist-modal': {
                title: '添加黑名单规则',
                body: `
                    <form id="add-blacklist-form" hx-post="/api/blacklist">
                        <input type="hidden" name="auth_token" value="disabled_auth_token">
                        <div class="config-field">
                            <label for="blacklist-type">规则类型</label>
                            <select id="blacklist-type" name="type" class="form-select" required>
                                <option value="">请选择类型</option>
                                <option value="keyword">关键词</option>
                                <option value="domain">域名</option>
                                <option value="regex">正则表达式</option>
                            </select>
                        </div>

                        <div class="config-field">
                            <label for="blacklist-value">规则内容</label>
                            <input type="text" id="blacklist-value" name="value" class="form-input"
                                   placeholder="输入要屏蔽的内容" required>
                            <div class="help-text">根据类型输入相应的规则内容</div>
                        </div>

                        <div class="config-field">
                            <label for="blacklist-reason">屏蔽原因</label>
                            <input type="text" id="blacklist-reason" name="reason" class="form-input"
                                   placeholder="屏蔽原因（可选）">
                        </div>

                        <div class="config-field">
                            <label>
                                <input type="checkbox" name="case_sensitive"> 区分大小写
                            </label>
                        </div>
                    </form>
                `,
                footer: `
                    <button type="button" class="btn btn-secondary" data-modal-close>取消</button>
                    <button type="submit" form="add-blacklist-form" class="btn btn-primary">添加规则</button>
                `
            },

            'batch-add-modal': {
                title: '批量添加域名',
                body: `
                    <form id="batch-add-form" hx-post="/api/domain-pool/batch-add">
                        <input type="hidden" name="auth_token" value="disabled_auth_token">
                        <div class="config-field">
                            <label for="batch-domains">域名列表</label>
                            <textarea id="batch-domains" name="domains" class="form-textarea"
                                    placeholder="每行一个域名，例如：&#10;example1.com&#10;example2.com&#10;example3.com"
                                    rows="10" required></textarea>
                            <div class="help-text">每行输入一个域名，支持批量导入</div>
                        </div>

                        <div class="config-field">
                            <label>域名类型</label>
                            <div class="radio-group">
                                <label class="radio-option">
                                    <input type="radio" name="type" value="upstream" required>
                                    <span class="radio-button"></span>
                                    <span class="radio-label">
                                        <span class="radio-title">🔼 上游域名</span>
                                        <span class="radio-desc">批量添加源站域名</span>
                                    </span>
                                </label>
                                <label class="radio-option">
                                    <input type="radio" name="type" value="downstream" required>
                                    <span class="radio-button"></span>
                                    <span class="radio-label">
                                        <span class="radio-title">🔽 下游域名</span>
                                        <span class="radio-desc">批量添加镜像域名</span>
                                    </span>
                                </label>
                            </div>
                        </div>

                        <div class="config-field">
                            <label>
                                <input type="checkbox" name="auto_pairing" checked> 自动配对
                            </label>
                            <div class="help-text">添加后自动进行域名配对</div>
                        </div>
                    </form>
                `,
                footer: `
                    <button type="button" class="btn btn-secondary" data-modal-close>取消</button>
                    <button type="submit" form="batch-add-form" class="btn btn-success">批量添加</button>
                `
            },

            'import-domains-modal': {
                title: '导入域名文件',
                body: `
                    <form id="import-domains-form" hx-post="/api/domains/import" hx-encoding="multipart/form-data">
                        <input type="hidden" name="auth_token" value="disabled_auth_token">
                        <div class="config-field">
                            <label for="domain-file">选择文件</label>
                            <input type="file" id="domain-file" name="file" class="form-input"
                                   accept=".txt,.csv,.json" required>
                            <div class="help-text">支持 TXT、CSV、JSON 格式文件</div>
                        </div>

                        <div class="config-field">
                            <label for="file-format">文件格式</label>
                            <select id="file-format" name="format" class="form-select" required>
                                <option value="">请选择格式</option>
                                <option value="txt">纯文本 (每行一个域名)</option>
                                <option value="csv">CSV (域名,类型,描述)</option>
                                <option value="json">JSON 格式</option>
                            </select>
                        </div>

                        <div class="config-field">
                            <label>
                                <input type="checkbox" name="validate_domains" checked> 验证域名格式
                            </label>
                        </div>

                        <div class="config-field">
                            <label>
                                <input type="checkbox" name="skip_duplicates" checked> 跳过重复域名
                            </label>
                        </div>
                    </form>
                `,
                footer: `
                    <button type="button" class="btn btn-secondary" data-modal-close>取消</button>
                    <button type="submit" form="import-domains-form" class="btn btn-primary">导入域名</button>
                `
            }
        };

        return contents[modalId];
    }

    /**
     * 创建模态框元素
     */
    createModal(modalId, content) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.setAttribute('data-modal-id', modalId);

        modal.innerHTML = `
            <div class="modal-container">
                <div class="modal-header">
                    <h3 class="modal-title">${content.title}</h3>
                    <button type="button" class="modal-close" data-modal-close>&times;</button>
                </div>
                <div class="modal-body">
                    ${content.body}
                </div>
                <div class="modal-footer">
                    ${content.footer}
                </div>
            </div>
        `;

        return modal;
    }

    /**
     * 绑定模态框事件
     */
    bindModalEvents(modal, modalId) {
        // 点击遮罩层关闭
        modal.addEventListener('click', (event) => {
            if (event.target === modal) {
                this.closeModal(modalId);
            }
        });

        // 点击关闭按钮
        modal.querySelectorAll('[data-modal-close]').forEach(button => {
            button.addEventListener('click', () => {
                this.closeModal(modalId);
            });
        });

        // ESC键关闭
        const escHandler = (event) => {
            if (event.key === 'Escape') {
                this.closeModal(modalId);
                document.removeEventListener('keydown', escHandler);
            }
        };
        document.addEventListener('keydown', escHandler);

        // 表单提交处理
        const form = modal.querySelector('form');
        if (form) {
            form.addEventListener('htmx:afterRequest', (event) => {
                if (event.detail.successful) {
                    this.closeModal(modalId);
                    this.showNotification('操作成功！', 'success');

                    // 刷新相关数据
                    this.refreshRelatedData(modalId);
                } else {
                    this.showNotification('操作失败，请重试', 'error');
                }
            });
        }
    }

    /**
     * 关闭模态框
     */
    closeModal(modalId) {
        const modal = this.modals.get(modalId);
        if (modal) {
            modal.classList.add('closing');
            setTimeout(() => {
                if (modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
                this.modals.delete(modalId);
            }, 300);
        }
    }

    /**
     * 设置表单处理器
     */
    setupFormHandlers() {
        // 配置保存
        document.addEventListener('click', (event) => {
            if (event.target.id === 'save-config') {
                this.saveConfig();
            }
        });
    }

    /**
     * 保存配置
     */
    saveConfig() {
        const configForm = document.querySelector('.config-form form');
        if (configForm) {
            const formData = new FormData(configForm);
            const config = Object.fromEntries(formData.entries());

            // 使用HTMX发送配置
            htmx.ajax('POST', '/api/config', {
                values: config,
                headers: {
                    'Authorization': `Bearer ${window.authManager?.getToken() || ''}`
                }
            }).then(() => {
                this.showNotification('配置保存成功！', 'success');
            }).catch(() => {
                this.showNotification('配置保存失败', 'error');
            });
        }
    }

    /**
     * 设置通知容器
     */
    setupNotificationContainer() {
        if (!document.getElementById('notification-container')) {
            const container = document.createElement('div');
            container.id = 'notification-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1100;
                pointer-events: none;
            `;
            document.body.appendChild(container);
        }
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notification-container');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.pointerEvents = 'auto';

        const icon = this.getNotificationIcon(type);
        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span style="font-size: 16px;">${icon}</span>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" 
                        style="background: none; border: none; font-size: 18px; cursor: pointer; margin-left: auto;">×</button>
            </div>
        `;

        container.appendChild(notification);
        this.notifications.push(notification);

        // 自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
                this.notifications = this.notifications.filter(n => n !== notification);
            }
        }, duration);
    }

    /**
     * 获取通知图标
     */
    getNotificationIcon(type) {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        return icons[type] || icons.info;
    }

    /**
     * 刷新相关数据
     */
    refreshRelatedData(modalId) {
        switch (modalId) {
            case 'add-domain-modal':
                // 刷新域名列表
                htmx.trigger(document.querySelector('#domains .table-container'), 'refresh');
                break;
            case 'start-task-modal':
                // 刷新任务列表
                htmx.trigger(document.querySelector('#task-list'), 'refresh');
                break;
            case 'add-blacklist-modal':
                // 刷新黑名单
                htmx.trigger(document.querySelector('.blacklist-content'), 'refresh');
                break;
        }
    }

    /**
     * 清除所有通知
     */
    clearNotifications() {
        this.notifications.forEach(notification => {
            if (notification.parentNode) {
                notification.remove();
            }
        });
        this.notifications = [];
    }

    /**
     * 关闭所有模态框
     */
    closeAllModals() {
        this.modals.forEach((modal, modalId) => {
            this.closeModal(modalId);
        });
    }
}

/**
 * 现代化模态框管理器
 * 提供美观、居中的弹窗体验
 */
class ModalManager {
    constructor() {
        this.activeModals = new Set();
        this.init();
    }

    init() {
        // 监听ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeTopModal();
            }
        });

        // 点击遮罩层关闭模态框
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeModal(e.target);
            }
        });
    }

    // 创建模态框
    createModal(options = {}) {
        const {
            title = '提示',
            content = '',
            size = '', // 'sm', 'lg', 'xl'
            type = '', // 'confirm-dialog', 'danger', 'warning', 'info'
            showCloseButton = true,
            buttons = [],
            onShow = null,
            onHide = null,
            backdrop = true, // 是否显示背景遮罩
            keyboard = true, // 是否允许ESC键关闭
            focus = true // 是否自动聚焦
        } = options;

        // 创建模态框HTML
        const modalId = `modal-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const modalHTML = `
            <div class="modal-overlay ${size ? `modal-${size}` : ''} ${type}" id="${modalId}">
                <div class="modal-container">
                    <div class="modal-header">
                        <h3 class="modal-title">${title}</h3>
                        ${showCloseButton ? '<button class="modal-close" type="button">&times;</button>' : ''}
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                    ${buttons.length > 0 ? `
                        <div class="modal-footer">
                            ${buttons.map(btn => `
                                <button class="btn ${btn.class || 'btn-secondary'}" 
                                        data-action="${btn.action || 'close'}"
                                        ${btn.disabled ? 'disabled' : ''}>
                                    ${btn.text}
                                </button>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        // 添加到DOM
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modalElement = document.getElementById(modalId);

        // 绑定事件
        this.bindModalEvents(modalElement, {
            onShow,
            onHide,
            backdrop,
            keyboard,
            focus
        });

        return modalElement;
    }

    // 绑定模态框事件
    bindModalEvents(modalElement, options) {
        const { onShow, onHide, backdrop, keyboard, focus } = options;

        // 关闭按钮事件
        const closeBtn = modalElement.querySelector('.modal-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.closeModal(modalElement);
            });
        }

        // 底部按钮事件
        const footerButtons = modalElement.querySelectorAll('.modal-footer .btn');
        footerButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = btn.dataset.action;
                if (action === 'close') {
                    this.closeModal(modalElement);
                } else {
                    // 触发自定义事件
                    modalElement.dispatchEvent(new CustomEvent('modal:action', {
                        detail: { action, button: btn }
                    }));
                }
            });
        });

        // 背景点击关闭
        if (backdrop) {
            modalElement.addEventListener('click', (e) => {
                if (e.target === modalElement) {
                    this.closeModal(modalElement);
                }
            });
        }

        // 存储选项
        modalElement._modalOptions = options;
    }

    // 显示模态框
    showModal(modalElement) {
        if (!modalElement) return;

        this.activeModals.add(modalElement);
        document.body.style.overflow = 'hidden';

        // 触发显示动画
        requestAnimationFrame(() => {
            modalElement.classList.add('show');
        });

        // 自动聚焦
        if (modalElement._modalOptions?.focus) {
            const focusElement = modalElement.querySelector('input, textarea, select, button');
            if (focusElement) {
                setTimeout(() => focusElement.focus(), 100);
            }
        }

        // 触发显示回调
        if (modalElement._modalOptions?.onShow) {
            modalElement._modalOptions.onShow(modalElement);
        }

        // 触发自定义事件
        modalElement.dispatchEvent(new CustomEvent('modal:show'));
    }

    // 关闭模态框
    closeModal(modalElement) {
        if (!modalElement) return;

        modalElement.classList.remove('show');
        this.activeModals.delete(modalElement);

        // 如果没有其他模态框，恢复body滚动
        if (this.activeModals.size === 0) {
            document.body.style.overflow = '';
        }

        // 触发隐藏回调
        if (modalElement._modalOptions?.onHide) {
            modalElement._modalOptions.onHide(modalElement);
        }

        // 触发自定义事件
        modalElement.dispatchEvent(new CustomEvent('modal:hide'));

        // 动画结束后移除DOM
        setTimeout(() => {
            if (modalElement.parentNode) {
                modalElement.parentNode.removeChild(modalElement);
            }
        }, 300);
    }

    // 关闭最顶层的模态框
    closeTopModal() {
        const modals = Array.from(this.activeModals);
        if (modals.length > 0) {
            const topModal = modals[modals.length - 1];
            if (topModal._modalOptions?.keyboard !== false) {
                this.closeModal(topModal);
            }
        }
    }

    // 关闭所有模态框
    closeAllModals() {
        Array.from(this.activeModals).forEach(modal => {
            this.closeModal(modal);
        });
    }

    // 确认对话框
    confirm(options = {}) {
        const {
            title = '确认操作',
            message = '您确定要执行此操作吗？',
            type = 'warning', // 'danger', 'warning', 'info'
            confirmText = '确认',
            cancelText = '取消',
            onConfirm = null,
            onCancel = null
        } = options;

        const iconMap = {
            danger: '⚠️',
            warning: '⚠️',
            info: 'ℹ️'
        };

        const modal = this.createModal({
            title,
            content: `
                <div class="confirm-dialog ${type}">
                    <div class="modal-icon">${iconMap[type] || '❓'}</div>
                    <p>${message}</p>
                </div>
            `,
            type: `confirm-dialog ${type}`,
            showCloseButton: false,
            buttons: [
                {
                    text: cancelText,
                    class: 'btn-secondary',
                    action: 'cancel'
                },
                {
                    text: confirmText,
                    class: type === 'danger' ? 'btn-danger' : 'btn-primary',
                    action: 'confirm'
                }
            ]
        });

        // 绑定确认事件
        modal.addEventListener('modal:action', (e) => {
            const { action } = e.detail;
            if (action === 'confirm' && onConfirm) {
                onConfirm();
            } else if (action === 'cancel' && onCancel) {
                onCancel();
            }
            this.closeModal(modal);
        });

        this.showModal(modal);
        return modal;
    }

    // 警告对话框
    alert(options = {}) {
        const {
            title = '提示',
            message = '',
            type = 'info',
            buttonText = '确定',
            onClose = null
        } = options;

        const iconMap = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };

        const modal = this.createModal({
            title,
            content: `
                <div class="confirm-dialog ${type}">
                    <div class="modal-icon">${iconMap[type] || 'ℹ️'}</div>
                    <p>${message}</p>
                </div>
            `,
            type: `confirm-dialog ${type}`,
            showCloseButton: false,
            buttons: [
                {
                    text: buttonText,
                    class: 'btn-primary',
                    action: 'close'
                }
            ]
        });

        // 绑定关闭事件
        modal.addEventListener('modal:hide', () => {
            if (onClose) onClose();
        });

        this.showModal(modal);
        return modal;
    }

    // 加载对话框
    loading(options = {}) {
        const {
            title = '加载中...',
            message = '请稍候...'
        } = options;

        const modal = this.createModal({
            title,
            content: `
                <div class="modal-loading">
                    <div class="spinner"></div>
                    <p style="margin-top: 16px; text-align: center;">${message}</p>
                </div>
            `,
            showCloseButton: false,
            backdrop: false,
            keyboard: false,
            focus: false
        });

        this.showModal(modal);
        return modal;
    }
}

// 创建全局模态框管理器实例
window.modalManager = new ModalManager();

// 便捷方法
window.showModal = (options) => {
    const modal = window.modalManager.createModal(options);
    window.modalManager.showModal(modal);
    return modal;
};

window.closeModal = (modal) => {
    window.modalManager.closeModal(modal);
};

window.confirmDialog = (options) => {
    return window.modalManager.confirm(options);
};

window.alertDialog = (options) => {
    return window.modalManager.alert(options);
};

window.loadingDialog = (options) => {
    return window.modalManager.loading(options);
};

// 全局组件管理器实例
let componentManager;

// 页面加载完成后初始化组件管理器
document.addEventListener('DOMContentLoaded', () => {
    componentManager = new ComponentManager();

    // 将组件管理器暴露到全局作用域
    window.componentManager = componentManager;
});

// 导出组件管理器类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ComponentManager;
}
