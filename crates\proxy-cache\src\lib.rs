/*!
# Proxy Extensions Crate

这个crate整合了反向代理的所有扩展功能：
- 缓存系统 (Cache)
- 认证授权 (Auth)
- 监控指标 (Monitoring)
- API接口 (API)

通过特性门控，可以选择性启用需要的功能。
*/

// 重新导出统一的错误处理
pub use proxy_core::{ProxyError, Result};

// 原有缓存模块
pub mod disk;
pub mod memory;
pub mod redis;

// 新增扩展模块
#[cfg(feature = "auth")]
pub mod auth;

#[cfg(feature = "monitoring")]
pub mod monitoring;

#[cfg(feature = "api")]
pub mod api;

// 缓存核心功能
use async_trait::async_trait;
use std::collections::HashMap;
use std::time::Duration;

/// 统一的缓存接口
#[async_trait]
pub trait Cache: Send + Sync {
    async fn get(&self, key: &CacheKey) -> Result<Option<Vec<u8>>>;
    async fn set(&self, key: &CacheKey, value: Vec<u8>, ttl: Duration) -> Result<()>;
    async fn delete(&self, key: &CacheKey) -> Result<()>;
    async fn exists(&self, key: &CacheKey) -> Result<bool>;
    async fn clear(&self) -> Result<()>;
}

/// 扩展服务管理器 - 统一管理所有扩展功能
///
/// 注意：本结构体本身不是线程安全的，如需多线程共享请用 `Arc<Mutex<ExtensionManager>>` 包裹，
/// 或将 cache_services 字段替换为 DashMap。所有 cache 操作接口为不可变/可变借用，
/// 多线程场景下请确保外部同步。
pub struct ExtensionManager {
    cache_services: HashMap<String, Box<dyn Cache + Send + Sync + 'static>>,
    #[cfg(feature = "auth")]
    auth_service: Option<Box<dyn auth::AuthService>>,

    #[cfg(feature = "monitoring")]
    monitoring_service: Option<monitoring::MonitoringService>,

    #[cfg(feature = "api")]
    api_service: Option<api::ApiService>,
}

impl ExtensionManager {
    /// 创建新的扩展管理器
    pub fn new() -> Self {
        Self {
            cache_services: HashMap::new(),

            #[cfg(feature = "auth")]
            auth_service: None,

            #[cfg(feature = "monitoring")]
            monitoring_service: None,

            #[cfg(feature = "api")]
            api_service: None,
        }
    }

    /// 启用缓存服务（支持多实例/多命名空间）
    pub fn with_cache(
        mut self,
        namespace: impl Into<String>,
        cache_service: Box<dyn Cache + Send + Sync + 'static>,
    ) -> Self {
        self.cache_services.insert(namespace.into(), cache_service);
        self
    }

    /// 启用认证服务    #[cfg(feature = "auth")]
    #[cfg(feature = "auth")]
    pub fn with_auth(mut self, auth_service: Box<dyn auth::AuthService>) -> Self {
        self.auth_service = Some(auth_service);
        self
    }

    /// 启用监控服务
    #[cfg(feature = "monitoring")]
    pub fn with_monitoring(mut self, monitoring_service: monitoring::MonitoringService) -> Self {
        self.monitoring_service = Some(monitoring_service);
        self
    }

    /// 启用API服务
    #[cfg(feature = "api")]
    pub fn with_api(mut self, api_service: api::ApiService) -> Self {
        self.api_service = Some(api_service);
        self
    }

    /// 启动所有启用的服务
    pub async fn start(&self) -> Result<()> {
        tracing::info!("启动扩展服务...");

        if !self.cache_services.is_empty() {
            tracing::info!("缓存服务已启动");
        }
        #[cfg(feature = "auth")]
        if let Some(ref _auth) = self.auth_service {
            // 认证服务不需要启动过程，只是实例化即可使用
            tracing::info!("认证服务已启动");
        }

        #[cfg(feature = "monitoring")]
        if let Some(ref monitoring) = self.monitoring_service {
            monitoring.start().await?;
            tracing::info!("监控服务已启动");
        }

        #[cfg(feature = "api")]
        if let Some(ref api) = self.api_service {
            api.start().await?;
            tracing::info!("API服务已启动");
        }

        tracing::info!("所有扩展服务启动完成");
        Ok(())
    }

    /// 获取缓存服务（可按命名空间获取）
    pub fn cache(&self, namespace: Option<&str>) -> Option<&(dyn Cache + Send + Sync + 'static)> {
        match namespace {
            Some(ns) => self
                .cache_services
                .get(ns)
                .map(|c| c.as_ref() as &(dyn Cache + Send + Sync + 'static)),
            None => self
                .cache_services
                .values()
                .next()
                .map(|c| c.as_ref() as &(dyn Cache + Send + Sync + 'static)),
        }
    }

    /// 动态移除缓存服务
    pub fn remove_cache(
        &mut self,
        namespace: &str,
    ) -> Option<Box<dyn Cache + Send + Sync + 'static>> {
        self.cache_services.remove(namespace)
    }

    /// 获取缓存服务的可变引用（如需动态操作）
    pub fn cache_mut(
        &mut self,
        namespace: &str,
    ) -> Option<&mut Box<dyn Cache + Send + Sync + 'static>> {
        self.cache_services.get_mut(namespace)
    }
}

impl Default for ExtensionManager {
    fn default() -> Self {
        Self::new()
    }
}

pub use memory::CacheKey;
