# 🚀 SM智能代理系统 - 万能命令

## 📋 **当前万能命令 (v2024.12)**

### **标准万能命令**
```bash
sed -i '1s/^\xEF\xBB\xBF//' install.sh && sed -i '1s/^\xEF\xBB\xBF//' setup.sh && chmod +x install.sh && sudo rm -rf target/ && ./install.sh
```

### **简化版万能命令**
```bash
chmod +x install.sh && sudo ./install.sh
```

## 🎯 **万能命令功能**

### **✅ 自动处理**
- **BOM字符清理** - 自动清理UTF-8 BOM字符
- **权限设置** - 自动设置执行权限
- **编译缓存清理** - 清理旧的编译产物
- **权限提升** - 自动使用sudo权限

### **🛡️ 安全特性**
- **部署前风险检测** - 检查现有安装和运行服务
- **用户确认机制** - 发现风险时要求用户确认
- **配置备份** - 自动备份原始配置文件
- **详细状态显示** - 显示系统详细状态

### **🚀 智能功能**
- **性能优化选择** - 可选的服务器性能优化
- **Screen持久化** - 支持SSH断开后继续安装
- **智能路径检测** - 自动检测最佳部署路径
- **依赖智能安装** - 只安装缺失的依赖包

## 📊 **执行流程详解**

### **第1步: 预处理**
```bash
sed -i '1s/^\xEF\xBB\xBF//' install.sh    # 清理BOM字符
sed -i '1s/^\xEF\xBB\xBF//' setup.sh      # 清理BOM字符
chmod +x install.sh                        # 设置执行权限
sudo rm -rf target/                        # 清理编译缓存
```

### **第2步: 启动安装**
```bash
./install.sh                               # 启动智能安装器
```

### **第3步: 用户交互**
```
⚠️  部署前重要提醒
🛡️  部署前安全检查
🚀 是否启用服务器性能优化？
🖥️  是否启用Screen持久化？
```

### **第4步: 自动部署**
```
🔧 智能安装系统依赖
🔨 智能检查和编译项目
📍 智能检测部署路径
👤 创建服务用户
🔧 设置环境变量
📋 部署文件
🔒 设置安全权限
⚙️  创建systemd服务
🚀 创建智能启动脚本
🔥 配置防火墙
🛠️  创建管理工具
```

## 🎯 **不同场景的使用方式**

### **场景1: 首次安装 (推荐)**
```bash
# 完整万能命令
sed -i '1s/^\xEF\xBB\xBF//' install.sh && sed -i '1s/^\xEF\xBB\xBF//' setup.sh && chmod +x install.sh && sudo rm -rf target/ && ./install.sh

# 选择建议:
# 性能优化: y (推荐)
# Screen持久化: y (推荐)
# 重启时机: 1 (继续安装，稍后重启)
```

### **场景2: 更新部署**
```bash
# 标准万能命令 (会检测现有安装)
sed -i '1s/^\xEF\xBB\xBF//' install.sh && chmod +x install.sh && sudo ./install.sh

# 安全检查会显示:
# ⚠️  风险: 发现已编译的二进制文件
# 🚨 风险: SM服务正在运行中！
# 选择: c (继续部署，我已了解风险)
```

### **场景3: 开发环境**
```bash
# 简化命令
chmod +x install.sh && sudo ./install.sh

# 选择建议:
# 性能优化: n (跳过)
# Screen持久化: n (直接执行)
```

### **场景4: 生产环境**
```bash
# 完整命令 + 性能优化
sed -i '1s/^\xEF\xBB\xBF//' install.sh && chmod +x install.sh && sudo ./install.sh

# 选择建议:
# 性能优化: y (启用)
# 重启时机: 2 (现在重启) 或 1 (稍后重启)
# Screen持久化: y (推荐)
```

## 🔧 **故障排除**

### **常见问题**

#### **1. 权限问题**
```bash
# 错误: Permission denied
# 解决: 确保使用sudo
sudo ./install.sh
```

#### **2. BOM字符问题**
```bash
# 错误: /bin/bash^M: bad interpreter
# 解决: 运行完整万能命令
sed -i '1s/^\xEF\xBB\xBF//' install.sh && ./install.sh
```

#### **3. 编译缓存问题**
```bash
# 错误: 编译失败或版本冲突
# 解决: 清理编译缓存
sudo rm -rf target/ && ./install.sh
```

#### **4. Screen会话问题**
```bash
# 查看Screen状态
./setup.sh status

# 连接到Screen会话
./setup.sh connect

# 清理Screen会话
./setup.sh clean
```

## 📱 **快速命令参考**

### **一键命令**
```bash
# 🚀 完整部署 (推荐)
curl -sSL https://your-domain/install.sh | sudo bash

# 或本地部署
sed -i '1s/^\xEF\xBB\xBF//' install.sh && sudo ./install.sh
```

### **管理命令**
```bash
# 服务管理
sudo systemctl start sm      # 启动服务
sudo systemctl stop sm       # 停止服务
sudo systemctl status sm     # 查看状态
sudo systemctl restart sm    # 重启服务

# 日志查看
sudo journalctl -u sm -f     # 实时日志
sudo journalctl -u sm -n 50  # 最近50行

# Screen管理
./setup.sh status            # 查看Screen状态
./setup.sh connect           # 连接Screen会话
./setup.sh clean             # 清理Screen会话
```

### **快速测试**
```bash
# API测试
curl http://localhost:1319/api/health

# Web界面
# http://your-server:1319
# 默认账户: admin / admin888
```

## 🎯 **最佳实践**

### **✅ 推荐做法**
1. **使用完整万能命令** - 确保所有预处理步骤
2. **启用性能优化** - 生产环境建议启用
3. **使用Screen持久化** - 网络不稳定时推荐
4. **定期备份配置** - 重要配置文件备份
5. **监控服务状态** - 定期检查服务运行状态

### **⚠️ 注意事项**
1. **维护窗口部署** - 避免业务高峰期
2. **网络连接稳定** - 确保下载依赖不中断
3. **磁盘空间充足** - 至少1GB可用空间
4. **权限准备** - 确保有sudo权限
5. **备份重要数据** - 部署前备份关键数据

---

💡 **建议**: 首次使用时选择完整万能命令，启用所有推荐选项，获得最佳的部署体验！
