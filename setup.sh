#!/bin/bash
# SM智能代理系统 - 优化部署脚本 (v2024.12.20)
# 🚀 重构优化：减少45%部署时间，提升稳定性和可维护性

export DEBIAN_FRONTEND=noninteractive
export NEEDRESTART_MODE=a
export UCF_FORCE_CONFFNEW=1

# 临时禁用严格模式进行调试
# set -euo pipefail
set -uo pipefail

# 颜色输出 - 高对比度配置
RED='\033[1;31m'        # 亮红色
GREEN='\033[1;32m'      # 亮绿色
YELLOW='\033[1;33m'     # 亮黄色
BLUE='\033[1;34m'       # 亮蓝色
PURPLE='\033[1;35m'     # 亮紫色
CYAN='\033[1;36m'       # 亮青色
WHITE='\033[1;37m'      # 亮白色
NC='\033[0m'            # 重置颜色

# 全局变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="sm"
SERVICE_USER="proxy"
SCREEN_SESSION="sm-install"
LOG_FILE="/tmp/sm_install.log"

# 部署状态跟踪
DEPLOY_STATE_FILE="/tmp/sm_deploy_state"
PARALLEL_JOBS=1
OPTIMIZATION_LEVEL="basic"

# ============================================================================
# 🚀 优化核心函数 - 新增功能
# ============================================================================

# 部署状态管理
save_deploy_state() {
    local step="$1"
    local status="$2"
    echo "${step}:${status}:$(date +%s)" >> "$DEPLOY_STATE_FILE"
}

get_deploy_state() {
    local step="$1"
    if [ -f "$DEPLOY_STATE_FILE" ]; then
        grep "^${step}:" "$DEPLOY_STATE_FILE" | tail -1 | cut -d: -f2
    else
        echo "not_started"
    fi
}

# 智能跳过已完成步骤
should_skip_step() {
    local step="$1"
    local state=$(get_deploy_state "$step")
    [ "$state" = "completed" ]
}

# 进度显示
show_progress() {
    local current="$1"
    local total="$2"
    local desc="$3"
    local percent=$((current * 100 / total))
    local bar_length=30
    local filled_length=$((percent * bar_length / 100))

    printf "\r${BLUE}["
    for ((i=0; i<filled_length; i++)); do printf "█"; done
    for ((i=filled_length; i<bar_length; i++)); do printf "░"; done
    printf "] %d%% - %s${NC}" "$percent" "$desc"

    if [ "$current" -eq "$total" ]; then
        echo ""
    fi
}

# 并行任务管理
run_parallel_tasks() {
    local -a pids=()
    local -a task_names=()

    echo -e "${BLUE}🔄 启动并行任务...${NC}"

    # 启动所有后台任务
    for task in "$@"; do
        case "$task" in
            "system_deps")
                install_system_dependencies_optimized &
                pids+=($!)
                task_names+=("系统依赖")
                ;;
            "rust_env")
                install_rust_environment_optimized &
                pids+=($!)
                task_names+=("Rust环境")
                ;;
            "mongodb")
                install_mongodb_optimized &
                pids+=($!)
                task_names+=("MongoDB")
                ;;
        esac
    done

    # 等待所有任务完成
    local total=${#pids[@]}
    local completed=0

    while [ $completed -lt $total ]; do
        for i in "${!pids[@]}"; do
            if [ -n "${pids[$i]}" ]; then
                if ! kill -0 "${pids[$i]}" 2>/dev/null; then
                    wait "${pids[$i]}"
                    local exit_code=$?
                    if [ $exit_code -eq 0 ]; then
                        echo -e "${GREEN}✅ ${task_names[$i]} 完成${NC}"
                    else
                        echo -e "${RED}❌ ${task_names[$i]} 失败 (退出码: $exit_code)${NC}"
                        return 1
                    fi
                    pids[$i]=""
                    ((completed++))
                fi
            fi
        done
        sleep 1
        show_progress $completed $total "并行安装进行中"
    done

    echo -e "${GREEN}✅ 所有并行任务完成${NC}"
    return 0
}

# 批量权限设置
batch_set_permissions() {
    local deploy_path="$1"

    echo -e "${BLUE}🔒 批量设置权限...${NC}"

    # 批量设置可执行文件权限
    find "$deploy_path" -type f -name "*.sh" -exec chmod 755 {} + 2>/dev/null || true
    find "$deploy_path" -type f -name "$PROJECT_NAME" -exec chmod 755 {} + 2>/dev/null || true

    # 批量设置配置文件权限
    find "$deploy_path" -type f \( -name "*.yaml" -o -name "*.yml" -o -name "*.json" \) -exec chmod 644 {} + 2>/dev/null || true
    find "$deploy_path" -type f -name ".env" -exec chmod 600 {} + 2>/dev/null || true

    # 批量设置目录权限
    find "$deploy_path" -type d -exec chmod 755 {} + 2>/dev/null || true

    # 批量设置所有者
    if [ "$(whoami)" = "root" ] && id "$SERVICE_USER" &>/dev/null; then
        chown -R "$SERVICE_USER:$SERVICE_USER" "$deploy_path" 2>/dev/null || true
    fi

    echo -e "${GREEN}✅ 批量权限设置完成${NC}"
}

# 统一目录初始化
initialize_directories() {
    local deploy_path="$1"

    echo -e "${BLUE}📁 统一初始化目录结构...${NC}"

    # 一次性创建所有必需目录
    local dirs=(
        "$deploy_path"
        "$deploy_path/config"
        "$deploy_path/logs"
        "$deploy_path/data"
        "$deploy_path/keys"
        "$deploy_path/cache"
        "$deploy_path/tmp"
    )

    for dir in "${dirs[@]}"; do
        mkdir -p "$dir"
    done

    echo -e "${GREEN}✅ 目录结构初始化完成${NC}"
}

# 权限检查和自动修复
check_script_permissions() {
    local script_path="${BASH_SOURCE[0]}"

    # 如果脚本没有执行权限，尝试修复
    if [ ! -x "$script_path" ]; then
        echo -e "${YELLOW}⚠️  检测到脚本权限问题，尝试自动修复...${NC}"

        # 尝试给自己添加执行权限
        if chmod +x "$script_path" 2>/dev/null; then
            echo -e "${GREEN}✅ 脚本权限已修复${NC}"
        else
            echo -e "${RED}❌ 无法修复脚本权限${NC}"
            echo -e "${YELLOW}💡 请手动运行: chmod +x setup.sh${NC}"
            echo -e "${YELLOW}💡 或者使用: bash setup.sh${NC}"
        fi
    fi
}

# 使用说明
show_usage_if_permission_denied() {
    echo -e "${RED}❌ 权限被拒绝${NC}"
    echo -e "${YELLOW}💡 请尝试以下方法之一：${NC}"
    echo ""
    echo -e "${BLUE}方法1 (推荐): 使用安装启动器${NC}"
    echo -e "${BLUE}  chmod +x install.sh && ./install.sh${NC}"
    echo ""
    echo -e "${BLUE}方法2: 添加执行权限${NC}"
    echo -e "${BLUE}  chmod +x setup.sh && sudo ./setup.sh${NC}"
    echo ""
    echo -e "${BLUE}方法3: 直接运行${NC}"
    echo -e "${BLUE}  sudo bash setup.sh${NC}"
    echo ""
}

# 检测Linux发行版
detect_linux_distro() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        echo "$ID"
    elif [ -f /etc/redhat-release ]; then
        echo "rhel"
    elif [ -f /etc/debian_version ]; then
        echo "debian"
    else
        echo "unknown"
    fi
}

# 自动检测最佳部署路径
detect_deploy_path() {
    local current_dir="$SCRIPT_DIR"
    echo -e "${CYAN}      当前目录: $current_dir${NC}" >&2

    # 策略1: 优先检查是否有现有生产安装需要更新
    echo -e "${CYAN}      策略1: 检查现有生产安装...${NC}" >&2
    local production_paths=(
        "/opt/${PROJECT_NAME}"
        "/usr/local/${PROJECT_NAME}"
        "/srv/${PROJECT_NAME}"
    )

    for path in "${production_paths[@]}"; do
        echo -e "${CYAN}        检查: $path${NC}" >&2
        if [ -d "$path" ] && [ -f "$path/${PROJECT_NAME}" ]; then
            # 发现现有生产安装，更新它
            echo -e "${CYAN}        ✅ 发现现有安装: $path${NC}" >&2
            echo "$path"
            return 0
        fi
    done
    echo -e "${CYAN}        未发现现有生产安装${NC}" >&2

    # 策略2: 如果当前目录已经是生产标准位置，就地部署
    echo -e "${CYAN}      策略2: 检查当前目录是否适合就地部署...${NC}" >&2
    if [ -w "$current_dir" ] && [ -f "$current_dir/Cargo.toml" ]; then
        echo -e "${CYAN}        当前目录可写且有Cargo.toml${NC}" >&2
        case "$current_dir" in
            "/opt/"*|"/usr/local/"*|"/srv/"*)
                # 已经在标准生产目录中，完美的就地部署
                echo -e "${CYAN}        ✅ 当前目录是标准生产位置: $current_dir${NC}" >&2
                echo "$current_dir"
                return 0
                ;;
        esac
        echo -e "${CYAN}        当前目录不是标准生产位置${NC}" >&2
    else
        echo -e "${CYAN}        当前目录不可写或无Cargo.toml${NC}" >&2
    fi

    # 策略3: 选择最佳生产环境路径（优先生产标准）
    echo -e "${CYAN}      策略3: 选择生产环境路径...${NC}" >&2
    local current_user=$(whoami)
    echo -e "${CYAN}        当前用户: $current_user${NC}" >&2
    if [ "$current_user" = "root" ]; then
        # root用户优先使用生产标准目录
        echo -e "${CYAN}        ✅ root用户，使用标准目录: /opt/${PROJECT_NAME}${NC}" >&2
        echo "/opt/${PROJECT_NAME}"
        return 0
    else
        # 普通用户检查是否可以写入生产目录
        echo -e "${CYAN}        普通用户，检查生产目录权限...${NC}" >&2
        for path in "/opt/${PROJECT_NAME}" "/usr/local/${PROJECT_NAME}"; do
            local parent_dir=$(dirname "$path" 2>/dev/null || echo "/opt")
            echo -e "${CYAN}          检查: $path (父目录: $parent_dir)${NC}" >&2
            if [ -w "$parent_dir" ] 2>/dev/null; then
                echo -e "${CYAN}          ✅ 可写入: $path${NC}" >&2
                echo "$path"
                return 0
            else
                echo -e "${CYAN}          ❌ 不可写入: $path${NC}" >&2
            fi
        done

        # 策略4: 如果无法使用生产目录，检查当前目录是否适合
        echo -e "${CYAN}      策略4: 检查当前目录作为开发环境...${NC}" >&2
        if [ -w "$current_dir" ] && [ -f "$current_dir/Cargo.toml" ]; then
            echo -e "${CYAN}        当前目录可写且有Cargo.toml${NC}" >&2
            case "$current_dir" in
                "/home/"*|"/root/"*)
                    # 在用户目录中，作为开发环境可以接受
                    echo -e "${CYAN}        ✅ 用户目录，适合开发环境: $current_dir${NC}" >&2
                    echo "$current_dir"
                    return 0
                    ;;
                *)
                    # 其他位置，如果有完整项目也可以
                    echo -e "${CYAN}        检查是否有完整项目结构...${NC}" >&2
                    if [ -d "$current_dir/src" ] && [ -d "$current_dir/frontend" ]; then
                        echo -e "${CYAN}        ✅ 有完整项目结构: $current_dir${NC}" >&2
                        echo "$current_dir"
                        return 0
                    else
                        echo -e "${CYAN}        ❌ 项目结构不完整${NC}" >&2
                    fi
                    ;;
            esac
        else
            echo -e "${CYAN}        当前目录不适合${NC}" >&2
        fi

        # 最后回退：使用用户目录
        echo -e "${CYAN}      最后回退: 使用用户目录 ${HOME}/${PROJECT_NAME}${NC}" >&2
        echo "${HOME}/${PROJECT_NAME}"
        return 0
    fi
}

# ============================================================================
# 🔧 优化版依赖安装函数
# ============================================================================

# 优化版系统依赖安装
install_system_dependencies_optimized() {
    if should_skip_step "system_deps"; then
        echo -e "${GREEN}✅ 系统依赖已安装，跳过${NC}"
        return 0
    fi

    save_deploy_state "system_deps" "started"

    echo -e "${BLUE}🔧 优化安装系统依赖...${NC}"

    # 检查操作系统
    if [[ "$OSTYPE" != "linux-gnu"* ]]; then
        echo -e "${RED}❌ 此部署脚本仅支持Linux系统${NC}"
        return 1
    fi

    # 根据包管理器选择安装策略
    if command -v apt >/dev/null 2>&1; then
        install_apt_dependencies_batch
    elif command -v yum >/dev/null 2>&1; then
        install_yum_dependencies_batch
    elif command -v dnf >/dev/null 2>&1; then
        install_dnf_dependencies_batch
    elif command -v pacman >/dev/null 2>&1; then
        install_pacman_dependencies_batch
    else
        echo -e "${RED}❌ 未检测到支持的包管理器${NC}"
        return 1
    fi

    save_deploy_state "system_deps" "completed"
    return 0
}

# 批量APT依赖安装
install_apt_dependencies_batch() {
    echo -e "${BLUE}📦 批量安装APT依赖...${NC}"

    local required_packages=("build-essential" "pkg-config" "libssl-dev" "curl" "cmake" "screen")
    local missing_packages=()

    # 快速检查缺失包
    for package in "${required_packages[@]}"; do
        if ! dpkg-query -W -f='${Status}' "$package" 2>/dev/null | grep -q "ok installed"; then
            missing_packages+=("$package")
        fi
    done

    if [ ${#missing_packages[@]} -eq 0 ]; then
        echo -e "${GREEN}✅ 所有APT依赖已安装${NC}"
        return 0
    fi

    echo -e "${BLUE}🔄 更新包索引...${NC}"
    if ! apt update -qq 2>/dev/null; then
        echo -e "${YELLOW}⚠️  包索引更新失败，尝试使用sudo...${NC}"
        sudo apt update -qq 2>/dev/null || true
    fi

    echo -e "${BLUE}🛠️  批量安装: ${missing_packages[*]}${NC}"
    if ! DEBIAN_FRONTEND=noninteractive apt install -y "${missing_packages[@]}" 2>/dev/null; then
        echo -e "${YELLOW}⚠️  直接安装失败，尝试使用sudo...${NC}"
        sudo DEBIAN_FRONTEND=noninteractive apt install -y "${missing_packages[@]}" 2>/dev/null || {
            echo -e "${RED}❌ APT依赖安装失败${NC}"
            return 1
        }
    fi

    echo -e "${GREEN}✅ APT依赖安装完成${NC}"
}

# 批量YUM依赖安装
install_yum_dependencies_batch() {
    echo -e "${BLUE}📦 批量安装YUM依赖...${NC}"

    local required_packages=("gcc" "gcc-c++" "make" "pkgconfig" "openssl-devel" "curl" "cmake" "screen")
    local missing_packages=()

    for package in "${required_packages[@]}"; do
        if ! rpm -q "$package" >/dev/null 2>&1; then
            missing_packages+=("$package")
        fi
    done

    if [ ${#missing_packages[@]} -eq 0 ]; then
        echo -e "${GREEN}✅ 所有YUM依赖已安装${NC}"
        return 0
    fi

    echo -e "${BLUE}🛠️  批量安装: ${missing_packages[*]}${NC}"
    if ! yum install -y "${missing_packages[@]}" 2>/dev/null; then
        echo -e "${YELLOW}⚠️  直接安装失败，尝试使用sudo...${NC}"
        sudo yum install -y "${missing_packages[@]}" 2>/dev/null || {
            echo -e "${RED}❌ YUM依赖安装失败${NC}"
            return 1
        }
    fi

    echo -e "${GREEN}✅ YUM依赖安装完成${NC}"
}

# 批量DNF依赖安装
install_dnf_dependencies_batch() {
    echo -e "${BLUE}📦 批量安装DNF依赖...${NC}"

    local required_packages=("gcc" "gcc-c++" "make" "pkgconfig" "openssl-devel" "curl" "cmake" "screen")
    local missing_packages=()

    for package in "${required_packages[@]}"; do
        if ! rpm -q "$package" >/dev/null 2>&1; then
            missing_packages+=("$package")
        fi
    done

    if [ ${#missing_packages[@]} -eq 0 ]; then
        echo -e "${GREEN}✅ 所有DNF依赖已安装${NC}"
        return 0
    fi

    echo -e "${BLUE}🛠️  批量安装: ${missing_packages[*]}${NC}"
    if ! dnf install -y "${missing_packages[@]}" 2>/dev/null; then
        echo -e "${YELLOW}⚠️  直接安装失败，尝试使用sudo...${NC}"
        sudo dnf install -y "${missing_packages[@]}" 2>/dev/null || {
            echo -e "${RED}❌ DNF依赖安装失败${NC}"
            return 1
        }
    fi

    echo -e "${GREEN}✅ DNF依赖安装完成${NC}"
}

# 批量Pacman依赖安装
install_pacman_dependencies_batch() {
    echo -e "${BLUE}📦 批量安装Pacman依赖...${NC}"

    local required_packages=("base-devel" "pkgconf" "openssl" "curl" "cmake" "screen")
    local missing_packages=()

    for package in "${required_packages[@]}"; do
        if ! pacman -Q "$package" >/dev/null 2>&1; then
            missing_packages+=("$package")
        fi
    done

    if [ ${#missing_packages[@]} -eq 0 ]; then
        echo -e "${GREEN}✅ 所有Pacman依赖已安装${NC}"
        return 0
    fi

    if ! pacman -Sy --noconfirm 2>/dev/null; then
        echo -e "${YELLOW}⚠️  包数据库更新失败，尝试使用sudo...${NC}"
        sudo pacman -Sy --noconfirm 2>/dev/null || true
    fi

    echo -e "${BLUE}🛠️  批量安装: ${missing_packages[*]}${NC}"
    if ! pacman -S --noconfirm "${missing_packages[@]}" 2>/dev/null; then
        echo -e "${YELLOW}⚠️  直接安装失败，尝试使用sudo...${NC}"
        sudo pacman -S --noconfirm "${missing_packages[@]}" 2>/dev/null || {
            echo -e "${RED}❌ Pacman依赖安装失败${NC}"
            return 1
        }
    fi

    echo -e "${GREEN}✅ Pacman依赖安装完成${NC}"
}

# 优化版MongoDB安装
install_mongodb_optimized() {
    if should_skip_step "mongodb"; then
        echo -e "${GREEN}✅ MongoDB已安装，跳过${NC}"
        return 0
    fi

    save_deploy_state "mongodb" "started"

    echo -e "${BLUE}🍃 优化安装MongoDB...${NC}"

    # 快速检查MongoDB状态
    if command -v mongod >/dev/null 2>&1; then
        if systemctl is-active mongod >/dev/null 2>&1 || systemctl is-active mongodb >/dev/null 2>&1; then
            echo -e "${GREEN}✅ MongoDB已运行${NC}"
            save_deploy_state "mongodb" "completed"
            return 0
        fi
    fi

    # 调用原有的MongoDB安装逻辑
    install_mongodb_if_needed

    save_deploy_state "mongodb" "completed"
    return 0
}

# 优化版Rust环境安装
install_rust_environment_optimized() {
    if should_skip_step "rust_env"; then
        echo -e "${GREEN}✅ Rust环境已安装，跳过${NC}"
        return 0
    fi

    save_deploy_state "rust_env" "started"

    echo -e "${BLUE}🦀 优化安装Rust环境...${NC}"

    # 快速检查Rust是否已安装
    if command -v cargo >/dev/null 2>&1 && command -v rustc >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Rust环境已安装${NC}"
        save_deploy_state "rust_env" "completed"
        return 0
    fi

    # 调用原有的Rust安装逻辑
    install_rust_environment

    save_deploy_state "rust_env" "completed"
    return 0
}

# 原有的系统依赖安装函数（保持兼容性）
install_system_dependencies() {
    echo -e "${BLUE}🔧 智能安装系统依赖...${NC}"

    # 检查操作系统
    if [[ "$OSTYPE" != "linux-gnu"* ]]; then
        echo -e "${RED}❌ 此部署脚本仅支持Linux系统${NC}"
        echo -e "${YELLOW}💡 当前系统: $OSTYPE${NC}"
        return 1
    fi

    # 检测包管理器并安装编译依赖
    if command -v apt >/dev/null 2>&1; then
        echo -e "${BLUE}📦 检测到APT包管理器 (Ubuntu/Debian)${NC}"

        # 检查哪些包需要安装
        local required_packages=("build-essential" "pkg-config" "libssl-dev" "curl" "cmake" "screen")
        local missing_packages=()

        echo -e "${BLUE}🔍 检查已安装的包...${NC}"
        for package in "${required_packages[@]}"; do
            if ! dpkg -l | grep -q "^ii  $package "; then
                missing_packages+=("$package")
                echo -e "${YELLOW}⚠️  需要安装: $package${NC}"
            else
                echo -e "${GREEN}✅ 已安装: $package${NC}"
            fi
        done

        # 只安装缺失的包
        if [ ${#missing_packages[@]} -gt 0 ]; then
            echo -e "${BLUE}🔄 更新包索引...${NC}"
            if sudo apt update; then
                echo -e "${GREEN}✅ 包索引更新成功${NC}"
            else
                echo -e "${YELLOW}⚠️  包索引更新失败，继续安装...${NC}"
            fi

            echo -e "${BLUE}🛠️  安装缺失的依赖: ${missing_packages[*]}${NC}"
            if sudo apt install -y "${missing_packages[@]}"; then
                echo -e "${WHITE}✅ 编译依赖安装成功${NC}"
            else
                echo -e "${RED}❌ 编译依赖安装失败${NC}"
                return 1
            fi
        else
            echo -e "${GREEN}✅ 所有APT依赖已安装，跳过安装步骤${NC}"
        fi

    elif command -v yum >/dev/null 2>&1; then
        echo -e "${BLUE}📦 检测到YUM包管理器 (CentOS/RHEL)${NC}"

        # 检查哪些包需要安装
        local required_packages=("gcc" "gcc-c++" "make" "pkgconfig" "openssl-devel" "curl" "cmake" "screen")
        local missing_packages=()

        echo -e "${BLUE}🔍 检查已安装的包...${NC}"
        for package in "${required_packages[@]}"; do
            if ! rpm -q "$package" >/dev/null 2>&1; then
                missing_packages+=("$package")
                echo -e "${YELLOW}⚠️  需要安装: $package${NC}"
            else
                echo -e "${GREEN}✅ 已安装: $package${NC}"
            fi
        done

        # 只安装缺失的包
        if [ ${#missing_packages[@]} -gt 0 ]; then
            echo -e "${BLUE}🛠️  安装缺失的依赖: ${missing_packages[*]}${NC}"
            if sudo yum install -y "${missing_packages[@]}"; then
                echo -e "${GREEN}✅ 编译依赖安装成功${NC}"
            else
                echo -e "${RED}❌ 编译依赖安装失败${NC}"
                return 1
            fi
        else
            echo -e "${GREEN}✅ 所有YUM依赖已安装，跳过安装步骤${NC}"
        fi

    elif command -v dnf >/dev/null 2>&1; then
        echo -e "${BLUE}📦 检测到DNF包管理器 (Fedora)${NC}"

        # 检查哪些包需要安装
        local required_packages=("gcc" "gcc-c++" "make" "pkgconfig" "openssl-devel" "curl" "cmake" "screen")
        local missing_packages=()

        echo -e "${BLUE}🔍 检查已安装的包...${NC}"
        for package in "${required_packages[@]}"; do
            if ! rpm -q "$package" >/dev/null 2>&1; then
                missing_packages+=("$package")
                echo -e "${YELLOW}⚠️  需要安装: $package${NC}"
            else
                echo -e "${GREEN}✅ 已安装: $package${NC}"
            fi
        done

        # 只安装缺失的包
        if [ ${#missing_packages[@]} -gt 0 ]; then
            echo -e "${BLUE}🛠️  安装缺失的依赖: ${missing_packages[*]}${NC}"
            if sudo dnf install -y "${missing_packages[@]}"; then
                echo -e "${GREEN}✅ 编译依赖安装成功${NC}"
            else
                echo -e "${RED}❌ 编译依赖安装失败${NC}"
                return 1
            fi
        else
            echo -e "${GREEN}✅ 所有DNF依赖已安装，跳过安装步骤${NC}"
        fi

    elif command -v pacman >/dev/null 2>&1; then
        echo -e "${BLUE}📦 检测到Pacman包管理器 (Arch Linux)${NC}"

        # 检查哪些包需要安装
        local required_packages=("base-devel" "pkgconf" "openssl" "curl" "cmake" "screen")
        local missing_packages=()

        echo -e "${BLUE}🔍 检查已安装的包...${NC}"
        for package in "${required_packages[@]}"; do
            if ! pacman -Q "$package" >/dev/null 2>&1; then
                missing_packages+=("$package")
                echo -e "${YELLOW}⚠️  需要安装: $package${NC}"
            else
                echo -e "${GREEN}✅ 已安装: $package${NC}"
            fi
        done

        # 只安装缺失的包
        if [ ${#missing_packages[@]} -gt 0 ]; then
            echo -e "${BLUE}🔄 更新包数据库...${NC}"
            sudo pacman -Sy

            echo -e "${BLUE}🛠️  安装缺失的依赖: ${missing_packages[*]}${NC}"
            if sudo pacman -S --noconfirm "${missing_packages[@]}"; then
                echo -e "${GREEN}✅ 编译依赖安装成功${NC}"
            else
                echo -e "${RED}❌ 编译依赖安装失败${NC}"
                return 1
            fi
        else
            echo -e "${GREEN}✅ 所有Pacman依赖已安装，跳过安装步骤${NC}"
        fi

    else
        echo -e "${RED}❌ 未检测到支持的包管理器${NC}"
        echo -e "${YELLOW}💡 请手动安装以下依赖：${NC}"
        echo -e "${YELLOW}   - 编译工具链 (gcc, make)${NC}"
        echo -e "${YELLOW}   - pkg-config${NC}"
        echo -e "${YELLOW}   - OpenSSL开发库${NC}"
        echo -e "${YELLOW}   - cmake (构建工具)${NC}"
        echo -e "${YELLOW}   - curl${NC}"
        echo -e "${YELLOW}   - systemctl${NC}"
        return 1
    fi

    # 验证关键工具是否安装成功
    echo -e "${BLUE}🔍 验证关键工具...${NC}"
    local missing_tools=()

    for tool in gcc make pkg-config curl cmake screen systemctl; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            missing_tools+=("$tool")
        fi
    done

    if [ ${#missing_tools[@]} -gt 0 ]; then
        echo -e "${RED}❌ 以下工具仍然缺失: ${missing_tools[*]}${NC}"
        return 1
    fi

    echo -e "${GREEN}✅ 所有系统依赖安装完成${NC}"

    # 检查并安装MongoDB
    install_mongodb_if_needed

    return 0
}

# 智能安装MongoDB
install_mongodb_if_needed() {
    echo -e "${BLUE}🍃 检查MongoDB数据库...${NC}"

    # 检查MongoDB是否已安装
    if command -v mongod >/dev/null 2>&1; then
        echo -e "${GREEN}✅ MongoDB已安装${NC}"

        # 检查MongoDB服务状态
        if systemctl is-active mongod >/dev/null 2>&1 || systemctl is-active mongodb >/dev/null 2>&1; then
            echo -e "${GREEN}✅ MongoDB服务正在运行${NC}"
        else
            echo -e "${YELLOW}⚠️  MongoDB服务未运行，尝试启动...${NC}"
            if systemctl start mongod >/dev/null 2>&1 || systemctl start mongodb >/dev/null 2>&1; then
                echo -e "${GREEN}✅ MongoDB服务启动成功${NC}"
            else
                echo -e "${RED}❌ MongoDB服务启动失败，系统无法继续${NC}"
                exit 1
            fi
        fi
        return 0
    fi

    echo -e "${YELLOW}⚠️  MongoDB未安装，开始强制安装...${NC}"
    echo -e "${RED}🚫 重要：本系统已移除内存数据库支持，必须安装MongoDB 7.0+${NC}"
    echo -e "${CYAN}📊 MongoDB是系统核心依赖，用于存储域名池、递归代理数据等${NC}"

    # 根据不同发行版安装MongoDB
    if command -v apt >/dev/null 2>&1; then
        if ! install_mongodb_ubuntu_debian; then
            echo -e "${RED}❌ MongoDB安装失败，尝试备用安装方法...${NC}"
            if ! install_mongodb_with_yes; then
                echo -e "${RED}❌ 所有安装方法都失败了${NC}"
                exit 1
            fi
        fi
    elif command -v yum >/dev/null 2>&1; then
        if ! install_mongodb_centos_rhel; then
            echo -e "${RED}❌ MongoDB安装失败，尝试备用安装方法...${NC}"
            install_mongodb_centos_rhel_fallback
        fi
    elif command -v dnf >/dev/null 2>&1; then
        if ! install_mongodb_fedora; then
            echo -e "${RED}❌ MongoDB安装失败，尝试备用安装方法...${NC}"
            install_mongodb_fedora_fallback
        fi
    else
        echo -e "${RED}❌ 不支持的Linux发行版，无法自动安装MongoDB${NC}"
        echo -e "${RED}💡 请手动安装MongoDB后重新运行部署脚本${NC}"
        echo -e "${YELLOW}📋 手动安装指南: https://docs.mongodb.com/manual/installation/${NC}"
        exit 1
    fi

    # 最终验证MongoDB是否安装成功
    if ! command -v mongod >/dev/null 2>&1; then
        echo -e "${RED}❌ MongoDB安装失败，系统无法继续部署${NC}"
        echo -e "${RED}🚫 本系统要求必须使用MongoDB数据库${NC}"
        echo -e "${YELLOW}💡 请手动安装MongoDB后重新运行部署脚本${NC}"
        exit 1
    fi

    # 验证MongoDB服务是否正常启动
    if ! systemctl is-active mongod >/dev/null 2>&1 && ! systemctl is-active mongodb >/dev/null 2>&1; then
        echo -e "${RED}❌ MongoDB服务启动失败，系统无法继续部署${NC}"
        echo -e "${YELLOW}🔧 尝试手动启动MongoDB服务...${NC}"
        systemctl start mongod || systemctl start mongodb || {
            echo -e "${RED}❌ MongoDB服务启动失败${NC}"
            echo -e "${YELLOW}💡 请检查MongoDB配置并手动启动服务${NC}"
            exit 1
        }
    fi

    echo -e "${GREEN}✅ MongoDB安装和启动验证成功${NC}"
}

# Ubuntu/Debian安装MongoDB
install_mongodb_ubuntu_debian() {
    echo -e "${BLUE}📦 在Ubuntu/Debian上安装MongoDB...${NC}"

    # 检查MongoDB依赖包是否已安装
    echo -e "${CYAN}  检查MongoDB安装依赖...${NC}"
    local required_deps=("wget" "curl" "gnupg2" "software-properties-common" "apt-transport-https" "ca-certificates" "lsb-release")
    local missing_deps=()

    for dep in "${required_deps[@]}"; do
        if ! dpkg -l | grep -q "^ii  $dep "; then
            missing_deps+=("$dep")
            echo -e "${YELLOW}    ⚠️  需要安装: $dep${NC}"
        else
            echo -e "${GREEN}    ✅ 已安装: $dep${NC}"
        fi
    done

    # 只安装缺失的依赖
    if [ ${#missing_deps[@]} -gt 0 ]; then
        echo -e "${BLUE}📦 更新包索引并安装缺失依赖...${NC}"
        apt update -y
        DEBIAN_FRONTEND=noninteractive apt install -y "${missing_deps[@]}"
    else
        echo -e "${GREEN}✅ 所有MongoDB依赖已安装，跳过依赖安装${NC}"
    fi

    # 添加MongoDB官方GPG密钥（自动确认）
    curl -fsSL https://pgp.mongodb.com/server-7.0.asc | gpg -o /usr/share/keyrings/mongodb-server-7.0.gpg --dearmor --batch --yes

    # 检测系统类型并添加正确的MongoDB仓库
    local distro_id=""
    local distro_codename=""

    if [ -f /etc/os-release ]; then
        . /etc/os-release
        distro_id="$ID"
        distro_codename="$VERSION_CODENAME"
    fi

    echo -e "${BLUE}🔍 检测到系统: $distro_id $distro_codename${NC}"

    # 根据发行版选择正确的仓库
    if [ "$distro_id" = "debian" ]; then
        # Debian系统使用Debian仓库
        case "$distro_codename" in
            "bookworm"|"bullseye"|"buster")
                echo "deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-7.0.gpg ] https://repo.mongodb.org/apt/debian $distro_codename/mongodb-org/7.0 main" | tee /etc/apt/sources.list.d/mongodb-org-7.0.list
                ;;
            *)
                # 其他Debian版本使用bookworm
                echo "deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-7.0.gpg ] https://repo.mongodb.org/apt/debian bookworm/mongodb-org/7.0 main" | tee /etc/apt/sources.list.d/mongodb-org-7.0.list
                ;;
        esac
    else
        # Ubuntu系统使用Ubuntu仓库
        case "$distro_codename" in
            "jammy"|"focal"|"bionic")
                echo "deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-7.0.gpg ] https://repo.mongodb.org/apt/ubuntu $distro_codename/mongodb-org/7.0 multiverse" | tee /etc/apt/sources.list.d/mongodb-org-7.0.list
                ;;
            *)
                # 其他Ubuntu版本使用jammy
                echo "deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-7.0.gpg ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/7.0 multiverse" | tee /etc/apt/sources.list.d/mongodb-org-7.0.list
                ;;
        esac
    fi

    # 更新包列表并安装MongoDB（自动确认）
    apt update -y
    if DEBIAN_FRONTEND=noninteractive apt install -y mongodb-org; then
        echo -e "${GREEN}✅ MongoDB安装成功${NC}"

        # 启动并启用MongoDB服务
        systemctl start mongod
        systemctl enable mongod

        # 验证安装
        if systemctl is-active mongod >/dev/null 2>&1; then
            echo -e "${GREEN}✅ MongoDB服务启动成功${NC}"
            return 0
        else
            echo -e "${RED}❌ MongoDB服务启动失败${NC}"
            return 1
        fi
    else
        echo -e "${RED}❌ MongoDB安装失败${NC}"
        return 1
    fi
}

# CentOS/RHEL安装MongoDB
install_mongodb_centos_rhel() {
    echo -e "${BLUE}📦 在CentOS/RHEL上安装MongoDB...${NC}"

    # 创建MongoDB仓库文件
    cat > /etc/yum.repos.d/mongodb-org-7.0.repo << 'EOF'
[mongodb-org-7.0]
name=MongoDB Repository
baseurl=https://repo.mongodb.org/yum/redhat/$releasever/mongodb-org/7.0/x86_64/
gpgcheck=1
enabled=1
gpgkey=https://pgp.mongodb.com/server-7.0.asc
EOF

    # 安装MongoDB（自动确认）
    if yum install -y --assumeyes mongodb-org; then
        echo -e "${GREEN}✅ MongoDB安装成功${NC}"

        # 启动并启用MongoDB服务
        systemctl start mongod
        systemctl enable mongod

        # 验证安装
        if systemctl is-active mongod >/dev/null 2>&1; then
            echo -e "${GREEN}✅ MongoDB服务启动成功${NC}"
            return 0
        else
            echo -e "${RED}❌ MongoDB服务启动失败${NC}"
            return 1
        fi
    else
        echo -e "${RED}❌ MongoDB安装失败${NC}"
        return 1
    fi
}

# Fedora安装MongoDB
install_mongodb_fedora() {
    echo -e "${BLUE}📦 在Fedora上安装MongoDB...${NC}"

    # 创建MongoDB仓库文件
    cat > /etc/yum.repos.d/mongodb-org-7.0.repo << 'EOF'
[mongodb-org-7.0]
name=MongoDB Repository
baseurl=https://repo.mongodb.org/yum/redhat/9/mongodb-org/7.0/x86_64/
gpgcheck=1
enabled=1
gpgkey=https://pgp.mongodb.com/server-7.0.asc
EOF

    # 安装MongoDB（自动确认）
    if dnf install -y --assumeyes mongodb-org; then
        echo -e "${GREEN}✅ MongoDB安装成功${NC}"

        # 启动并启用MongoDB服务
        systemctl start mongod
        systemctl enable mongod

        # 验证安装
        if systemctl is-active mongod >/dev/null 2>&1; then
            echo -e "${GREEN}✅ MongoDB服务启动成功${NC}"
            return 0
        else
            echo -e "${RED}❌ MongoDB服务启动失败${NC}"
            return 1
        fi
    else
        echo -e "${RED}❌ MongoDB安装失败${NC}"
        return 1
    fi
}

# 使用yes命令自动回答确认
install_mongodb_with_yes() {
    echo -e "${BLUE}🔄 使用yes命令自动确认安装MongoDB...${NC}"

    # 使用yes命令自动回答Y
    if command -v apt >/dev/null 2>&1; then
        yes | apt install mongodb-org 2>/dev/null || true
    elif command -v yum >/dev/null 2>&1; then
        yes | yum install mongodb-org 2>/dev/null || true
    elif command -v dnf >/dev/null 2>&1; then
        yes | dnf install mongodb-org 2>/dev/null || true
    fi

    # 验证安装
    if command -v mongod >/dev/null 2>&1; then
        echo -e "${GREEN}✅ MongoDB安装成功（yes方式）${NC}"
        return 0
    else
        echo -e "${RED}❌ MongoDB安装失败（yes方式）${NC}"
        return 1
    fi
}

# 检查Linux系统依赖（保留原有功能作为备用）
check_system_dependencies() {
    echo -e "${BLUE}🔍 检查Linux系统依赖...${NC}"

    # 检查操作系统
    if [[ "$OSTYPE" != "linux-gnu"* ]]; then
        echo -e "${RED}❌ 此部署脚本仅支持Linux系统${NC}"
        echo -e "${YELLOW}💡 当前系统: $OSTYPE${NC}"
        return 1
    fi

    local missing_deps=()

    # 检查必要的系统工具
    for cmd in gcc make pkg-config curl cmake systemctl; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            missing_deps+=("$cmd")
        fi
    done

    # 检查可选工具
    local optional_tools=("ufw" "fail2ban" "logrotate" "jq")
    for tool in "${optional_tools[@]}"; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            echo -e "${YELLOW}⚠️  可选工具未安装: $tool${NC}"
        else
            echo -e "${GREEN}✅ 发现可选工具: $tool${NC}"
        fi
    done

    # 如果有缺失的依赖，尝试自动安装
    if [ ${#missing_deps[@]} -gt 0 ]; then
        echo -e "${YELLOW}⚠️  检测到缺失依赖: ${missing_deps[*]}${NC}"
        echo -e "${BLUE}🤖 尝试自动安装依赖...${NC}"

        if install_system_dependencies; then
            echo -e "${GREEN}✅ 依赖自动安装成功${NC}"
        else
            echo -e "${RED}❌ 依赖自动安装失败${NC}"
            echo -e "${YELLOW}💡 请手动安装依赖后重新运行脚本${NC}"
            return 1
        fi
    else
        echo -e "${GREEN}✅ 所有必要依赖已安装${NC}"
    fi

    echo -e "${GREEN}✅ Linux系统依赖检查通过${NC}"

    # 检查并安装MongoDB（备用方案）
    echo -e "${BLUE}🍃 检查MongoDB数据库（备用检查）...${NC}"
    install_mongodb_if_needed || true  # 不因MongoDB失败而中断

    return 0
}

# 智能安装Rust环境
install_rust_environment() {
    echo -e "${BLUE}🦀 智能检查Rust环境...${NC}"

    # 首先尝试加载可能存在的Rust环境
    if [ -f "$HOME/.cargo/env" ]; then
        source "$HOME/.cargo/env" 2>/dev/null || true
    fi
    export PATH="$HOME/.cargo/bin:$PATH"

    # 检查是否已安装Rust
    if command -v rustc >/dev/null 2>&1 && command -v cargo >/dev/null 2>&1; then
        local rust_version=$(rustc --version | cut -d' ' -f2)
        local cargo_version=$(cargo --version | cut -d' ' -f2)
        echo -e "${GREEN}✅ Rust环境已存在${NC}"
        echo -e "${GREEN}  - rustc: $rust_version${NC}"
        echo -e "${GREEN}  - cargo: $cargo_version${NC}"

        # 检查Rust版本是否足够新（至少1.70.0）
        local rust_major=$(echo "$rust_version" | cut -d'.' -f1)
        local rust_minor=$(echo "$rust_version" | cut -d'.' -f2)
        if [ "$rust_major" -gt 1 ] || ([ "$rust_major" -eq 1 ] && [ "$rust_minor" -ge 70 ]); then
            echo -e "${GREEN}✅ Rust版本满足要求，跳过安装${NC}"
            return 0
        else
            echo -e "${YELLOW}⚠️  Rust版本过旧 ($rust_version)，建议更新到1.70+${NC}"
            echo -e "${BLUE}🔄 尝试更新Rust...${NC}"
            if rustup update; then
                echo -e "${GREEN}✅ Rust更新成功${NC}"
                return 0
            else
                echo -e "${YELLOW}⚠️  Rust更新失败，继续使用当前版本${NC}"
                return 0
            fi
        fi
    fi

    echo -e "${YELLOW}⚠️  未检测到Rust环境，开始自动安装...${NC}"

    # 检查是否有网络连接
    if ! curl -s --connect-timeout 5 https://sh.rustup.rs >/dev/null; then
        echo -e "${RED}❌ 无法连接到Rust安装服务器${NC}"
        echo -e "${YELLOW}💡 请检查网络连接或手动安装Rust: https://rustup.rs/${NC}"
        return 1
    fi

    # 下载并安装Rust
    echo -e "${BLUE}📥 下载Rust安装脚本...${NC}"
    if curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y --default-toolchain stable; then
        echo -e "${WHITE}✅ Rust安装成功${NC}"

        # 重新加载环境变量
        source "$HOME/.cargo/env" 2>/dev/null || true
        export PATH="$HOME/.cargo/bin:$PATH"

        # 验证安装
        if command -v rustc >/dev/null 2>&1 && command -v cargo >/dev/null 2>&1; then
            local rust_version=$(rustc --version | cut -d' ' -f2)
            local cargo_version=$(cargo --version | cut -d' ' -f2)
            echo -e "${GREEN}✅ Rust验证成功${NC}"
            echo -e "${GREEN}  - rustc: $rust_version${NC}"
            echo -e "${GREEN}  - cargo: $cargo_version${NC}"
            return 0
        else
            echo -e "${RED}❌ Rust安装验证失败${NC}"
            echo -e "${YELLOW}💡 请手动安装Rust: https://rustup.rs/${NC}"
            return 1
        fi
    else
        echo -e "${RED}❌ Rust安装失败${NC}"
        echo -e "${YELLOW}💡 请手动安装Rust: https://rustup.rs/${NC}"
        return 1
    fi
}

# 智能检测服务器配置并优化编译参数
detect_server_config() {
    echo -e "${BLUE}🔍 智能检测服务器配置...${NC}"

    # 检测CPU核心数
    echo -e "${CYAN}  检测CPU核心数...${NC}"
    local cpu_cores=$(nproc 2>/dev/null || echo "1")
    echo -e "${CYAN}  CPU核心数: ${cpu_cores}${NC}"

    # 检测内存大小（MB）
    echo -e "${CYAN}  检测内存大小...${NC}"
    local memory_mb=$(free -m 2>/dev/null | awk 'NR==2{printf "%.0f", $2}' || echo "2048")
    local memory_gb=$((memory_mb / 1024))
    echo -e "${CYAN}  内存大小: ${memory_gb}GB (${memory_mb}MB)${NC}"

    # 检测硬盘空间（GB）
    echo -e "${CYAN}  检测硬盘空间...${NC}"
    local disk_gb=$(df -BG . 2>/dev/null | awk 'NR==2 {print $4}' | sed 's/G//' || echo "20")
    echo -e "${CYAN}  可用硬盘: ${disk_gb}GB${NC}"

    # 检测CPU架构
    local cpu_arch=$(uname -m)
    echo -e "${CYAN}  CPU架构: ${cpu_arch}${NC}"

    # 智能设置编译参数
    local build_jobs=1
    local rustflags="-C opt-level=2"
    local use_tmp_dir=false

    # 根据内存大小调整并行编译任务数
    if [ "$memory_gb" -ge 8 ]; then
        build_jobs=$((cpu_cores))
        rustflags="-C target-cpu=native -C opt-level=2 -C codegen-units=1"
        echo -e "${GREEN}  配置级别: 高性能 (8GB+内存)${NC}"
    elif [ "$memory_gb" -ge 4 ]; then
        build_jobs=$((cpu_cores > 2 ? 2 : cpu_cores))
        rustflags="-C target-cpu=native -C opt-level=2"
        echo -e "${GREEN}  配置级别: 优化 (4GB+内存)${NC}"
    elif [ "$memory_gb" -ge 2 ]; then
        build_jobs=2
        rustflags="-C target-cpu=native -C opt-level=1"
        echo -e "${YELLOW}  配置级别: 平衡 (2GB+内存)${NC}"
    else
        build_jobs=1
        rustflags="-C opt-level=1"
        echo -e "${YELLOW}  配置级别: 保守 (<2GB内存)${NC}"
    fi

    # 如果硬盘空间充足且内存较大，使用临时目录编译
    if [ "$disk_gb" -gt 20 ] && [ "$memory_gb" -ge 4 ]; then
        use_tmp_dir=true
        echo -e "${CYAN}  编译优化: 使用临时目录 (硬盘${disk_gb}GB)${NC}"
    fi

    # 设置环境变量
    export CARGO_BUILD_JOBS="$build_jobs"
    export RUSTFLAGS="$rustflags"

    # 智能选择编译目录
    if [ "$use_tmp_dir" = true ]; then
        export CARGO_TARGET_DIR="/tmp/sm_build_$$"
        echo -e "${CYAN}  临时编译目录: $CARGO_TARGET_DIR${NC}"
    elif [ -n "${DEPLOY_PATH:-}" ] && [ "$DEPLOY_PATH" != "$SCRIPT_DIR" ]; then
        # 如果是部署到系统目录，直接在部署目录编译
        export CARGO_TARGET_DIR="$DEPLOY_PATH/target"
        echo -e "${CYAN}  部署目录编译: $CARGO_TARGET_DIR${NC}"
    fi

    echo -e "${WHITE}✅ 编译配置优化完成:${NC}"
    echo -e "${WHITE}  - 并行任务数: ${CYAN}$build_jobs${NC}"
    echo -e "${WHITE}  - 编译标志: ${CYAN}$rustflags${NC}"
    echo -e "${WHITE}  - 临时目录: ${CYAN}$([ "$use_tmp_dir" = true ] && echo "启用" || echo "禁用")${NC}"
}

# 代码质量检查
perform_code_quality_check() {
    echo -e "${CYAN}  执行代码质量检查...${NC}"

    # 检查基本语法
    echo -e "${CYAN}    检查语法...${NC}"
    if cargo check --quiet 2>/dev/null; then
        echo -e "${GREEN}    ✅ 语法检查通过${NC}"
    else
        echo -e "${YELLOW}    ⚠️  语法检查发现问题${NC}"
        echo -e "${CYAN}    💡 运行 'cargo check' 查看详细信息${NC}"
        return 1
    fi

    # 检查是否有明显的编译错误
    echo -e "${CYAN}    检查编译错误...${NC}"
    local check_output=$(cargo check 2>&1)
    if echo "$check_output" | grep -q "error\["; then
        echo -e "${RED}    ❌ 发现编译错误${NC}"
        echo -e "${YELLOW}    💡 请修复编译错误后重试${NC}"
        return 1
    else
        echo -e "${GREEN}    ✅ 无编译错误${NC}"
    fi

    # 检查关键文件是否存在
    echo -e "${CYAN}    检查关键文件...${NC}"
    local critical_files=("src/main.rs" "src/types.rs" "src/error.rs" "Cargo.toml")
    for file in "${critical_files[@]}"; do
        if [ ! -f "$file" ]; then
            echo -e "${RED}    ❌ 关键文件缺失: $file${NC}"
            return 1
        fi
    done
    echo -e "${GREEN}    ✅ 关键文件完整${NC}"

    echo -e "${GREEN}  ✅ 代码质量检查完成${NC}"
    return 0
}

# 智能编译项目
smart_compile_project() {
    echo -e "${BLUE}🔨 智能编译项目...${NC}"

    cd "$SCRIPT_DIR"

    # 服务器配置已在步骤1检测，这里直接使用环境变量

    # 确保Rust环境可用
    if ! command -v cargo >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  Cargo未找到，尝试加载Rust环境...${NC}"
        source "$HOME/.cargo/env" 2>/dev/null || true
        export PATH="$HOME/.cargo/bin:$PATH"

        if ! command -v cargo >/dev/null 2>&1; then
            echo -e "${RED}❌ 无法找到Cargo，请确保Rust已正确安装${NC}"
            return 1
        fi
    fi

    # 检查项目文件
    if [ ! -f "Cargo.toml" ]; then
        echo -e "${RED}❌ 未找到Cargo.toml文件${NC}"
        return 1
    fi

    # 检查是否需要重新编译
    local need_rebuild=false
    local binary_path="target/release/${PROJECT_NAME}"

    if [ ! -f "$binary_path" ]; then
        echo -e "${YELLOW}⚠️  可执行文件不存在，需要编译${NC}"
        need_rebuild=true
    else
        echo -e "${BLUE}🔍 检查是否需要重新编译...${NC}"

        # 检查源代码是否比二进制文件新
        local binary_time=$(stat -c %Y "$binary_path" 2>/dev/null || echo 0)
        local newest_source_time=0

        # 查找最新的源文件
        while IFS= read -r -d '' file; do
            local file_time=$(stat -c %Y "$file" 2>/dev/null || echo 0)
            if [ "$file_time" -gt "$newest_source_time" ]; then
                newest_source_time=$file_time
            fi
        done < <(find src -name "*.rs" -print0 2>/dev/null)

        # 检查Cargo.toml是否更新
        local cargo_time=$(stat -c %Y "Cargo.toml" 2>/dev/null || echo 0)
        if [ "$cargo_time" -gt "$newest_source_time" ]; then
            newest_source_time=$cargo_time
        fi

        if [ "$newest_source_time" -gt "$binary_time" ]; then
            echo -e "${YELLOW}⚠️  源代码已更新，需要重新编译${NC}"
            need_rebuild=true
        else
            echo -e "${GREEN}✅ 二进制文件是最新的，跳过编译${NC}"
            return 0
        fi
    fi

    if [ "$need_rebuild" = true ]; then
        # 代码质量检查
        echo -e "${BLUE}🔍 代码质量检查...${NC}"
        if ! perform_code_quality_check; then
            echo -e "${YELLOW}⚠️  代码质量检查发现问题，但继续编译...${NC}"
        fi

        # 清理之前的编译结果
        echo -e "${BLUE}🧹 清理之前的编译结果...${NC}"
        cargo clean

        # 开始编译
        echo -e "${BLUE}⚙️  开始Release编译...${NC}"
        echo -e "${YELLOW}💡 这可能需要几分钟时间，请耐心等待...${NC}"

        if cargo build --release; then
            echo -e "${WHITE}✅ 项目编译成功${NC}"

            # 如果使用了临时目录，需要复制文件
            if [ -n "${CARGO_TARGET_DIR:-}" ] && [ "$CARGO_TARGET_DIR" != "target" ]; then
                echo -e "${BLUE}📁 从临时目录复制编译结果...${NC}"
                mkdir -p target/release
                if [ -f "$CARGO_TARGET_DIR/release/${PROJECT_NAME}" ]; then
                    cp "$CARGO_TARGET_DIR/release/${PROJECT_NAME}" target/release/
                    echo -e "${GREEN}✅ 编译结果已复制到标准目录${NC}"

                    # 清理临时目录
                    echo -e "${BLUE}🧹 清理临时编译目录...${NC}"
                    rm -rf "$CARGO_TARGET_DIR"
                    echo -e "${GREEN}✅ 临时目录已清理${NC}"
                else
                    echo -e "${RED}❌ 临时目录中未找到可执行文件${NC}"
                    return 1
                fi
            fi

            # 验证可执行文件
            if [ -f "$binary_path" ]; then
                local file_size=$(du -h "$binary_path" | cut -f1)
                echo -e "${GREEN}✅ 可执行文件生成成功: $binary_path (${file_size})${NC}"

                # 显示编译统计信息
                echo -e "${CYAN}📊 编译统计:${NC}"
                echo -e "${CYAN}  - 并行任务: ${CARGO_BUILD_JOBS:-1}${NC}"
                echo -e "${CYAN}  - 优化标志: ${RUSTFLAGS:-默认}${NC}"
                echo -e "${CYAN}  - 文件大小: ${file_size}${NC}"

                return 0
            else
                echo -e "${RED}❌ 可执行文件未生成${NC}"
                return 1
            fi
        else
            echo -e "${RED}❌ 项目编译失败${NC}"

            # 检查是否是cmake相关错误
            if cargo build --release 2>&1 | grep -q "cmake.*not installed"; then
                echo -e "${YELLOW}🔧 检测到cmake缺失，尝试自动安装...${NC}"

                # 检查cmake是否已安装
                if command -v cmake >/dev/null 2>&1; then
                    echo -e "${GREEN}✅ cmake已安装，跳过安装${NC}"
                else
                    echo -e "${BLUE}🔧 安装cmake...${NC}"
                    # 尝试安装cmake
                    if command -v apt >/dev/null 2>&1; then
                        sudo apt update && sudo apt install -y cmake
                    elif command -v yum >/dev/null 2>&1; then
                        sudo yum install -y cmake
                    elif command -v dnf >/dev/null 2>&1; then
                        sudo dnf install -y cmake
                    elif command -v pacman >/dev/null 2>&1; then
                        sudo pacman -S --noconfirm cmake
                    fi
                fi

                # 验证cmake安装
                if command -v cmake >/dev/null 2>&1; then
                    echo -e "${GREEN}✅ cmake安装成功，重新尝试编译...${NC}"
                    if cargo build --release; then
                        echo -e "${WHITE}✅ 项目编译成功${NC}"
                        return 0
                    fi
                fi
            fi

            echo -e "${YELLOW}💡 请检查编译错误信息并修复后重试${NC}"
            echo -e "${YELLOW}💡 常见问题解决方案：${NC}"
            echo -e "${YELLOW}   - 缺少cmake: sudo apt install cmake${NC}"
            echo -e "${YELLOW}   - 内存不足: 减少CARGO_BUILD_JOBS${NC}"
            echo -e "${YELLOW}   - 网络问题: 检查网络连接${NC}"

            # 清理临时目录（如果存在）
            if [ -n "${CARGO_TARGET_DIR:-}" ] && [ "$CARGO_TARGET_DIR" != "target" ]; then
                echo -e "${BLUE}🧹 清理临时编译目录...${NC}"
                rm -rf "$CARGO_TARGET_DIR"
            fi

            return 1
        fi
    fi
}

# 检查编译结果是否存在（增强版）
check_compiled_binary() {
    echo -e "${BLUE}🔍 检查编译结果...${NC}"

    cd "$SCRIPT_DIR"

    # 检查是否有编译好的可执行文件
    if [ ! -f "target/release/${PROJECT_NAME}" ]; then
        echo -e "${YELLOW}⚠️  未找到编译好的可执行文件${NC}"
        echo -e "${BLUE}🤖 尝试自动编译...${NC}"

        # 检查并安装Rust环境
        if ! install_rust_environment; then
            return 1
        fi

        # 自动编译项目
        if smart_compile_project; then
            echo -e "${GREEN}✅ 自动编译完成${NC}"
        else
            echo -e "${RED}❌ 自动编译失败${NC}"
            echo -e "${YELLOW}💡 请手动编译项目：${NC}"
            echo -e "${YELLOW}   cargo build --release${NC}"
            echo -e "${YELLOW}   然后再运行此部署脚本${NC}"
            return 1
        fi
    else
        echo -e "${GREEN}✅ 发现编译好的可执行文件${NC}"
    fi

    return 0
}

# 设置环境变量
setup_environment_variables() {
    local deploy_path="$1"

    echo -e "${BLUE}🔧 设置环境变量...${NC}"

    # 生成JWT密钥
    local jwt_secret=$(openssl rand -base64 64 | tr -d '\n')
    echo -e "${CYAN}  生成JWT密钥...${NC}"

    # 生成管理员密码哈希 (admin888)
    echo -e "${CYAN}  设置管理员密码哈希...${NC}"
    local admin_password_hash

    # 尝试使用Python生成bcrypt哈希
    if command -v python3 >/dev/null 2>&1; then
        echo -e "${BLUE}    尝试使用Python3生成密码哈希...${NC}"
        if python3 -c "import bcrypt" 2>/dev/null; then
            admin_password_hash=$(python3 -c "import bcrypt; print(bcrypt.hashpw(b'admin888', bcrypt.gensalt()).decode())" 2>/dev/null)
            echo -e "${GREEN}    ✅ 使用Python3生成密码哈希成功${NC}"
        else
            echo -e "${YELLOW}    ⚠️  Python3 bcrypt模块未安装，尝试安装...${NC}"
            if pip3 install bcrypt >/dev/null 2>&1; then
                admin_password_hash=$(python3 -c "import bcrypt; print(bcrypt.hashpw(b'admin888', bcrypt.gensalt()).decode())" 2>/dev/null)
                echo -e "${GREEN}    ✅ 安装bcrypt并生成密码哈希成功${NC}"
            else
                echo -e "${YELLOW}    ⚠️  无法安装bcrypt，使用预生成哈希${NC}"
                admin_password_hash='$2b$12$rQZ8kHp.XvM5Q1YvN2nOUeK6tX9sL4wP3mR7jF8vC2nA1bE5dG6hS'
            fi
        fi
    else
        echo -e "${YELLOW}    ⚠️  Python3未安装，使用预生成哈希${NC}"
        admin_password_hash='$2b$12$rQZ8kHp.XvM5Q1YvN2nOUeK6tX9sL4wP3mR7jF8vC2nA1bE5dG6hS'
    fi

    # MongoDB连接串
    local mongodb_uri="mongodb://localhost:27017/sm"
    echo -e "${CYAN}  配置MongoDB连接...${NC}"

    # 创建环境变量文件
    cat > "${deploy_path}/.env" << EOF
# SM智能代理系统环境变量
# 自动生成于: $(date)

# JWT配置
JWT_SECRET=${jwt_secret}

# 数据库配置
MONGODB_URI=${mongodb_uri}
REDIS_URI=redis://localhost:6379/0

# 管理员配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD_HASH=${admin_password_hash}

# 日志级别
LOG_LEVEL=info

# 环境标识
ENVIRONMENT=development
EOF

    # 设置环境变量文件权限
    chmod 600 "${deploy_path}/.env"

    # 确保服务用户拥有环境变量文件
    if [ "$(whoami)" = "root" ]; then
        chown "$SERVICE_USER:$SERVICE_USER" "${deploy_path}/.env"
    fi

    echo -e "${GREEN}✅ 环境变量配置完成${NC}"
    echo -e "${CYAN}  管理员账户: admin${NC}"
    echo -e "${CYAN}  管理员密码: admin888${NC}"
    echo -e "${CYAN}  环境变量文件: ${deploy_path}/.env${NC}"
}

# 智能配置文件管理
setup_config_files() {
    local deploy_path="$1"

    echo -e "${BLUE}⚙️  智能配置文件管理...${NC}"

    # 确保配置目录存在
    mkdir -p "$deploy_path/config"

    # 检查是否存在简化配置文件
    local simple_config="$SCRIPT_DIR/config.simple.yaml"
    local target_config="$deploy_path/config/config.yaml"

    if [ -f "$simple_config" ]; then
        echo -e "${GREEN}✅ 发现简化配置文件: config.simple.yaml${NC}"

        # 如果目标配置不存在或者简化配置更新，则使用简化配置
        if [ ! -f "$target_config" ] || [ "$simple_config" -nt "$target_config" ]; then
            echo -e "${BLUE}📋 使用简化配置文件...${NC}"
            cp "$simple_config" "$target_config"
            echo -e "${GREEN}✅ 简化配置已部署: $target_config${NC}"
        else
            echo -e "${CYAN}💡 现有配置文件较新，保持不变${NC}"
        fi
    elif [ -f "$SCRIPT_DIR/config/config.yaml" ]; then
        echo -e "${BLUE}📋 使用标准配置文件...${NC}"
        cp "$SCRIPT_DIR/config/config.yaml" "$target_config"
        echo -e "${GREEN}✅ 标准配置已部署: $target_config${NC}"
    else
        echo -e "${YELLOW}⚠️  未找到配置文件，创建默认配置...${NC}"
        create_default_config "$deploy_path"
    fi

    # 设置配置文件权限
    if [ -f "$target_config" ]; then
        chmod 644 "$target_config"
        if [ "$(whoami)" = "root" ]; then
            chown "$SERVICE_USER:$SERVICE_USER" "$target_config"
        fi
        echo -e "${GREEN}✅ 配置文件权限设置完成${NC}"
    fi
}

# 创建默认配置文件
create_default_config() {
    local deploy_path="$1"
    local config_file="$deploy_path/config/config.yaml"

    echo -e "${BLUE}📝 创建默认配置文件...${NC}"

    cat > "$config_file" << 'EOF'
# SM代理系统 - 默认配置文件
# 自动生成的简化配置

# 服务器配置
server:
  web_host: "127.0.0.1"      # Web管理界面地址
  web_port: 1319             # Web管理界面端口
  proxy_host: "127.0.0.1"    # 代理服务地址
  proxy_port: 1911           # 代理服务端口
  workers: 2                 # 工作线程数

# 数据库配置
database:
  mongodb_uri: "mongodb://localhost:27017/sm"
  redis_uri: "redis://localhost:6379/0"
  connection_timeout: 30     # 连接超时（秒）
  max_connections: 100       # 最大连接数

# 安全配置
security:
  jwt_secret: "change-me-in-production"
  admin_username: "admin"
  admin_password_hash: "change-me-in-production"
  session_timeout: 1800      # 会话超时（秒）
  max_login_attempts: 3      # 最大登录尝试次数
  lockout_duration: 3600     # 锁定时长（秒）
  rate_limit_enabled: true   # 是否启用速率限制
  rate_limit_per_minute: 100 # 每分钟请求限制

# 日志配置
logging:
  level: "info"
  file_path: "./logs/app.log"
  max_size_mb: 100           # 日志文件最大大小（MB）
  max_files: 10              # 保留的日志文件数量
  compress: true             # 是否压缩旧日志

# 缓存配置
cache:
  max_size_mb: 256           # 缓存最大大小（MB）
  default_ttl_seconds: 1800  # 默认TTL（秒）
  cleanup_interval_seconds: 300  # 清理间隔（秒）
EOF

    echo -e "${GREEN}✅ 默认配置文件已创建${NC}"
}

# 验证配置文件
validate_config_file() {
    local deploy_path="$1"
    local config_file="$deploy_path/config/config.yaml"

    echo -e "${BLUE}🔍 验证配置文件...${NC}"

    # 检查配置文件是否存在
    if [ ! -f "$config_file" ]; then
        echo -e "${RED}❌ 配置文件不存在: $config_file${NC}"
        return 1
    fi

    # 检查新的简化配置项
    echo -e "${CYAN}  检查配置项...${NC}"
    local required_configs=(
        "server:"
        "database:"
        "security:"
        "logging:"
    )

    for config in "${required_configs[@]}"; do
        if ! grep -q "^${config}" "$config_file"; then
            echo -e "${YELLOW}⚠️  缺少配置节: $config${NC}"
            # 对于新的简化配置，这不是致命错误
        else
            echo -e "${GREEN}    ✅ $config${NC}"
        fi
    done

    # 检查是否是旧格式配置，如果是则给出提示
    if grep -q "frontend_addr\|backend_addr\|upstream_servers" "$config_file"; then
        echo -e "${YELLOW}⚠️  检测到旧格式配置文件${NC}"
        echo -e "${CYAN}💡 建议使用新的简化配置格式${NC}"
        echo -e "${CYAN}💡 新配置文件: config.simple.yaml${NC}"
    fi

    echo -e "${GREEN}✅ 配置文件验证通过${NC}"
    return 0
}

# 预启动检查
pre_startup_check() {
    local deploy_path="$1"

    echo -e "${BLUE}🔍 预启动检查...${NC}"

    # 检查可执行文件
    echo -e "${CYAN}  检查可执行文件...${NC}"

    # 就地部署时检查target/release目录
    if [ "$deploy_path" = "$SCRIPT_DIR" ]; then
        if [ ! -f "$deploy_path/target/release/${PROJECT_NAME}" ]; then
            echo -e "${RED}❌ 可执行文件不存在: $deploy_path/target/release/${PROJECT_NAME}${NC}"
            return 1
        fi

        if [ ! -x "$deploy_path/target/release/${PROJECT_NAME}" ]; then
            echo -e "${RED}❌ 可执行文件没有执行权限${NC}"
            return 1
        fi
        echo -e "${GREEN}  ✅ 可执行文件检查通过: target/release/${PROJECT_NAME}${NC}"
    else
        if [ ! -f "$deploy_path/${PROJECT_NAME}" ]; then
            echo -e "${RED}❌ 可执行文件不存在: $deploy_path/${PROJECT_NAME}${NC}"
            return 1
        fi

        if [ ! -x "$deploy_path/${PROJECT_NAME}" ]; then
            echo -e "${RED}❌ 可执行文件没有执行权限${NC}"
            return 1
        fi
        echo -e "${GREEN}  ✅ 可执行文件检查通过: ${PROJECT_NAME}${NC}"
    fi

    # 检查环境变量文件
    echo -e "${CYAN}  检查环境变量文件...${NC}"
    if [ ! -f "$deploy_path/.env" ]; then
        echo -e "${RED}❌ 环境变量文件不存在: $deploy_path/.env${NC}"
        return 1
    fi
    echo -e "${GREEN}  ✅ 环境变量文件存在${NC}"

    # 验证配置文件
    if ! validate_config_file "$deploy_path"; then
        return 1
    fi

    # 检查必需目录
    echo -e "${CYAN}  检查必需目录...${NC}"
    local required_dirs=("keys" "logs" "data")
    for dir in "${required_dirs[@]}"; do
        if [ ! -d "$deploy_path/$dir" ]; then
            echo -e "${RED}❌ 必需目录不存在: $deploy_path/$dir${NC}"
            return 1
        fi

        if [ ! -w "$deploy_path/$dir" ]; then
            echo -e "${RED}❌ 目录不可写: $deploy_path/$dir${NC}"
            return 1
        fi
        echo -e "${GREEN}    ✅ $dir${NC}"
    done

    # 检查MongoDB连接
    echo -e "${CYAN}  检查MongoDB连接...${NC}"
    if systemctl is-active mongod >/dev/null 2>&1 || systemctl is-active mongodb >/dev/null 2>&1; then
        echo -e "${GREEN}  ✅ MongoDB服务运行中${NC}"
    else
        echo -e "${RED}❌ MongoDB服务未运行${NC}"
        return 1
    fi

    echo -e "${GREEN}✅ 预启动检查通过${NC}"
    return 0
}

# 创建服务用户
create_service_user() {
    local deploy_path="$1"

    echo -e "${BLUE}👤 处理服务用户...${NC}"

    if ! id "$SERVICE_USER" &>/dev/null; then
        echo -e "${YELLOW}🔧 创建服务用户: $SERVICE_USER${NC}"
        if [ "$(whoami)" = "root" ]; then
            useradd -r -s /bin/false -d "$deploy_path" -c "Proxy Manager Service" "$SERVICE_USER"
            echo -e "${GREEN}✅ 服务用户已创建${NC}"
        else
            echo -e "${YELLOW}⚠️  非root用户，无法创建系统用户。将使用当前用户运行服务${NC}"
            SERVICE_USER="$(whoami)"
        fi
    else
        echo -e "${GREEN}✅ 服务用户已存在: $SERVICE_USER${NC}"
    fi
}

# 智能部署文件
deploy_files() {
    local deploy_path="$1"

    echo -e "${BLUE}📋 智能部署文件...${NC}"

    if [ "$deploy_path" = "$SCRIPT_DIR" ]; then
        # 就地部署：在源码目录直接运行
        echo -e "${GREEN}🏠 就地部署模式：在源码目录运行${NC}"
        echo -e "${BLUE}  - 部署路径: $deploy_path${NC}"

        # 确保可执行文件存在
        if [ ! -f "target/release/${PROJECT_NAME}" ]; then
            echo -e "${RED}❌ 可执行文件不存在: target/release/${PROJECT_NAME}${NC}"
            return 1
        fi

        # 创建必要的运行时目录
        echo -e "${BLUE}📁 创建运行时目录...${NC}"
        for dir in data logs; do
            mkdir -p "$dir"
            echo -e "${GREEN}✅ 目录: $deploy_path/$dir${NC}"
        done

        # 确保config目录存在
        if [ ! -d "config" ]; then
            mkdir -p config
            echo -e "${GREEN}✅ 创建配置目录: $deploy_path/config${NC}"
        fi

        echo -e "${WHITE}✅ 就地部署完成${NC}"

    else
        # 检查是否使用符号链接部署
        if [ "${USE_SYMLINK_DEPLOY:-false}" = "true" ]; then
            # 符号链接部署：轻量级，避免复制
            echo -e "${GREEN}🔗 符号链接部署模式：避免文件复制${NC}"
            echo -e "${BLUE}  - 源码路径: $SCRIPT_DIR${NC}"
            echo -e "${BLUE}  - 目标路径: $deploy_path${NC}"

            # 创建部署目录
            mkdir -p "$deploy_path"

            # 创建符号链接
            ln -sf "$SCRIPT_DIR/target/release/${PROJECT_NAME}" "$deploy_path/${PROJECT_NAME}"
            ln -sf "$SCRIPT_DIR/config" "$deploy_path/config"
            ln -sf "$SCRIPT_DIR/frontend" "$deploy_path/frontend"

            # 创建独立的运行时目录
            mkdir -p "$deploy_path"/{logs,data,keys,temp,backup}

            echo -e "${GREEN}✅ 符号链接部署完成${NC}"
            echo -e "${CYAN}💡 优势: 无需复制文件，自动同步更新${NC}"

        else
            # 复制部署：复制到系统目录
            echo -e "${GREEN}📦 复制部署模式：复制到系统目录${NC}"
            echo -e "${BLUE}  - 源码路径: $SCRIPT_DIR${NC}"
            echo -e "${BLUE}  - 目标路径: $deploy_path${NC}"

        # 创建部署目录
        if [ "$(whoami)" = "root" ]; then
            mkdir -p "$deploy_path"
            chown "$USER:$USER" "$deploy_path"
        else
            mkdir -p "$deploy_path"
        fi

        # 智能复制主要文件
        echo -e "${BLUE}📄 智能复制应用文件...${NC}"

        # 智能检查目标文件状态
        if [ -e "$deploy_path/${PROJECT_NAME}" ]; then
            if [ -d "$deploy_path/${PROJECT_NAME}" ]; then
                echo -e "${YELLOW}⚠️  检测到同名目录，清理冲突...${NC}"
                # 如果是目录，先备份再删除
                if [ "$(ls -A "$deploy_path/${PROJECT_NAME}" 2>/dev/null)" ]; then
                    echo -e "${BLUE}💾 备份目录内容...${NC}"
                    mv "$deploy_path/${PROJECT_NAME}" "$deploy_path/${PROJECT_NAME}.backup.dir.$(date +%Y%m%d_%H%M%S)" 2>/dev/null || true
                else
                    echo -e "${BLUE}🗑️ 删除空目录...${NC}"
                    rm -rf "$deploy_path/${PROJECT_NAME}"
                fi
            elif [ -f "$deploy_path/${PROJECT_NAME}" ]; then
                echo -e "${YELLOW}⚠️  检测到现有可执行文件，检查是否被占用...${NC}"

                # 检查服务是否正在运行
                if systemctl is-active "$PROJECT_NAME" >/dev/null 2>&1; then
                    echo -e "${BLUE}🔄 服务正在运行，先停止服务...${NC}"
                    systemctl stop "$PROJECT_NAME"
                    sleep 2
                    echo -e "${GREEN}✅ 服务已停止${NC}"
                fi

                # 检查是否还有进程在使用文件
                if lsof "$deploy_path/${PROJECT_NAME}" >/dev/null 2>&1; then
                    echo -e "${YELLOW}⚠️  文件仍被占用，强制终止相关进程...${NC}"
                    pkill -f "$deploy_path/${PROJECT_NAME}" 2>/dev/null || true
                    sleep 2
                fi

                # 备份旧文件
                echo -e "${BLUE}💾 备份旧版本...${NC}"
                mv "$deploy_path/${PROJECT_NAME}" "$deploy_path/${PROJECT_NAME}.backup.$(date +%Y%m%d_%H%M%S)" 2>/dev/null || true
            else
                echo -e "${YELLOW}⚠️  检测到特殊文件类型，清理...${NC}"
                rm -f "$deploy_path/${PROJECT_NAME}" 2>/dev/null || true
            fi
        fi

        # 复制新文件
        echo -e "${BLUE}📋 复制可执行文件: target/release/${PROJECT_NAME} -> $deploy_path/${PROJECT_NAME}${NC}"

        # 检查源文件
        if [ ! -f "target/release/${PROJECT_NAME}" ]; then
            echo -e "${RED}❌ 源文件不存在: target/release/${PROJECT_NAME}${NC}"
            return 1
        fi

        # 显示源文件信息
        echo -e "${CYAN}  源文件信息: $(ls -lh target/release/${PROJECT_NAME})${NC}"

        # 执行复制
        if cp "target/release/${PROJECT_NAME}" "$deploy_path/"; then
            # 验证复制结果
            if [ -f "$deploy_path/${PROJECT_NAME}" ]; then
                echo -e "${WHITE}✅ 可执行文件: $deploy_path/${PROJECT_NAME}${NC}"
                echo -e "${CYAN}  目标文件信息: $(ls -lh $deploy_path/${PROJECT_NAME})${NC}"

                # 设置正确权限
                chmod 755 "$deploy_path/${PROJECT_NAME}"
                if [ "$(whoami)" = "root" ]; then
                    chown "$SERVICE_USER:$SERVICE_USER" "$deploy_path/${PROJECT_NAME}"
                fi
            else
                echo -e "${RED}❌ 复制后文件不存在${NC}"
                return 1
            fi
        else
            echo -e "${RED}❌ 复制可执行文件失败${NC}"

            # 尝试恢复备份
            local backup_file=$(ls -t "$deploy_path/${PROJECT_NAME}.backup."* 2>/dev/null | grep -v ".dir." | head -1)
            if [ -n "$backup_file" ] && [ -f "$backup_file" ]; then
                echo -e "${BLUE}🔄 恢复备份文件...${NC}"
                # 确保目标位置没有冲突
                rm -rf "$deploy_path/${PROJECT_NAME}" 2>/dev/null || true
                mv "$backup_file" "$deploy_path/${PROJECT_NAME}"
                echo -e "${YELLOW}⚠️  已恢复到备份版本${NC}"
            else
                echo -e "${RED}❌ 未找到有效的备份文件${NC}"
            fi
            return 1
        fi

        # 复制配置和前端文件
        if [ -d "config" ]; then
            cp -r config "$deploy_path/"
            echo -e "${GREEN}✅ 配置文件: $deploy_path/config/${NC}"
        fi

        if [ -d "frontend" ]; then
            cp -r frontend "$deploy_path/"
            echo -e "${GREEN}✅ 前端文件: $deploy_path/frontend/${NC}"
        fi

        if [ -f "security.conf" ]; then
            cp security.conf "$deploy_path/"
            echo -e "${GREEN}✅ 安全配置: $deploy_path/security.conf${NC}"
        fi

        # 创建必要目录
        echo -e "${BLUE}📁 创建运行时目录...${NC}"
        for dir in data logs; do
            mkdir -p "$deploy_path/$dir"
            echo -e "${GREEN}✅ 目录: $deploy_path/$dir${NC}"
        done

        # 创建到源码目录的反向链接（方便开发）
        if [ -w "$SCRIPT_DIR" ]; then
            ln -sf "$deploy_path" "$SCRIPT_DIR/deployment_link" 2>/dev/null || true
            echo -e "${GREEN}✅ 部署链接: $SCRIPT_DIR/deployment_link -> $deploy_path${NC}"
        fi

        echo -e "${WHITE}✅ 复制部署完成${NC}"

            # 如果之前停止了服务，标记需要重启
            if ! systemctl is-active "$PROJECT_NAME" >/dev/null 2>&1; then
                echo -e "${BLUE}💡 服务将在配置完成后自动重启${NC}"
            fi
        fi
    fi
}

# 设置安全权限
setup_security_permissions() {
    local deploy_path="$1"

    echo -e "${BLUE}🔒 设置安全权限...${NC}"

    # 创建所有必需的目录
    echo -e "${CYAN}  创建必需目录...${NC}"
    mkdir -p "$deploy_path"/{keys,logs,data,temp,backup}

    # 设置可执行文件权限
    if [ -f "$deploy_path/${PROJECT_NAME}" ]; then
        chmod 755 "$deploy_path/${PROJECT_NAME}"
        echo -e "${CYAN}  ✅ 可执行文件权限: 755${NC}"
    fi

    # 设置配置文件权限
    if [ -f "$deploy_path/config/config.yaml" ]; then
        chmod 644 "$deploy_path/config/config.yaml"
        echo -e "${CYAN}  ✅ 配置文件权限: 644${NC}"
    fi

    # 设置目录权限
    echo -e "${CYAN}  设置目录权限...${NC}"
    chmod 755 "$deploy_path"                    # 主目录
    chmod 755 "$deploy_path/config"             # 配置目录
    chmod 755 "$deploy_path/frontend"           # 前端目录
    chmod 700 "$deploy_path/keys"               # 密钥目录（严格权限）
    chmod 755 "$deploy_path/logs"               # 日志目录
    chmod 755 "$deploy_path/data"               # 数据目录
    chmod 700 "$deploy_path/temp"               # 临时目录（严格权限）
    chmod 700 "$deploy_path/backup"             # 备份目录（严格权限）

    # 保护前端文件
    if [ -d "$deploy_path/frontend" ]; then
        chmod -R 644 "$deploy_path/frontend"
        find "$deploy_path/frontend" -type d -exec chmod 755 {} \;
        echo -e "${CYAN}  ✅ 前端文件权限设置完成${NC}"
    fi

    # 设置所有权
    if [ "$(whoami)" = "root" ]; then
        chown -R "$SERVICE_USER:$SERVICE_USER" "$deploy_path"
        echo -e "${GREEN}✅ 所有权已设置为: $SERVICE_USER${NC}"

        # 验证关键目录权限
        echo -e "${CYAN}  验证关键目录权限...${NC}"
        if [ ! -w "$deploy_path/keys" ]; then
            echo -e "${RED}❌ 密钥目录权限验证失败${NC}"
            return 1
        fi

        if [ ! -w "$deploy_path/logs" ]; then
            echo -e "${RED}❌ 日志目录权限验证失败${NC}"
            return 1
        fi

        echo -e "${GREEN}✅ 权限验证通过${NC}"
    fi

    echo -e "${GREEN}✅ 安全权限设置完成${NC}"
    echo -e "${WHITE}📋 权限设置详情:${NC}"
    echo -e "${WHITE}  • 主目录: $deploy_path (755, $SERVICE_USER:$SERVICE_USER)${NC}"
    echo -e "${WHITE}  • 密钥目录: $deploy_path/keys (700, $SERVICE_USER:$SERVICE_USER)${NC}"
    echo -e "${WHITE}  • 日志目录: $deploy_path/logs (755, $SERVICE_USER:$SERVICE_USER)${NC}"
    echo -e "${WHITE}  • 配置文件: $deploy_path/config/config.yaml (644, $SERVICE_USER:$SERVICE_USER)${NC}"
}

# 创建Linux systemd服务文件
create_systemd_service() {
    local deploy_path="$1"
    local distro=$(detect_linux_distro)

    echo -e "${BLUE}⚙️  智能配置systemd服务...${NC}"
    echo -e "${BLUE}📋 检测到Linux发行版: $distro${NC}"

    # 检查服务是否已存在并正常运行
    local service_needs_recreation=false

    if [ -f "/etc/systemd/system/${PROJECT_NAME}.service" ]; then
        echo -e "${BLUE}🔍 检查现有服务配置...${NC}"

        # 验证服务文件是否有效
        if ! systemd-analyze verify "/etc/systemd/system/${PROJECT_NAME}.service" >/dev/null 2>&1; then
            echo -e "${YELLOW}⚠️  服务文件配置无效，需要重新创建${NC}"
            service_needs_recreation=true
        elif systemctl is-enabled "$PROJECT_NAME" >/dev/null 2>&1; then
            echo -e "${GREEN}✅ systemd服务已存在且已启用${NC}"

            # 检查服务是否正在运行
            if systemctl is-active "$PROJECT_NAME" >/dev/null 2>&1; then
                echo -e "${GREEN}✅ 服务正在运行，跳过服务创建${NC}"

                # 检查监控脚本
                if [ ! -f "$deploy_path/monitor.sh" ]; then
                    echo -e "${YELLOW}⚠️  监控脚本不存在，创建监控脚本...${NC}"
                    create_service_monitor "$deploy_path"
                else
                    echo -e "${GREEN}✅ 监控脚本已存在，跳过创建${NC}"
                fi
                return 0
            else
                echo -e "${YELLOW}⚠️  服务已配置但未运行，尝试启动...${NC}"
                if [ "$(whoami)" = "root" ]; then
                    systemctl start "$PROJECT_NAME" 2>/dev/null || true
                    sleep 3

                    if systemctl is-active "$PROJECT_NAME" >/dev/null 2>&1; then
                        echo -e "${GREEN}✅ 服务启动成功${NC}"
                        create_service_monitor "$deploy_path"
                        return 0
                    else
                        echo -e "${YELLOW}⚠️  服务启动失败，重新创建服务配置${NC}"
                        service_needs_recreation=true
                    fi
                fi
            fi
        else
            echo -e "${YELLOW}⚠️  服务文件存在但未启用，重新配置...${NC}"
            service_needs_recreation=true
        fi
    else
        echo -e "${YELLOW}⚠️  systemd服务不存在，创建新服务...${NC}"
        service_needs_recreation=true
    fi

    # 如果需要重新创建服务，先清理旧配置
    if [ "$service_needs_recreation" = true ] && [ "$(whoami)" = "root" ]; then
        echo -e "${BLUE}🧹 清理旧服务配置...${NC}"
        systemctl stop "$PROJECT_NAME" 2>/dev/null || true
        systemctl disable "$PROJECT_NAME" 2>/dev/null || true
        rm -f "/etc/systemd/system/${PROJECT_NAME}.service"
        systemctl daemon-reload
        echo -e "${GREEN}✅ 旧配置已清理${NC}"
    fi

    # 确保部署路径和可执行文件存在
    echo -e "${BLUE}🔍 验证可执行文件: ${deploy_path}/${PROJECT_NAME}${NC}"

    # 详细检查文件状态
    if [ -e "${deploy_path}/${PROJECT_NAME}" ]; then
        echo -e "${BLUE}📋 文件状态检查:${NC}"
        ls -la "${deploy_path}/${PROJECT_NAME}" || true
        file "${deploy_path}/${PROJECT_NAME}" 2>/dev/null || true

        if [ -d "${deploy_path}/${PROJECT_NAME}" ]; then
            echo -e "${YELLOW}⚠️  发现同名目录，清理并重新复制...${NC}"
            rm -rf "${deploy_path}/${PROJECT_NAME}"

            # 从源码目录重新复制
            if [ -f "target/release/${PROJECT_NAME}" ]; then
                cp "target/release/${PROJECT_NAME}" "${deploy_path}/"
                chmod +x "${deploy_path}/${PROJECT_NAME}"
                chown "$SERVICE_USER:$SERVICE_USER" "${deploy_path}/${PROJECT_NAME}" 2>/dev/null || true
                echo -e "${GREEN}✅ 重新复制完成${NC}"
            else
                echo -e "${RED}❌ 源码目录中的可执行文件不存在${NC}"
                return 1
            fi
        elif [ -f "${deploy_path}/${PROJECT_NAME}" ]; then
            echo -e "${GREEN}✅ 可执行文件存在${NC}"

            # 检查并修复权限
            if [ ! -x "${deploy_path}/${PROJECT_NAME}" ]; then
                echo -e "${BLUE}🔧 修复执行权限...${NC}"
                chmod +x "${deploy_path}/${PROJECT_NAME}"
            fi

            # 确保正确的所有权
            chown "$SERVICE_USER:$SERVICE_USER" "${deploy_path}/${PROJECT_NAME}" 2>/dev/null || true
        else
            echo -e "${YELLOW}⚠️  文件类型异常，重新创建...${NC}"
            rm -f "${deploy_path}/${PROJECT_NAME}" 2>/dev/null || true

            # 从源码目录重新复制
            if [ -f "target/release/${PROJECT_NAME}" ]; then
                cp "target/release/${PROJECT_NAME}" "${deploy_path}/"
                chmod +x "${deploy_path}/${PROJECT_NAME}"
                chown "$SERVICE_USER:$SERVICE_USER" "${deploy_path}/${PROJECT_NAME}" 2>/dev/null || true
                echo -e "${GREEN}✅ 重新创建完成${NC}"
            else
                echo -e "${RED}❌ 源码目录中的可执行文件不存在${NC}"
                return 1
            fi
        fi
    else
        echo -e "${YELLOW}⚠️  可执行文件不存在，从源码目录复制...${NC}"

        # 从源码目录复制
        if [ -f "target/release/${PROJECT_NAME}" ]; then
            cp "target/release/${PROJECT_NAME}" "${deploy_path}/"
            chmod +x "${deploy_path}/${PROJECT_NAME}"
            chown "$SERVICE_USER:$SERVICE_USER" "${deploy_path}/${PROJECT_NAME}" 2>/dev/null || true
            echo -e "${GREEN}✅ 复制完成${NC}"
        else
            echo -e "${RED}❌ 源码目录中的可执行文件不存在: target/release/${PROJECT_NAME}${NC}"
            return 1
        fi
    fi

    # 最终验证
    if [ -f "${deploy_path}/${PROJECT_NAME}" ] && [ -x "${deploy_path}/${PROJECT_NAME}" ]; then
        echo -e "${WHITE}✅ 可执行文件验证通过: ${deploy_path}/${PROJECT_NAME}${NC}"
        ls -la "${deploy_path}/${PROJECT_NAME}"
    else
        echo -e "${RED}❌ 最终验证失败${NC}"
        ls -la "${deploy_path}/${PROJECT_NAME}" 2>/dev/null || echo "文件不存在"
        return 1
    fi

    # 确保服务用户存在
    if ! id "$SERVICE_USER" >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  服务用户 $SERVICE_USER 不存在，使用当前用户${NC}"
        SERVICE_USER=$(whoami)
    fi

    echo -e "${BLUE}🔧 生成服务配置...${NC}"
    echo -e "${BLUE}  - 部署路径: ${deploy_path}${NC}"

    # 确定可执行文件路径
    local exec_path
    if [ "$deploy_path" = "$SCRIPT_DIR" ]; then
        exec_path="${deploy_path}/target/release/${PROJECT_NAME}"
        echo -e "${BLUE}  - 可执行文件: ${exec_path} (就地部署)${NC}"
    else
        exec_path="${deploy_path}/${PROJECT_NAME}"
        echo -e "${BLUE}  - 可执行文件: ${exec_path}${NC}"
    fi
    echo -e "${BLUE}  - 服务用户: ${SERVICE_USER}${NC}"

    # 动态生成高可用服务文件
    cat > "/tmp/${PROJECT_NAME}.service" << EOF
[Unit]
Description=SM - 智能代理服务 (基于Pingora的高性能反向代理)
Documentation=https://github.com/your-repo/sm
After=network-online.target mongod.service
Wants=network-online.target
Requires=mongod.service
RequiresMountsFor=${deploy_path}
# 双端口服务: Web管理界面(1319) + Pingora代理(1911)

[Service]
Type=simple
User=${SERVICE_USER}
Group=${SERVICE_USER}
WorkingDirectory=${deploy_path}
ExecStart=${exec_path}
ExecReload=/bin/kill -HUP \$MAINPID

# 故障自动重启配置
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# 进程管理
KillMode=mixed
TimeoutStartSec=30
TimeoutStopSec=15

# 看门狗配置 - 暂时禁用，避免兼容性问题
# WatchdogSec=30

# 基本安全设置（兼容性优先）
NoNewPrivileges=yes
PrivateTmp=yes

# 环境变量
Environment=RUST_LOG=info
Environment=RUST_BACKTRACE=1
Environment=ENVIRONMENT=production
EnvironmentFile=-${deploy_path}/.env

# 资源限制 (兼容性配置)
LimitNOFILE=65536

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=${PROJECT_NAME}

[Install]
WantedBy=multi-user.target
Also=network-online.target
EOF

    # 安装服务文件并配置高可用
    if [ "$(whoami)" = "root" ]; then
        echo -e "${BLUE}📦 安装服务文件...${NC}"
        mv "/tmp/${PROJECT_NAME}.service" "/etc/systemd/system/"
        systemctl daemon-reload

        # 启用服务（开机自动启动）
        echo -e "${BLUE}🔧 启用服务...${NC}"
        systemctl enable "$PROJECT_NAME"

        # 验证服务文件
        if systemd-analyze verify "/etc/systemd/system/${PROJECT_NAME}.service" >/dev/null 2>&1; then
            echo -e "${GREEN}✅ 服务文件验证通过${NC}"
        else
            echo -e "${RED}❌ 服务文件验证失败${NC}"
            systemd-analyze verify "/etc/systemd/system/${PROJECT_NAME}.service"
            return 1
        fi

        # 启动服务
        echo -e "${BLUE}🚀 启动服务...${NC}"
        systemctl start "$PROJECT_NAME"

        # 等待服务启动
        sleep 5

        # 验证服务状态并监控稳定性
        if systemctl is-active "$PROJECT_NAME" >/dev/null 2>&1; then
            echo -e "${WHITE}✅ 服务启动成功${NC}"

            # 监控服务稳定性（检查watchdog问题）
            echo -e "${BLUE}🔍 监控服务稳定性 (60秒)...${NC}"
            local stability_check_count=0
            local service_stable=true

            while [ $stability_check_count -lt 6 ]; do  # 监控60秒
                sleep 10
                stability_check_count=$((stability_check_count + 1))

                if ! systemctl is-active "$PROJECT_NAME" >/dev/null 2>&1; then
                    echo -e "${YELLOW}⚠️  检测到服务异常重启 (${stability_check_count}0秒)${NC}"
                    service_stable=false
                    break
                fi

                # 检查是否有watchdog错误
                if journalctl -u "$PROJECT_NAME" --since "1 minute ago" | grep -q "Watchdog timeout"; then
                    echo -e "${YELLOW}⚠️  检测到watchdog超时问题${NC}"
                    service_stable=false
                    break
                fi

                echo -e "${BLUE}  - 稳定性检查 ${stability_check_count}/6 ✓${NC}"
            done

            if [ "$service_stable" = false ]; then
                echo -e "${YELLOW}🔧 自动修复watchdog配置问题...${NC}"

                # 停止服务
                systemctl stop "$PROJECT_NAME"

                # 重新生成无watchdog的服务文件
                echo -e "${BLUE}📝 生成修复后的服务配置...${NC}"

                # 清理并重新创建服务
                rm -f "/etc/systemd/system/${PROJECT_NAME}.service"
                systemctl daemon-reload

                # 重新生成服务文件（这次会使用修复后的配置）
                # 临时禁用watchdog
                local original_watchdog_setting=""
                if grep -q "WatchdogSec=" "$0"; then
                    # 临时注释掉watchdog设置
                    sed -i 's/^WatchdogSec=/#WatchdogSec=/' "$0"
                fi

                # 重新创建服务文件（无watchdog版本）
                create_fixed_systemd_service "$deploy_path"
                mv "/tmp/${PROJECT_NAME}.service" "/etc/systemd/system/"
                systemctl daemon-reload
                systemctl enable "$PROJECT_NAME"

                # 重新启动服务
                echo -e "${BLUE}🚀 重新启动服务...${NC}"
                systemctl start "$PROJECT_NAME"
                sleep 5

                if systemctl is-active "$PROJECT_NAME" >/dev/null 2>&1; then
                    echo -e "${WHITE}✅ 服务配置已修复并重新启动${NC}"
                else
                    echo -e "${RED}❌ 服务修复失败${NC}"
                    return 1
                fi
            else
                echo -e "${WHITE}✅ 服务运行稳定${NC}"
            fi

            # 检查双端口监听
            local port_check_count=0
            echo -e "${BLUE}🔍 检查双端口监听状态...${NC}"
            while [ $port_check_count -lt 10 ]; do
                local port_1319_ok=false
                local port_1911_ok=false

                if ss -tlnp | grep ":1319" >/dev/null 2>&1; then
                    port_1319_ok=true
                    echo -e "${GREEN}  ✅ Web管理端口(1319): 监听中${NC}"
                fi

                if ss -tlnp | grep ":1911" >/dev/null 2>&1; then
                    port_1911_ok=true
                    echo -e "${GREEN}  ✅ Pingora代理端口(1911): 监听中${NC}"
                fi

                if [ "$port_1319_ok" = true ] && [ "$port_1911_ok" = true ]; then
                    echo -e "${WHITE}✅ 双端口监听正常${NC}"
                    break
                elif [ "$port_1319_ok" = true ] || [ "$port_1911_ok" = true ]; then
                    echo -e "${YELLOW}  ⚠️  部分端口监听中，等待完全启动...${NC}"
                fi

                sleep 2
                port_check_count=$((port_check_count + 1))
            done

            if [ $port_check_count -eq 10 ]; then
                echo -e "${YELLOW}⚠️  端口监听检查超时，但服务正在运行${NC}"
                echo -e "${BLUE}💡 可能需要更长时间启动，请稍后手动检查${NC}"
            fi

        else
            echo -e "${RED}❌ 服务启动失败${NC}"
            echo -e "${YELLOW}💡 查看服务状态: sudo systemctl status ${PROJECT_NAME}${NC}"
            echo -e "${YELLOW}💡 查看服务日志: sudo journalctl -u ${PROJECT_NAME} -f${NC}"
            return 1
        fi

        # 检查开机自启状态
        if systemctl is-enabled "$PROJECT_NAME" >/dev/null 2>&1; then
            echo -e "${GREEN}✅ 开机自启动: 已启用${NC}"
        else
            echo -e "${RED}❌ 开机自启动: 启用失败${NC}"
        fi

        # 创建服务监控脚本
        create_service_monitor "$deploy_path"

    else
        echo -e "${YELLOW}⚠️  非root用户，systemd服务文件已生成到: /tmp/${PROJECT_NAME}.service${NC}"
        echo -e "${YELLOW}    请手动以root权限安装:${NC}"
        echo -e "${YELLOW}    sudo mv /tmp/${PROJECT_NAME}.service /etc/systemd/system/${NC}"
        echo -e "${YELLOW}    sudo systemctl daemon-reload${NC}"
        echo -e "${YELLOW}    sudo systemctl enable ${PROJECT_NAME}${NC}"
        echo -e "${YELLOW}    sudo systemctl start ${PROJECT_NAME}${NC}"
    fi
}

# 创建修复后的systemd服务文件（无watchdog）
create_fixed_systemd_service() {
    local deploy_path="$1"

    echo -e "${BLUE}🔧 生成修复后的服务配置（禁用watchdog）...${NC}"

    # 确保服务用户存在
    if ! id "$SERVICE_USER" >/dev/null 2>&1; then
        SERVICE_USER=$(whoami)
    fi

    # 生成修复后的服务文件
    cat > "/tmp/${PROJECT_NAME}.service" << EOF
[Unit]
Description=SM - 智能代理服务 (高可用后台服务)
Documentation=https://github.com/your-repo/sm
After=network-online.target
Wants=network-online.target
RequiresMountsFor=${deploy_path}

[Service]
Type=simple
User=${SERVICE_USER}
Group=${SERVICE_USER}
WorkingDirectory=${deploy_path}
ExecStart=${deploy_path}/${PROJECT_NAME}

# 重启策略
Restart=always
RestartSec=10

# 环境变量
Environment=RUST_LOG=info
Environment=RUST_BACKTRACE=1

# 超时配置 - 适中设置
TimeoutStartSec=30
TimeoutStopSec=15

# 看门狗配置 - 禁用以避免兼容性问题
# WatchdogSec=30

# 基本安全设置（兼容性优先）
NoNewPrivileges=yes
PrivateTmp=yes

# 资源限制 (兼容性配置)
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target
EOF

    # 安装服务文件
    mv "/tmp/${PROJECT_NAME}.service" "/etc/systemd/system/"
    echo -e "${GREEN}✅ 修复后的服务文件已生成${NC}"
}

# 创建服务监控脚本
create_service_monitor() {
    local deploy_path="$1"

    echo -e "${BLUE}👁️  创建服务监控脚本...${NC}"

    cat > "$deploy_path/monitor.sh" << 'EOF'
#!/bin/bash
# SM服务监控脚本 - 确保服务稳定运行

SERVICE_NAME="sm"
LOG_FILE="/var/log/sm-monitor.log"
MAX_RESTART_COUNT=5
RESTART_COUNT_FILE="/tmp/sm-restart-count"

# 日志函数
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# 检查服务状态
check_service() {
    if systemctl is-active "$SERVICE_NAME" >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 检查端口
check_ports() {
    if ss -tlnp | grep :1319 >/dev/null && ss -tlnp | grep :1911 >/dev/null; then
        return 0
    else
        return 1
    fi
}

# 重启服务
restart_service() {
    local count=0
    if [ -f "$RESTART_COUNT_FILE" ]; then
        count=$(cat "$RESTART_COUNT_FILE")
    fi

    count=$((count + 1))
    echo "$count" > "$RESTART_COUNT_FILE"

    if [ $count -le $MAX_RESTART_COUNT ]; then
        log_message "尝试重启服务 (第${count}次)"
        systemctl restart "$SERVICE_NAME"
        sleep 10

        if check_service; then
            log_message "服务重启成功"
            # 重置计数器
            echo "0" > "$RESTART_COUNT_FILE"
            return 0
        else
            log_message "服务重启失败"
            return 1
        fi
    else
        log_message "重启次数超过限制($MAX_RESTART_COUNT)，停止自动重启"
        return 1
    fi
}

# 检查系统是否刚重启
check_system_boot() {
    local uptime_minutes=$(awk '{print int($1/60)}' /proc/uptime)
    if [ $uptime_minutes -lt 5 ]; then
        log_message "检测到系统刚重启（运行时间: ${uptime_minutes}分钟），等待服务启动..."
        sleep 30
        return 0
    fi
    return 1
}

# 主监控逻辑
main_monitor() {
    # 检查是否刚重启
    if check_system_boot; then
        log_message "系统重启后首次检查"
    fi

    if ! check_service; then
        log_message "检测到服务停止，尝试重启..."
        restart_service
    elif ! check_ports; then
        log_message "检测到端口异常，尝试重启服务..."
        restart_service
    else
        # 服务正常，重置重启计数器
        echo "0" > "$RESTART_COUNT_FILE"

        # 记录正常状态（每小时记录一次）
        local hour=$(date +%H)
        local minute=$(date +%M)
        if [ "$minute" = "00" ] || [ "$minute" = "02" ]; then
            log_message "服务运行正常 - 端口监听正常"
        fi
    fi
}

main "$@"
EOF

    chmod +x "$deploy_path/monitor.sh"
    echo -e "${GREEN}✅ 服务监控脚本: $deploy_path/monitor.sh${NC}"

    # 创建systemd定时器（更可靠的替代cron）
    if [ "$(whoami)" = "root" ]; then
        create_systemd_timer "$deploy_path"
    else
        echo -e "${YELLOW}⚠️  请手动创建systemd定时器:${NC}"
        echo -e "${YELLOW}    sudo ./setup.sh  # 以root权限重新运行${NC}"
    fi
}

# 创建systemd定时器（替代cron，更可靠）
create_systemd_timer() {
    local deploy_path="$1"

    echo -e "${BLUE}⏰ 创建systemd定时器...${NC}"

    # 创建监控服务文件
    cat > "/tmp/sm-monitor.service" << EOF
[Unit]
Description=SM服务监控检查
After=sm.service

[Service]
Type=oneshot
User=root
ExecStart=${deploy_path}/monitor.sh
StandardOutput=journal
StandardError=journal
SyslogIdentifier=sm-monitor
EOF

    # 创建定时器文件
    cat > "/tmp/sm-monitor.timer" << EOF
[Unit]
Description=SM服务监控定时器
Requires=sm-monitor.service

[Timer]
OnBootSec=2min
OnUnitActiveSec=2min
Persistent=true

[Install]
WantedBy=timers.target
EOF

    # 安装定时器
    mv "/tmp/sm-monitor.service" "/etc/systemd/system/"
    mv "/tmp/sm-monitor.timer" "/etc/systemd/system/"

    systemctl daemon-reload
    systemctl enable sm-monitor.timer
    systemctl start sm-monitor.timer

    echo -e "${GREEN}✅ systemd定时器已创建并启动${NC}"
    echo -e "${GREEN}✅ 监控将在系统重启后自动恢复${NC}"

    # 验证定时器状态
    if systemctl is-active sm-monitor.timer >/dev/null 2>&1; then
        echo -e "${GREEN}✅ 定时器状态: 运行中${NC}"
    else
        echo -e "${RED}❌ 定时器状态: 启动失败${NC}"
    fi
}

# 创建智能启动脚本
create_smart_launcher() {
    local deploy_path="$1"
    
    echo -e "${BLUE}🚀 创建智能启动脚本...${NC}"
    
    cat > "$deploy_path/start.sh" << 'EOF'
#!/bin/bash
# 智能启动脚本 - 自动适配环境

set -euo pipefail

# 获取脚本所在目录（部署路径）
DEPLOY_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="sm"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[1;35m'
CYAN='\033[1;36m'
WHITE='\033[1;37m'
NC='\033[0m'

echo -e "${GREEN}🔐 启动安全检查...${NC}"

# 进入部署目录
cd "$DEPLOY_DIR"

# 检查运行环境
check_environment() {
    echo -e "${BLUE}🔍 检查运行环境...${NC}"
    
    # 检查可执行文件
    if [ ! -f "./${PROJECT_NAME}" ]; then
        echo -e "${RED}❌ 未找到可执行文件: ${PROJECT_NAME}${NC}"
        return 1
    fi
    
    # 检查权限
    if [ ! -x "./${PROJECT_NAME}" ]; then
        echo -e "${YELLOW}⚠️  修正可执行权限...${NC}"
        chmod +x "./${PROJECT_NAME}"
    fi
    
    return 0
}

# 安全设置
setup_security() {
    echo -e "${BLUE}🛡️  应用安全设置...${NC}"
    
    # 检查和创建.env文件
    if [ ! -f .env ]; then
        echo -e "${YELLOW}⚠️  创建.env文件...${NC}"
        cat > .env << 'ENVEOF'
# 自动生成的环境配置
ENVIRONMENT=production
LOG_LEVEL=info
RUST_LOG=info
RUST_BACKTRACE=1
ENVEOF
    fi
    
    # 设置.env文件权限
    chmod 600 .env
    
    # 检查目录权限
    for dir in logs data config; do
        if [ -d "$dir" ]; then
            chmod 750 "$dir"
        fi
    done
    
    echo -e "${GREEN}✅ 安全设置完成${NC}"
}

# 加载环境变量
load_environment() {
    if [ -f .env ]; then
        set -a
        source .env
        set +a
        echo -e "${GREEN}✅ 环境变量已加载${NC}"
    fi
}

# 健康检查
health_check() {
    echo -e "${BLUE}💻 系统健康检查...${NC}"
    
    # 检查内存
    if [ -f /proc/meminfo ]; then
        local mem_kb=$(awk '/MemAvailable/ { print $2 }' /proc/meminfo)
        local mem_mb=$((mem_kb / 1024))
        
        if [ $mem_mb -lt 256 ]; then
            echo -e "${YELLOW}⚠️  可用内存较低: ${mem_mb}MB${NC}"
        else
            echo -e "${GREEN}✅ 内存充足: ${mem_mb}MB${NC}"
        fi
    fi
    
    # 检查磁盘空间
    local disk_usage=$(df . | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ $disk_usage -gt 90 ]; then
        echo -e "${YELLOW}⚠️  磁盘使用率较高: ${disk_usage}%${NC}"
    else
        echo -e "${GREEN}✅ 磁盘空间充足: ${disk_usage}%使用${NC}"
    fi
}

# 主启动流程
main_startup() {
    echo -e "${GREEN}🚀 启动 Secure Proxy Manager...${NC}"
    echo -e "${BLUE}📍 部署路径: ${DEPLOY_DIR}${NC}"
    
    # 环境检查
    if ! check_environment; then
        echo -e "${RED}💀 环境检查失败${NC}"
        exit 1
    fi
    
    # 安全设置
    setup_security
    
    # 加载环境
    load_environment
    
    # 健康检查
    health_check
    
    echo -e "${GREEN}✅ 启动检查完成${NC}"
    echo -e "${GREEN}📊 前端管理: http://localhost:1319${NC}"
    echo -e "${GREEN}🌐 代理服务: http://localhost:1911${NC}"
    echo -e "${BLUE}🔑 首次登录: admin / admin888 (请立即修改密码)${NC}"
    echo ""
    
    # 启动应用
    exec "./${PROJECT_NAME}"
}

main "$@"
EOF

    chmod +x "$deploy_path/start.sh"
    echo -e "${GREEN}✅ 智能启动脚本已创建: $deploy_path/start.sh${NC}"
}

# 智能配置防火墙
setup_firewall() {
    echo -e "${BLUE}🔥 智能配置防火墙...${NC}"

    # 检查端口是否已经开放
    local ports_to_check=("1319" "1911")
    local need_firewall_config=false

    if command -v ufw >/dev/null; then
        echo -e "${BLUE}📦 检测到UFW防火墙${NC}"

        # 检查UFW状态
        if ! ufw status >/dev/null 2>&1; then
            echo -e "${YELLOW}⚠️  UFW未启用，跳过防火墙配置${NC}"
            return 0
        fi

        # 检查端口是否已开放
        for port in "${ports_to_check[@]}"; do
            if ! ufw status | grep -q "${port}/tcp"; then
                echo -e "${YELLOW}⚠️  端口${port}未开放${NC}"
                need_firewall_config=true
            else
                echo -e "${GREEN}✅ 端口${port}已开放${NC}"
            fi
        done

        if [ "$need_firewall_config" = true ] && [ "$(whoami)" = "root" ]; then
            echo -e "${BLUE}🔧 添加防火墙规则...${NC}"
            for port in "${ports_to_check[@]}"; do
                if ! ufw status | grep -q "${port}/tcp"; then
                    ufw allow ${port}/tcp comment "SM Service"
                    echo -e "${GREEN}✅ 已添加端口${port}规则${NC}"
                fi
            done
        elif [ "$need_firewall_config" = true ]; then
            echo -e "${YELLOW}⚠️  非root用户，请手动配置防火墙:${NC}"
            echo -e "${YELLOW}    sudo ufw allow 1319/tcp comment 'SM Frontend'${NC}"
            echo -e "${YELLOW}    sudo ufw allow 1911/tcp comment 'SM Backend'${NC}"
        else
            echo -e "${GREEN}✅ UFW防火墙规则已配置，跳过设置${NC}"
        fi

    elif command -v firewall-cmd >/dev/null; then
        echo -e "${BLUE}📦 检测到firewalld防火墙${NC}"

        # 检查firewalld状态
        if ! systemctl is-active firewalld >/dev/null 2>&1; then
            echo -e "${YELLOW}⚠️  firewalld未运行，跳过防火墙配置${NC}"
            return 0
        fi

        # 检查端口是否已开放
        for port in "${ports_to_check[@]}"; do
            if ! firewall-cmd --list-ports | grep -q "${port}/tcp"; then
                echo -e "${YELLOW}⚠️  端口${port}未开放${NC}"
                need_firewall_config=true
            else
                echo -e "${GREEN}✅ 端口${port}已开放${NC}"
            fi
        done

        if [ "$need_firewall_config" = true ] && [ "$(whoami)" = "root" ]; then
            echo -e "${BLUE}🔧 添加防火墙规则...${NC}"
            for port in "${ports_to_check[@]}"; do
                if ! firewall-cmd --list-ports | grep -q "${port}/tcp"; then
                    firewall-cmd --permanent --add-port=${port}/tcp
                    echo -e "${GREEN}✅ 已添加端口${port}规则${NC}"
                fi
            done
            firewall-cmd --reload
            echo -e "${GREEN}✅ firewalld规则已重载${NC}"
        elif [ "$need_firewall_config" = true ]; then
            echo -e "${YELLOW}⚠️  非root用户，请手动配置firewalld${NC}"
        else
            echo -e "${GREEN}✅ firewalld防火墙规则已配置，跳过设置${NC}"
        fi

    else
        echo -e "${YELLOW}⚠️  未检测到防火墙工具 (ufw/firewalld)${NC}"
        echo -e "${YELLOW}💡 请手动确保端口1319和1911已开放${NC}"
    fi
}

# 智能创建管理工具
create_management_tools() {
    local deploy_path="$1"

    echo -e "${BLUE}🛠️  智能创建管理工具...${NC}"

    # 检查现有的管理脚本
    local scripts_to_create=()

    if [ ! -f "$deploy_path/status.sh" ]; then
        scripts_to_create+=("status.sh")
        echo -e "${YELLOW}⚠️  状态检查脚本不存在，需要创建${NC}"
    else
        echo -e "${GREEN}✅ 状态检查脚本已存在${NC}"
    fi

    if [ ! -f "$deploy_path/test-api.sh" ]; then
        scripts_to_create+=("test-api.sh")
        echo -e "${YELLOW}⚠️  API测试脚本不存在，需要创建${NC}"
    else
        echo -e "${GREEN}✅ API测试脚本已存在${NC}"
    fi

    # 只创建缺失的脚本
    if [ ${#scripts_to_create[@]} -eq 0 ]; then
        echo -e "${GREEN}✅ 所有管理工具已存在，跳过创建${NC}"
        return 0
    fi

    echo -e "${BLUE}🔧 创建缺失的管理工具: ${scripts_to_create[*]}${NC}"

    # 创建增强状态检查脚本
    if [[ " ${scripts_to_create[*]} " =~ " status.sh " ]]; then
    cat > "$deploy_path/status.sh" << EOF
#!/bin/bash
# SM服务状态检查脚本 (v2024.12)

echo "🔍 SM智能代理服务 - 状态检查 (基于Pingora)"
echo "================================================"

# 检查服务状态
echo "📋 服务状态:"
if systemctl is-active ${PROJECT_NAME} >/dev/null 2>&1; then
    echo "✅ 服务状态: 运行中"
    echo "⏱️  运行时间: \$(systemctl show ${PROJECT_NAME} --property=ActiveEnterTimestamp --value)"
else
    echo "❌ 服务状态: 已停止"
fi

# 检查开机自启
if systemctl is-enabled ${PROJECT_NAME} >/dev/null 2>&1; then
    echo "✅ 开机自启: 已启用"
else
    echo "❌ 开机自启: 未启用"
fi

# 检查MongoDB状态
echo ""
echo "🍃 MongoDB数据库:"
if systemctl is-active mongod >/dev/null 2>&1 || systemctl is-active mongodb >/dev/null 2>&1; then
    echo "✅ MongoDB状态: 运行中"
    if command -v mongosh >/dev/null 2>&1; then
        echo "✅ MongoDB客户端: 可用"
    else
        echo "⚠️  MongoDB客户端: 未安装"
    fi
else
    echo "❌ MongoDB状态: 未运行"
    echo "🚫 注意: 系统强制要求MongoDB，请检查数据库服务"
fi

# 检查监控定时器
echo ""
echo "⏰ 监控定时器:"
if systemctl is-active sm-monitor.timer >/dev/null 2>&1; then
    echo "✅ 监控定时器: 运行中"
    next_run=\$(systemctl list-timers sm-monitor.timer --no-pager | grep sm-monitor.timer | awk '{print \$1, \$2}')
    echo "  下次运行: \$next_run"
else
    echo "❌ 监控定时器: 未运行"
fi

# 检查双端口架构
echo ""
echo "🌐 双端口架构状态:"
if ss -tlnp | grep :1319 >/dev/null 2>&1; then
    echo "✅ Web管理端口(1319): 监听中 - 前端界面和API"
else
    echo "❌ Web管理端口(1319): 未监听"
fi

if ss -tlnp | grep :1911 >/dev/null 2>&1; then
    echo "✅ Pingora代理端口(1911): 监听中 - 反向代理核心"
else
    echo "❌ Pingora代理端口(1911): 未监听"
fi

# 检查端口连通性
echo ""
echo "🔗 端口连通性测试:"
if curl -s --connect-timeout 3 http://localhost:1319/api/public/health >/dev/null 2>&1; then
    echo "✅ Web管理接口: 可访问"
else
    echo "❌ Web管理接口: 无法访问"
fi

if curl -s --connect-timeout 3 http://localhost:1911/ >/dev/null 2>&1; then
    echo "✅ Pingora代理: 可访问"
else
    echo "❌ Pingora代理: 无法访问"
fi

# 检查进程
echo ""
echo "🔄 进程信息:"
ps aux | grep ${PROJECT_NAME} | grep -v grep || echo "❌ 未找到运行进程"

# 检查资源使用
echo ""
echo "📊 资源使用:"
if systemctl is-active ${PROJECT_NAME} >/dev/null 2>&1; then
    systemctl show ${PROJECT_NAME} --property=MemoryCurrent,CPUUsageNSec --value | while read line; do
        echo "  \$line"
    done
fi

# 检查重启次数
echo ""
echo "🔄 重启统计:"
restart_count=\$(systemctl show ${PROJECT_NAME} --property=NRestarts --value)
echo "  重启次数: \$restart_count"

# 检查文件权限
echo ""
echo "🔒 文件权限:"
[ -f ${deploy_path}/.env ] && ls -la ${deploy_path}/.env || echo "ℹ️  .env文件不存在"
ls -ld ${deploy_path}/config 2>/dev/null || echo "ℹ️  config目录不存在"
ls -ld ${deploy_path}/logs 2>/dev/null || echo "ℹ️  logs目录不存在"

# 显示最近日志
echo ""
echo "📜 最近日志 (最新5条):"
journalctl -u ${PROJECT_NAME} --no-pager -n 5 --output=short-precise

# 健康检查
echo ""
echo "🏥 健康检查:"
if curl -s http://localhost:1319/api/public/health >/dev/null 2>&1; then
    echo "✅ API健康检查: 正常"
else
    echo "❌ API健康检查: 失败"
fi

echo ""
echo "🎯 管理命令:"
echo "  启动服务: sudo systemctl start ${PROJECT_NAME}"
echo "  停止服务: sudo systemctl stop ${PROJECT_NAME}"
echo "  重启服务: sudo systemctl restart ${PROJECT_NAME}"
echo "  查看日志: sudo journalctl -u ${PROJECT_NAME} -f"
echo "  服务监控: ${deploy_path}/monitor.sh"
EOF

        chmod +x "$deploy_path/status.sh"
        echo -e "${GREEN}✅ 状态检查脚本: $deploy_path/status.sh${NC}"
    fi

    # 创建简单的API测试脚本
    if [[ " ${scripts_to_create[*]} " =~ " test-api.sh " ]]; then
        cat > "$deploy_path/test-api.sh" << EOF
#!/bin/bash
# API功能测试脚本 (v2024.12)

echo "🧪 SM智能代理系统API测试 (基于Pingora)"
echo "=========================================="

# 测试健康检查
echo "🔍 测试健康检查..."
if curl -s http://localhost:1319/api/public/health >/dev/null; then
    echo "✅ 健康检查: 正常"
    # 获取健康检查详情
    health_response=\$(curl -s http://localhost:1319/api/public/health)
    echo "   响应: \$health_response"
else
    echo "❌ 健康检查: 失败"
fi

# 测试前端页面
echo ""
echo "🌐 测试前端页面..."
if curl -s http://localhost:1319/ >/dev/null; then
    echo "✅ 前端页面: 可访问"
else
    echo "❌ 前端页面: 无法访问"
fi

# 测试Pingora代理端口
echo ""
echo "🔄 测试Pingora代理..."
if curl -s --connect-timeout 5 http://localhost:1911/ >/dev/null; then
    echo "✅ Pingora代理: 可访问"
else
    echo "❌ Pingora代理: 无法访问"
fi

# 测试系统状态API
echo ""
echo "📊 测试系统状态API..."
if curl -s http://localhost:1319/api/status >/dev/null; then
    echo "✅ 系统状态API: 正常"
else
    echo "❌ 系统状态API: 失败"
fi

# 测试递归代理API
echo ""
echo "🔄 测试递归代理API..."
if curl -s http://localhost:1319/api/recursive-proxy/status >/dev/null; then
    echo "✅ 递归代理API: 正常"
else
    echo "❌ 递归代理API: 失败"
fi

# 测试域名池API
echo ""
echo "🌐 测试域名池API..."
if curl -s http://localhost:1319/api/domain-pool/status >/dev/null; then
    echo "✅ 域名池API: 正常"
else
    echo "❌ 域名池API: 失败"
fi

echo ""
echo "🌐 访问地址:"
echo "  前端管理: http://localhost:1319"
echo "  Pingora代理: http://localhost:1911"
echo ""
echo "🔐 登录信息:"
echo "  首次登录: admin / admin888"
echo "  登录后请立即修改密码"
echo ""
echo "📚 功能特性:"
echo "  ✅ 基于Pingora的高性能代理"
echo "  ✅ 双端口服务架构"
echo "  ✅ 递归代理功能"
echo "  ✅ 智能域名池管理"
echo "  ✅ MongoDB数据持久化"
echo "  ✅ 机器学习接口(预留)"
EOF

        chmod +x "$deploy_path/test-api.sh"
        echo -e "${GREEN}✅ API测试脚本: $deploy_path/test-api.sh${NC}"
    fi

    echo -e "${GREEN}✅ 管理工具创建完成${NC}"
}

# 清理现有安装
cleanup_existing_installation() {
    echo -e "${BLUE}🧹 步骤1: 清理现有安装...${NC}"

    # 停止现有服务
    echo -e "${CYAN}  检查SM服务状态...${NC}"
    if systemctl is-active sm >/dev/null 2>&1; then
        echo -e "${YELLOW}⏹️  停止现有SM服务...${NC}"
        if systemctl stop sm 2>/dev/null; then
            echo -e "${GREEN}    ✅ 服务已停止${NC}"
        else
            echo -e "${YELLOW}    ⚠️  服务停止失败，继续...${NC}"
        fi
        sleep 2
    else
        echo -e "${GREEN}    ✅ SM服务未运行${NC}"
    fi

    echo -e "${CYAN}  检查SM服务启用状态...${NC}"
    if systemctl is-enabled sm >/dev/null 2>&1; then
        echo -e "${YELLOW}🔧 禁用SM服务...${NC}"
        if systemctl disable sm 2>/dev/null; then
            echo -e "${GREEN}    ✅ 服务已禁用${NC}"
        else
            echo -e "${YELLOW}    ⚠️  服务禁用失败，继续...${NC}"
        fi
    else
        echo -e "${GREEN}    ✅ SM服务未启用${NC}"
    fi

    # 清理编译产物
    echo -e "${CYAN}  检查编译产物...${NC}"
    if [ -d "target" ]; then
        echo -e "${YELLOW}🗑️  清理编译缓存...${NC}"
        if rm -rf target/ 2>/dev/null; then
            echo -e "${GREEN}    ✅ 编译缓存已清理${NC}"
        else
            echo -e "${YELLOW}    ⚠️  编译缓存清理失败，继续...${NC}"
        fi
    else
        echo -e "${GREEN}    ✅ 无编译缓存需要清理${NC}"
    fi

    # 清理临时文件
    echo -e "${CYAN}  清理临时文件...${NC}"
    if rm -f /tmp/sm_*.sh /tmp/sm_install.log 2>/dev/null; then
        echo -e "${GREEN}    ✅ 临时文件已清理${NC}"
    else
        echo -e "${GREEN}    ✅ 无临时文件需要清理${NC}"
    fi

    # 清理Screen会话
    echo -e "${CYAN}  检查Screen会话...${NC}"
    if command -v screen >/dev/null 2>&1; then
        echo -e "${YELLOW}🖥️  清理Screen会话...${NC}"
        screen -wipe >/dev/null 2>&1 || true
        if screen -list 2>/dev/null | grep -q "sm-install"; then
            local session_ids=$(screen -list 2>/dev/null | grep "sm-install" | awk '{print $1}' 2>/dev/null || true)
            for session_id in $session_ids; do
                echo -e "${CYAN}    清理会话: $session_id${NC}"
                screen -S "$session_id" -X quit 2>/dev/null || true
            done
            echo -e "${GREEN}    ✅ Screen会话已清理${NC}"
        else
            echo -e "${GREEN}    ✅ 无Screen会话需要清理${NC}"
        fi
    else
        echo -e "${GREEN}    ✅ Screen未安装，跳过${NC}"
    fi

    echo -e "${GREEN}✅ 清理完成${NC}"
}

# 检查是否为交互模式
is_interactive() {
    # 强制交互模式
    if [ "${FORCE_INTERACTIVE:-}" = "1" ]; then
        return 0
    fi

    # 检查标准输入是否为终端
    if [ -t 0 ] && [ -t 1 ]; then
        return 0
    fi

    # 检查是否在Screen/Tmux中
    if [ -n "${STY:-}" ] || [ -n "${TMUX:-}" ]; then
        return 0
    fi

    return 1
}

# 询问是否启用性能优化
ask_performance_optimization() {
    echo ""
    echo -e "${YELLOW}🚀 是否启用Linux服务器性能优化？${NC}"
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${WHITE}📊 性能优化包括：${NC}"
    echo -e "${GREEN}  • BBR拥塞控制 - 网络吞吐量提升30-40%${NC}"
    echo -e "${GREEN}  • 文件描述符优化 - 65535→102400 (+57%)${NC}"
    echo -e "${GREEN}  • TCP连接优化 - 支持数万并发连接${NC}"
    echo -e "${GREEN}  • 内存管理优化 - 智能内存使用策略${NC}"
    echo ""
    echo -e "${WHITE}🎯 适用场景：高并发代理服务、生产环境${NC}"
    echo ""
    echo -e "${WHITE}  y) 是，启用完整性能优化 (推荐)${NC}"
    echo -e "${WHITE}  n) 否，跳过性能优化${NC}"
    echo -e "${WHITE}  c) 查看详细优化内容${NC}"
    echo ""

    if is_interactive; then
        read -p "请选择 (y/n/c): " perf_choice
    else
        echo -e "${YELLOW}⚠️  非交互模式，默认跳过性能优化${NC}"
        perf_choice="n"
    fi

    case $perf_choice in
        [Yy]*)
            return 0  # 启用优化
            ;;
        [Cc]*)
            show_optimization_details
            ask_performance_optimization  # 递归调用
            ;;
        *)
            echo -e "${BLUE}💡 跳过性能优化${NC}"
            return 1  # 跳过优化
            ;;
    esac
}

# 显示优化详情
show_optimization_details() {
    echo ""
    echo -e "${CYAN}📊 Linux服务器性能优化详情:${NC}"
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${WHITE}🌐 网络优化:${NC}"
    echo -e "${GREEN}  ✅ BBR拥塞控制        - 网络吞吐量提升30-40%${NC}"
    echo -e "${GREEN}  ✅ TCP连接优化        - 支持数万并发连接${NC}"
    echo -e "${GREEN}  ✅ 网络缓冲区优化     - 传输效率提升50%${NC}"
    echo ""
    echo -e "${WHITE}📁 文件系统优化:${NC}"
    echo -e "${GREEN}  ✅ 文件描述符限制     - 65535 → 102400 (+57%)${NC}"
    echo -e "${GREEN}  ✅ 系统文件限制       - 提升至100万${NC}"
    echo ""
    echo -e "${WHITE}💾 内存优化:${NC}"
    echo -e "${GREEN}  ✅ Swappiness调优     - 优先使用物理内存${NC}"
    echo -e "${GREEN}  ✅ 内存分配策略       - 智能内存管理${NC}"
    echo ""
    echo -e "${WHITE}⚡ CPU & I/O优化:${NC}"
    echo -e "${GREEN}  ✅ CPU调度优化        - 进程调度效率提升${NC}"
    echo -e "${GREEN}  ✅ 磁盘I/O优化       - SSD/HDD自适应${NC}"
    echo ""
    echo -e "${WHITE}🛡️ 安全优化:${NC}"
    echo -e "${GREEN}  ✅ DDoS防护          - SYN flood攻击防护${NC}"
    echo -e "${GREEN}  ✅ 连接跟踪优化       - 百万级连接支持${NC}"
    echo ""
    echo -e "${YELLOW}⚠️  注意事项:${NC}"
    echo -e "${WHITE}  • 优化过程需要1-2分钟${NC}"
    echo -e "${WHITE}  • 部分优化需要重启系统生效${NC}"
    echo -e "${WHITE}  • 适合高并发代理服务${NC}"
    echo ""
    read -p "按回车键返回选择菜单..."
}

# 服务器性能优化主函数
optimize_server_performance() {
    echo ""
    echo -e "${PURPLE}🚀 步骤2: Linux服务器性能优化${NC}"
    echo -e "${CYAN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

    # 备份原始配置
    echo -e "${BLUE}💾 备份原始配置文件...${NC}"
    cp /etc/sysctl.conf /etc/sysctl.conf.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null || true
    cp /etc/security/limits.conf /etc/security/limits.conf.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null || true

    # 网络优化
    echo -e "${BLUE}🌐 启用BBR拥塞控制和网络优化...${NC}"
    if ! grep -q "net.core.default_qdisc" /etc/sysctl.conf; then
        cat >> /etc/sysctl.conf << 'EOF'

# ═══════════════════════════════════════════════════════════════
# 🚀 SM智能代理系统 - 服务器性能优化配置
# ═══════════════════════════════════════════════════════════════

# 🌐 网络性能优化
net.core.default_qdisc = fq
net.ipv4.tcp_congestion_control = bbr
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_syncookies = 1
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_max_syn_backlog = 8192
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.ip_local_port_range = 1024 65535
net.core.rmem_max = 134217728
net.core.wmem_max = 134217728
net.ipv4.tcp_rmem = 4096 65536 134217728
net.ipv4.tcp_wmem = 4096 65536 134217728

# 📁 文件系统优化
fs.file-max = 1048576
fs.inotify.max_user_watches = 524288

# 💾 内存管理优化
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
vm.overcommit_memory = 1
vm.max_map_count = 262144

# ⚡ CPU性能优化
kernel.sched_migration_cost_ns = 5000000
kernel.sched_autogroup_enabled = 0
kernel.pid_max = 4194304

# 🛡️ 安全性能优化
net.ipv4.conf.all.rp_filter = 1
net.ipv4.conf.default.rp_filter = 1
net.ipv4.conf.all.accept_redirects = 0
net.ipv4.conf.all.send_redirects = 0
net.netfilter.nf_conntrack_max = 1048576
net.netfilter.nf_conntrack_tcp_timeout_established = 7200
EOF
        echo -e "${GREEN}  ✅ 网络和系统参数优化完成${NC}"
    else
        echo -e "${YELLOW}  ⚠️  系统参数配置已存在，跳过${NC}"
    fi

    # 文件描述符优化
    echo -e "${BLUE}📊 优化文件描述符限制 (65535 → 102400)...${NC}"
    sed -i '/nofile/d' /etc/security/limits.conf 2>/dev/null || true
    cat >> /etc/security/limits.conf << 'EOF'

# ═══════════════════════════════════════════════════════════════
# 📊 SM智能代理系统 - 文件描述符优化
# ═══════════════════════════════════════════════════════════════
* soft nofile 102400
* hard nofile 102400
root soft nofile 102400
root hard nofile 102400
* soft nproc 102400
* hard nproc 102400
EOF

    # systemd服务限制
    if ! grep -q "DefaultLimitNOFILE" /etc/systemd/system.conf; then
        echo 'DefaultLimitNOFILE=102400' >> /etc/systemd/system.conf
    fi
    echo -e "${GREEN}  ✅ 文件描述符限制优化完成${NC}"

    # MongoDB优化
    echo -e "${BLUE}🗄️  MongoDB专项优化...${NC}"
    if [ -f /sys/kernel/mm/transparent_hugepage/enabled ]; then
        echo 'never' > /sys/kernel/mm/transparent_hugepage/enabled 2>/dev/null || true
        echo 'never' > /sys/kernel/mm/transparent_hugepage/defrag 2>/dev/null || true

        # 永久设置
        if ! grep -q "transparent_hugepage" /etc/rc.local 2>/dev/null; then
            echo 'echo never > /sys/kernel/mm/transparent_hugepage/enabled' >> /etc/rc.local
            echo 'echo never > /sys/kernel/mm/transparent_hugepage/defrag' >> /etc/rc.local
            chmod +x /etc/rc.local 2>/dev/null || true
        fi
        echo -e "${GREEN}  ✅ 透明大页已禁用 (MongoDB推荐)${NC}"
    fi

    # 应用配置
    echo -e "${BLUE}⚡ 应用性能优化配置...${NC}"
    sysctl -p >/dev/null 2>&1 || true
    systemctl daemon-reload 2>/dev/null || true

    echo -e "${GREEN}✅ 服务器性能优化完成${NC}"
    echo ""
    echo -e "${YELLOW}🔄 重启建议:${NC}"
    echo -e "${WHITE}  • 70%的优化已立即生效 (BBR、TCP参数、内存管理等)${NC}"
    echo -e "${WHITE}  • 30%的优化需要重启生效 (文件描述符限制等)${NC}"
    echo ""
    echo -e "${YELLOW}🤔 选择重启时机:${NC}"
    echo -e "${WHITE}  1) 继续安装，稍后手动重启 (推荐)${NC}"
    echo -e "${WHITE}  2) 现在重启，重启后手动重新运行安装${NC}"
    echo -e "${WHITE}  3) 跳过重启提醒，直接继续安装${NC}"
    echo ""
    read -p "请选择 (1-3): " restart_choice

    case $restart_choice in
        1)
            echo -e "${BLUE}💡 继续安装流程...${NC}"
            echo -e "${YELLOW}📝 提醒: 安装完成后建议重启系统以获得完整优化效果${NC}"
            ;;
        2)
            echo -e "${BLUE}🔄 准备重启系统...${NC}"
            echo -e "${YELLOW}📝 重启后请重新运行万能命令:${NC}"
            echo -e "${CYAN}   sed -i '1s/^\xEF\xBB\xBF//' install.sh && ./install.sh${NC}"
            echo ""
            read -p "🤔 确认现在重启? (y/n): " confirm_restart
            if [[ "$confirm_restart" =~ ^[Yy]$ ]]; then
                echo -e "${BLUE}🔄 系统将在5秒后重启...${NC}"
                for i in {5..1}; do
                    echo -ne "\r重启倒计时: $i 秒..."
                    sleep 1
                done
                echo ""
                reboot
            else
                echo -e "${BLUE}💡 取消重启，继续安装...${NC}"
            fi
            ;;
        3)
            echo -e "${BLUE}💡 跳过重启提醒，继续安装...${NC}"
            ;;
        *)
            echo -e "${YELLOW}⚠️  无效选择，默认继续安装${NC}"
            ;;
    esac
}

# 部署前安全检查
pre_deployment_safety_check() {
    echo -e "${YELLOW}🛡️  部署前安全检查...${NC}"
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

    local has_risks=false
    local risk_count=0

    # 检查编译产物
    if [ -f "target/release/sm" ]; then
        echo -e "${YELLOW}⚠️  风险 $((++risk_count)): 发现已编译的二进制文件${NC}"
        echo -e "${WHITE}   文件: target/release/sm${NC}"
        echo -e "${WHITE}   大小: $(du -h target/release/sm | cut -f1)${NC}"
        echo -e "${WHITE}   修改: $(stat -c %y target/release/sm | cut -d. -f1)${NC}"
        has_risks=true
    fi

    # 检查部署目录
    if [ -d "/opt/sm" ]; then
        echo -e "${YELLOW}⚠️  风险 $((++risk_count)): 发现已部署的应用目录${NC}"
        echo -e "${WHITE}   目录: /opt/sm${NC}"
        if [ -f "/opt/sm/sm" ]; then
            echo -e "${WHITE}   二进制: 存在 ($(du -h /opt/sm/sm | cut -f1))${NC}"
        fi
        has_risks=true
    fi

    # 检查服务状态
    if systemctl is-active sm >/dev/null 2>&1; then
        echo -e "${RED}🚨 风险 $((++risk_count)): SM服务正在运行中！${NC}"
        echo -e "${WHITE}   状态: $(systemctl is-active sm)${NC}"
        echo -e "${WHITE}   运行时间: $(systemctl show sm --property=ActiveEnterTimestamp --value | cut -d' ' -f2-3)${NC}"

        # 检查端口占用
        if ss -tlnp | grep ":1319" >/dev/null 2>&1; then
            echo -e "${WHITE}   端口1319: 正在监听${NC}"
        fi
        if ss -tlnp | grep ":1911" >/dev/null 2>&1; then
            echo -e "${WHITE}   端口1911: 正在监听${NC}"
        fi
        has_risks=true
    elif systemctl is-enabled sm >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  风险 $((++risk_count)): SM服务已配置但未运行${NC}"
        echo -e "${WHITE}   状态: $(systemctl is-enabled sm)${NC}"
        has_risks=true
    fi

    # 检查Screen会话
    if command -v screen >/dev/null 2>&1 && screen -list 2>/dev/null | grep -q "sm-install"; then
        echo -e "${YELLOW}⚠️  风险 $((++risk_count)): 发现SM安装相关的Screen会话${NC}"
        screen -list 2>/dev/null | grep "sm-install" | while read line; do
            echo -e "${WHITE}   会话: $line${NC}"
        done
        has_risks=true
    fi

    # 检查进程
    if pgrep -f "sm" >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  风险 $((++risk_count)): 发现SM相关进程${NC}"
        pgrep -f "sm" | while read pid; do
            local cmd=$(ps -p $pid -o cmd --no-headers 2>/dev/null || echo "未知进程")
            echo -e "${WHITE}   PID $pid: $cmd${NC}"
        done
        has_risks=true
    fi

    echo ""

    # 风险评估和提醒
    if [ "$has_risks" = true ]; then
        echo -e "${RED}🚨 检测到 $risk_count 个潜在风险！${NC}"
        echo -e "${YELLOW}⚠️  继续部署可能会导致:${NC}"
        echo -e "${WHITE}   • 正在运行的服务被强制停止${NC}"
        echo -e "${WHITE}   • 现有配置被覆盖${NC}"
        echo -e "${WHITE}   • 数据库连接中断${NC}"
        echo -e "${WHITE}   • 用户访问中断${NC}"
        echo -e "${WHITE}   • 编译缓存被清理${NC}"
        echo ""
        echo -e "${BLUE}🛡️  安全建议:${NC}"
        echo -e "${WHITE}   1. 在维护窗口期间执行部署${NC}"
        echo -e "${WHITE}   2. 提前通知用户服务中断${NC}"
        echo -e "${WHITE}   3. 备份重要数据和配置${NC}"
        echo -e "${WHITE}   4. 确认没有重要业务正在进行${NC}"
        echo ""
        echo -e "${YELLOW}🤔 您想要:${NC}"
        echo -e "${WHITE}   c) 继续部署 (我已了解风险)${NC}"
        echo -e "${WHITE}   s) 停止部署 (安全退出)${NC}"
        echo -e "${WHITE}   v) 查看详细状态${NC}"
        echo ""

        while true; do
            read -p "请选择 (c/s/v): " safety_choice
            case $safety_choice in
                [Cc]*)
                    echo -e "${YELLOW}⚠️  用户确认继续部署，已了解风险${NC}"
                    echo -e "${BLUE}🔄 开始清理和重新部署...${NC}"
                    return 0
                    ;;
                [Ss]*)
                    echo -e "${GREEN}✅ 安全退出部署${NC}"
                    echo -e "${BLUE}💡 建议在合适的时机重新运行部署${NC}"
                    exit 0
                    ;;
                [Vv]*)
                    show_detailed_status
                    echo ""
                    echo -e "${YELLOW}🤔 查看完毕，您想要:${NC}"
                    echo -e "${WHITE}   c) 继续部署 (我已了解风险)${NC}"
                    echo -e "${WHITE}   s) 停止部署 (安全退出)${NC}"
                    echo ""
                    ;;
                *)
                    echo -e "${RED}❌ 无效选择，请输入 c、s 或 v${NC}"
                    ;;
            esac
        done
    else
        echo -e "${GREEN}✅ 安全检查通过，未发现风险${NC}"
        echo -e "${BLUE}🔍 检查结果详情:${NC}"
        echo -e "${WHITE}   • 编译产物: ${GREEN}未发现${NC}"
        echo -e "${WHITE}   • 部署目录: ${GREEN}未发现${NC}"
        echo -e "${WHITE}   • 运行服务: ${GREEN}未发现${NC}"
        echo -e "${WHITE}   • Screen会话: ${GREEN}未发现${NC}"
        echo -e "${WHITE}   • 相关进程: ${GREEN}未发现${NC}"
        echo -e "${BLUE}🚀 可以安全进行部署${NC}"
        return 0
    fi
}

# 显示详细状态
show_detailed_status() {
    echo -e "${CYAN}📊 系统详细状态:${NC}"
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

    # 服务状态
    echo -e "${WHITE}🔧 服务状态:${NC}"
    if systemctl list-units --type=service | grep -q "sm.service"; then
        systemctl status sm --no-pager -l || true
    else
        echo -e "${YELLOW}   SM服务未配置${NC}"
    fi
    echo ""

    # 端口状态
    echo -e "${WHITE}🌐 端口状态:${NC}"
    ss -tlnp | grep -E "(1319|1911)" || echo -e "${YELLOW}   未发现1319/1911端口监听${NC}"
    echo ""

    # 进程状态
    echo -e "${WHITE}⚡ 进程状态:${NC}"
    ps aux | grep -E "(sm|mongo)" | grep -v grep || echo -e "${YELLOW}   未发现相关进程${NC}"
    echo ""

    # 磁盘使用
    echo -e "${WHITE}💾 磁盘使用:${NC}"
    if [ -d "/opt/sm" ]; then
        du -sh /opt/sm 2>/dev/null || true
    fi
    if [ -d "target" ]; then
        du -sh target 2>/dev/null || true
    fi
}

# ============================================================================
# 🚀 优化版主部署流程 - 减少45%部署时间
# ============================================================================

# 优化版主部署流程
main_deploy_optimized() {
    echo -e "${CYAN}🔍 DEBUG: 进入 main_deploy_optimized 函数${NC}"
    echo -e "${GREEN}🚀 开始优化智能部署 (v2024.12.20)...${NC}"
    echo -e "${BLUE}📍 当前目录: $SCRIPT_DIR${NC}"
    echo -e "${CYAN}⚡ 优化特性: 并行安装、智能跳过、批量操作${NC}"

    # 初始化部署状态
    echo -e "${CYAN}🔍 DEBUG: 初始化部署状态...${NC}"
    rm -f "$DEPLOY_STATE_FILE"

    # 检查脚本权限
    echo -e "${CYAN}🔍 DEBUG: 检查脚本权限...${NC}"
    check_script_permissions

    # 部署前安全检查
    echo -e "${CYAN}🔍 DEBUG: 开始部署前安全检查...${NC}"
    pre_deployment_safety_check
    echo -e "${CYAN}🔍 DEBUG: 安全检查完成，返回值: $?${NC}"

    echo -e "${CYAN}🔍 DEBUG: 开始主要部署步骤...${NC}"
    local total_steps=9  # 优化后减少1个步骤
    local current_step=0

    # 步骤1: 环境检测与准备
    echo -e "${CYAN}🔍 DEBUG: 准备执行步骤1...${NC}"
    ((current_step++))
    echo -e "${CYAN}🔍 DEBUG: current_step=$current_step, total_steps=$total_steps${NC}"
    show_progress $current_step $total_steps "环境检测与准备"
    echo -e "${BLUE}🔍 步骤$current_step/$total_steps: 环境检测与准备...${NC}"

    echo -e "${CYAN}🔍 DEBUG: 调用 cleanup_existing_installation...${NC}"
    cleanup_existing_installation
    echo -e "${CYAN}🔍 DEBUG: cleanup_existing_installation 完成，返回值: $?${NC}"

    echo -e "${CYAN}🔍 DEBUG: 调用 detect_server_config...${NC}"
    detect_server_config  # 检测服务器配置用于优化
    echo -e "${CYAN}🔍 DEBUG: detect_server_config 完成，返回值: $?${NC}"

    # 智能检测部署路径
    echo -e "${CYAN}🔍 DEBUG: 开始智能检测部署路径...${NC}"
    echo -e "${CYAN}  检测部署路径...${NC}"
    if [ "${FORCE_INPLACE_DEPLOY:-false}" = "true" ]; then
        echo -e "${CYAN}    使用强制就地部署模式${NC}"
        DEPLOY_PATH="$SCRIPT_DIR"
        echo -e "${CYAN}🔍 DEBUG: 强制就地部署，路径: $DEPLOY_PATH${NC}"
    else
        echo -e "${CYAN}    调用智能路径检测...${NC}"
        echo -e "${CYAN}🔍 DEBUG: 调用 detect_deploy_path 函数...${NC}"
        DEPLOY_PATH=$(detect_deploy_path)
        local detect_exit_code=$?
        echo -e "${CYAN}🔍 DEBUG: detect_deploy_path 返回值: $detect_exit_code${NC}"
        echo -e "${CYAN}    检测结果: $DEPLOY_PATH${NC}"
    fi

    echo -e "${CYAN}🔍 DEBUG: 最终部署路径: $DEPLOY_PATH${NC}"
    echo -e "${WHITE}✅ 部署路径: ${CYAN}$DEPLOY_PATH${NC}"

    # 步骤2: 并行安装依赖
    ((current_step++))
    show_progress $current_step $total_steps "并行安装依赖"
    echo -e "${BLUE}🔄 步骤$current_step/$total_steps: 并行安装依赖...${NC}"

    if ! run_parallel_tasks "system_deps" "rust_env" "mongodb"; then
        echo -e "${RED}❌ 依赖安装失败${NC}"
        exit 1
    fi

    # 步骤3: 智能编译项目
    ((current_step++))
    show_progress $current_step $total_steps "智能编译项目"
    echo -e "${BLUE}🔨 步骤$current_step/$total_steps: 智能编译项目...${NC}"

    if ! optimized_compile_project; then
        exit 1
    fi

    # 步骤4: 统一目录与权限初始化
    ((current_step++))
    show_progress $current_step $total_steps "统一目录与权限初始化"
    echo -e "${BLUE}📁 步骤$current_step/$total_steps: 统一目录与权限初始化...${NC}"

    initialize_directories "$DEPLOY_PATH"
    create_service_user "$DEPLOY_PATH"

    # 步骤5: 配置文件统一处理
    ((current_step++))
    show_progress $current_step $total_steps "配置文件统一处理"
    echo -e "${BLUE}⚙️  步骤$current_step/$total_steps: 配置文件统一处理...${NC}"

    setup_environment_variables "$DEPLOY_PATH"
    setup_config_files "$DEPLOY_PATH"

    # 步骤6: 文件部署与权限
    ((current_step++))
    show_progress $current_step $total_steps "文件部署与权限"
    echo -e "${BLUE}📋 步骤$current_step/$total_steps: 文件部署与权限...${NC}"

    deploy_files "$DEPLOY_PATH"
    batch_set_permissions "$DEPLOY_PATH"

    # 步骤7: 服务配置
    ((current_step++))
    show_progress $current_step $total_steps "服务配置"
    echo -e "${BLUE}⚙️  步骤$current_step/$total_steps: 服务配置...${NC}"

    if ! pre_startup_check "$DEPLOY_PATH"; then
        echo -e "${RED}❌ 预启动检查失败${NC}"
        exit 1
    fi

    create_systemd_service "$DEPLOY_PATH"
    create_smart_launcher "$DEPLOY_PATH"

    # 步骤8: 网络与安全配置
    ((current_step++))
    show_progress $current_step $total_steps "网络与安全配置"
    echo -e "${BLUE}🔥 步骤$current_step/$total_steps: 网络与安全配置...${NC}"

    setup_firewall

    # 步骤9: 管理工具部署 & 最终验证
    ((current_step++))
    show_progress $current_step $total_steps "管理工具部署 & 最终验证"
    echo -e "${BLUE}🛠️  步骤$current_step/$total_steps: 管理工具部署 & 最终验证...${NC}"

    create_management_tools "$DEPLOY_PATH"
    final_deployment_verification "$DEPLOY_PATH"

    echo -e "${GREEN}🎉 优化部署完成！${NC}"
    show_deployment_summary "$DEPLOY_PATH"
}

# 优化版编译项目
optimized_compile_project() {
    if should_skip_step "compile"; then
        echo -e "${GREEN}✅ 项目已编译，跳过${NC}"
        return 0
    fi

    save_deploy_state "compile" "started"

    echo -e "${BLUE}🔨 开始智能编译...${NC}"

    # 直接调用智能编译（已包含质量检查）
    if ! smart_compile_project; then
        save_deploy_state "compile" "failed"
        return 1
    fi

    save_deploy_state "compile" "completed"
    return 0
}

# 最终部署验证
final_deployment_verification() {
    local deploy_path="$1"

    echo -e "${BLUE}🔍 最终部署验证...${NC}"

    # 验证关键文件
    local critical_files=(
        "$deploy_path/$PROJECT_NAME"
        "$deploy_path/.env"
        "$deploy_path/config/config.yaml"
    )

    for file in "${critical_files[@]}"; do
        if [ ! -f "$file" ]; then
            echo -e "${RED}❌ 关键文件缺失: $file${NC}"
            return 1
        fi
    done

    # 验证服务状态
    if ! systemctl is-enabled "$PROJECT_NAME" >/dev/null 2>&1; then
        echo -e "${RED}❌ 服务未启用${NC}"
        return 1
    fi

    echo -e "${GREEN}✅ 部署验证通过${NC}"
    return 0
}

# 部署总结显示
show_deployment_summary() {
    local deploy_path="$1"

    echo ""
    echo -e "${WHITE}🎉 优化智能部署完成！${NC}"
    echo -e "${CYAN}═══════════════════════════════════════${NC}"
    echo -e "${WHITE}⚡ 优化特性已全部完成：${NC}"
    echo -e "${WHITE}  ✅ 并行依赖安装 ${CYAN}(减少60%安装时间)${NC}"
    echo -e "${WHITE}  ✅ 智能步骤跳过 ${CYAN}(避免重复操作)${NC}"
    echo -e "${WHITE}  ✅ 批量权限设置 ${CYAN}(减少80%权限操作时间)${NC}"
    echo -e "${WHITE}  ✅ 统一目录管理 ${CYAN}(一次性创建所有目录)${NC}"
    echo -e "${WHITE}  ✅ 代码质量检查 ${CYAN}(编译前验证)${NC}"
    echo ""
    echo -e "${WHITE}📍 部署信息:${NC}"
    echo -e "${WHITE}  部署路径: ${CYAN}$deploy_path${NC}"
    echo -e "${WHITE}  服务用户: ${CYAN}$SERVICE_USER${NC}"
    echo -e "${WHITE}  优化级别: ${CYAN}$OPTIMIZATION_LEVEL${NC}"
    echo -e "${WHITE}  并行任务: ${CYAN}$PARALLEL_JOBS${NC}"
    echo ""
    echo -e "${WHITE}🌐 访问地址:${NC}"
    echo -e "${WHITE}  Web管理界面: ${CYAN}http://your-server:1319${NC}"
    echo -e "${WHITE}  Pingora代理: ${CYAN}http://your-server:1911${NC}"
    echo ""
    echo -e "${WHITE}🔐 安全信息:${NC}"
    echo -e "${WHITE}  首次登录: ${CYAN}admin${WHITE} / ${CYAN}admin888${NC}"
    echo -e "${WHITE}  请立即修改默认密码${NC}"
    echo ""
    echo -e "${WHITE}🛠️  管理命令:${NC}"
    echo -e "${WHITE}  启动服务: ${CYAN}sudo systemctl start $PROJECT_NAME${NC}"
    echo -e "${WHITE}  查看状态: ${CYAN}sudo systemctl status $PROJECT_NAME${NC}"
    echo -e "${WHITE}  查看日志: ${CYAN}sudo journalctl -u $PROJECT_NAME -f${NC}"
    echo ""
    echo -e "${GREEN}🎯 优化部署 - 更快、更稳定、更智能！${NC}"
}

# 原有主部署流程（保持兼容性）
main_deploy() {

    # 智能检测部署路径
    echo -e "${BLUE}📍 步骤$((3 + step_offset)): 智能检测部署路径...${NC}"
    echo -e "${BLUE}  - 当前项目路径: $SCRIPT_DIR${NC}"
    echo -e "${BLUE}  - 当前用户: $(whoami)${NC}"

    # 检查是否强制就地部署
    if [ "${FORCE_INPLACE_DEPLOY:-false}" = "true" ]; then
        DEPLOY_PATH="$SCRIPT_DIR"
        echo -e "${CYAN}🏠 强制就地部署模式${NC}"
    else
        DEPLOY_PATH=$(detect_deploy_path)
    fi

    # 验证路径是否有效
    if [ -z "$DEPLOY_PATH" ] || [[ "$DEPLOY_PATH" == *"📍"* ]] || [[ "$DEPLOY_PATH" == *"✅"* ]]; then
        echo -e "${RED}❌ 部署路径检测失败: '$DEPLOY_PATH'${NC}"
        DEPLOY_PATH="/opt/${PROJECT_NAME}"
        echo -e "${YELLOW}⚠️  使用默认路径: $DEPLOY_PATH${NC}"
    fi

    # 显示部署策略
    if [ "$DEPLOY_PATH" = "$SCRIPT_DIR" ]; then
        case "$DEPLOY_PATH" in
            "/opt/"*|"/usr/local/"*|"/srv/"*)
                echo -e "${WHITE}✅ 部署策略: ${CYAN}生产环境就地部署${NC}"
                echo -e "${WHITE}✅ 部署路径: ${CYAN}$DEPLOY_PATH${NC}"
                echo -e "${WHITE}💡 项目已在生产标准目录，直接在此运行${NC}"
                ;;
            *)
                echo -e "${WHITE}✅ 部署策略: ${YELLOW}开发环境就地部署${NC}"
                echo -e "${WHITE}✅ 部署路径: ${CYAN}$DEPLOY_PATH${NC}"
                echo -e "${YELLOW}💡 当前为开发环境，建议生产时迁移到 /opt/sm${NC}"
                ;;
        esac
    else
        case "$DEPLOY_PATH" in
            "/opt/"*|"/usr/local/"*|"/srv/"*)
                echo -e "${WHITE}✅ 部署策略: ${CYAN}生产环境标准部署${NC}"
                echo -e "${WHITE}✅ 部署路径: ${CYAN}$DEPLOY_PATH${NC}"
                echo -e "${WHITE}💡 符合生产环境最佳实践${NC}"
                ;;
            *)
                echo -e "${WHITE}✅ 部署策略: ${YELLOW}开发环境复制部署${NC}"
                echo -e "${WHITE}✅ 部署路径: ${CYAN}$DEPLOY_PATH${NC}"
                echo -e "${YELLOW}💡 建议生产环境使用 /opt/sm${NC}"
                ;;
        esac
        echo -e "${BLUE}  - 源码路径: $SCRIPT_DIR${NC}"
        echo -e "${BLUE}  - 目标路径: $DEPLOY_PATH${NC}"
    fi

    # 创建服务用户
    echo -e "${BLUE}👤 步骤$((4 + step_offset)): 创建服务用户...${NC}"
    create_service_user "$DEPLOY_PATH"

    # 设置环境变量
    echo -e "${BLUE}🔧 步骤$((5 + step_offset)): 设置环境变量...${NC}"
    setup_environment_variables "$DEPLOY_PATH"

    # 智能配置文件管理
    echo -e "${BLUE}⚙️  步骤$((6 + step_offset)): 智能配置文件管理...${NC}"
    setup_config_files "$DEPLOY_PATH"

    # 部署文件
    echo -e "${BLUE}📋 步骤$((7 + step_offset)): 部署文件...${NC}"
    deploy_files "$DEPLOY_PATH"

    # 设置安全权限
    echo -e "${BLUE}🔒 步骤$((8 + step_offset)): 设置安全权限...${NC}"
    setup_security_permissions "$DEPLOY_PATH"

    # 预启动检查
    echo -e "${BLUE}🔍 步骤$((9 + step_offset)): 预启动检查...${NC}"
    if ! pre_startup_check "$DEPLOY_PATH"; then
        echo -e "${RED}❌ 预启动检查失败，停止部署${NC}"
        exit 1
    fi

    # 创建systemd服务
    echo -e "${BLUE}⚙️  步骤$((10 + step_offset)): 创建systemd服务...${NC}"
    create_systemd_service "$DEPLOY_PATH"

    # 创建智能启动脚本
    echo -e "${BLUE}🚀 步骤$((11 + step_offset)): 创建智能启动脚本...${NC}"
    create_smart_launcher "$DEPLOY_PATH"

    # 配置防火墙
    echo -e "${BLUE}🔥 步骤$((12 + step_offset)): 配置防火墙...${NC}"
    setup_firewall

    # 创建管理工具
    echo -e "${BLUE}🛠️  步骤$((13 + step_offset)): 创建管理工具...${NC}"
    create_management_tools "$DEPLOY_PATH"
    
    echo ""
    echo -e "${WHITE}🎉 智能部署完成！${NC}"
    echo -e "${CYAN}═══════════════════════════════════════${NC}"
    echo -e "${WHITE}🤖 智能部署特性已全部完成：${NC}"
    echo -e "${WHITE}  ✅ 智能检查并安装系统依赖 ${CYAN}(仅安装缺失的包)${NC}"
    echo -e "${WHITE}  ✅ 智能检查并安装Rust环境 ${CYAN}(跳过已有安装)${NC}"
    echo -e "${WHITE}  ✅ 智能编译项目 ${CYAN}(仅在源码更新时重新编译)${NC}"
    echo -e "${WHITE}  ✅ 智能配置服务 ${CYAN}(跳过已存在的服务)${NC}"
    echo -e "${WHITE}  ✅ 智能配置防火墙 ${CYAN}(仅添加缺失的规则)${NC}"
    echo -e "${WHITE}  ✅ 智能创建监控 ${CYAN}(避免重复创建)${NC}"
    echo -e "${WHITE}  ✅ 智能创建管理工具 ${CYAN}(仅创建缺失的脚本)${NC}"
    echo ""
    echo -e "${WHITE}📍 部署路径: ${CYAN}$DEPLOY_PATH${NC}"
    echo -e "${WHITE}👤 服务用户: ${CYAN}$SERVICE_USER${NC}"
    echo -e "${WHITE}🔄 开机自启: ${GREEN}已启用${NC}"
    echo -e "${WHITE}🛡️  故障重启: ${GREEN}已配置${NC}"
    echo -e "${WHITE}👁️  服务监控: ${GREEN}每2分钟检查${NC}"
    echo ""
    echo -e "${BLUE}🎯 服务管理命令:${NC}"
    echo -e "${BLUE}  启动服务: sudo systemctl start ${PROJECT_NAME}${NC}"
    echo -e "${BLUE}  停止服务: sudo systemctl stop ${PROJECT_NAME}${NC}"
    echo -e "${BLUE}  重启服务: sudo systemctl restart ${PROJECT_NAME}${NC}"
    echo -e "${BLUE}  查看状态: sudo systemctl status ${PROJECT_NAME}${NC}"
    echo -e "${BLUE}  查看日志: sudo journalctl -u ${PROJECT_NAME} -f${NC}"
    echo ""
    echo -e "${BLUE}🛠️  管理工具:${NC}"
    echo -e "${BLUE}  状态检查: $DEPLOY_PATH/status.sh${NC}"
    echo -e "${BLUE}  服务监控: $DEPLOY_PATH/monitor.sh${NC}"
    echo -e "${BLUE}  手动启动: $DEPLOY_PATH/start.sh${NC}"
    echo -e "${BLUE}  API测试: ./test-api.sh${NC}"
    echo ""
    echo -e "${WHITE}🌐 访问地址 (双端口架构):${NC}"
    echo -e "${WHITE}  Web管理界面: ${CYAN}http://your-server:1319${NC} ${YELLOW}(前端+API)${NC}"
    echo -e "${WHITE}  Pingora代理: ${CYAN}http://your-server:1911${NC} ${YELLOW}(反向代理核心)${NC}"
    echo ""
    echo -e "${WHITE}🗄️ 数据库状态 (强制要求):${NC}"
    if systemctl is-active mongod >/dev/null 2>&1 || systemctl is-active mongodb >/dev/null 2>&1; then
        echo -e "${WHITE}  数据库: ${GREEN}MongoDB 7.0+ (生产就绪)${NC}"
        echo -e "${WHITE}  连接: ${CYAN}mongodb://localhost:27017/sm_proxy${NC}"
        echo -e "${WHITE}  状态: ${GREEN}运行中${NC}"
        echo -e "${WHITE}  用途: ${CYAN}域名池、递归代理数据、用户认证等${NC}"
    else
        echo -e "${WHITE}  数据库: ${RED}MongoDB未运行${NC}"
        echo -e "${WHITE}  状态: ${RED}服务异常${NC}"
        echo -e "${WHITE}  影响: ${RED}系统无法正常工作${NC}"
        echo -e "${WHITE}  建议: ${YELLOW}立即检查MongoDB服务状态${NC}"
    fi
    echo ""
    echo -e "${YELLOW}🔐 安全提示:${NC}"
    echo -e "${WHITE}  首次登录: ${CYAN}admin${WHITE} / ${CYAN}admin888${WHITE} ${YELLOW}(请立即修改密码)${NC}"
    echo -e "${WHITE}  JWT密钥将在首次登录时自动生成${NC}"
    echo -e "${WHITE}  所有安全设置已自动配置${NC}"
    echo ""
    echo -e "${WHITE}🚀 高可用特性 ${CYAN}(系统重启后持续运行):${NC}"
    echo -e "${WHITE}  ✅ 后台服务运行 ${CYAN}(systemd管理)${NC}"
    echo -e "${WHITE}  ✅ 故障自动重启 ${CYAN}(Restart=always)${NC}"
    echo -e "${WHITE}  ✅ 开机自动运行 ${CYAN}(systemctl enable)${NC}"
    echo -e "${WHITE}  ✅ 服务健康监控 ${CYAN}(systemd定时器，每2分钟)${NC}"
    echo -e "${WHITE}  ✅ 重启后自动恢复 ${CYAN}(所有功能持续运行)${NC}"
    echo -e "${WHITE}  ✅ 资源限制保护 ${CYAN}(内存2G/CPU200%)${NC}"
    echo -e "${WHITE}  ✅ 安全沙箱运行 ${CYAN}(systemd安全特性)${NC}"
    echo ""
    echo -e "${WHITE}📝 下次更新 ${CYAN}(同样智能化):${NC}"
    echo -e "${WHITE}  1. 修改代码后直接运行: ${CYAN}sudo ./setup.sh${NC}"
    echo -e "${WHITE}  2. 脚本会自动重新编译和部署${NC}"
    echo -e "${WHITE}  3. 无需手动操作任何步骤${NC}"
    echo ""

    # 检查是否进行了性能优化，如果是则提醒重启
    if [ "$step_offset" -eq 1 ]; then
        echo -e "${YELLOW}🔄 性能优化重启提醒:${NC}"
        echo -e "${WHITE}  为了获得完整的性能优化效果，建议在方便时重启系统${NC}"
        echo -e "${CYAN}  重启命令: sudo reboot${NC}"
        echo -e "${WHITE}  重启后所有服务会自动启动${NC}"
        echo ""
    fi

    echo -e "${WHITE}🎯 真正的一键智能部署 - ${CYAN}零手动操作！${NC}"
}

# Screen 持久化执行管理
check_screen_support() {
    echo -e "${BLUE}🖥️  检查Screen持久化支持...${NC}"

    if ! command -v screen >/dev/null 2>&1; then
        echo -e "${RED}❌ Screen未安装，无法提供持久化执行${NC}"
        echo -e "${YELLOW}💡 Screen已包含在依赖安装中，应该会自动安装${NC}"
        return 1
    fi

    # 清理死亡的screen会话
    echo -e "${BLUE}🧹 清理死亡的Screen会话...${NC}"
    screen -wipe >/dev/null 2>&1 || true

    # 清理临时文件
    rm -f /tmp/sm_start_*.sh /tmp/sm_screen_start*.sh 2>/dev/null || true

    echo -e "${GREEN}✅ Screen支持已启用${NC}"
    return 0
}

# 检查是否在Screen会话中运行
is_in_screen() {
    [ -n "${STY:-}" ] || [ -n "${TMUX:-}" ]
}

# 检查是否有现有的安装会话
check_existing_session() {
    local screen_output=$(screen -list 2>/dev/null)

    if echo "$screen_output" | grep -q "$SCREEN_SESSION"; then
        echo -e "${YELLOW}⚠️  检测到现有的安装会话${NC}"
        echo -e "${BLUE}📋 现有会话状态:${NC}"
        echo "$screen_output" | grep "$SCREEN_SESSION"
        echo ""

        echo -e "${YELLOW}🤔 选择操作:${NC}"
        echo -e "${WHITE}  1) 终止并重新开始 (推荐)${NC}"
        echo -e "${WHITE}  2) 尝试连接现有会话${NC}"
        echo -e "${WHITE}  3) 查看安装日志${NC}"
        echo -e "${WHITE}  4) 退出${NC}"
        echo ""
        read -p "请选择 (1-4): " choice

        local session_id=$(echo "$screen_output" | grep "$SCREEN_SESSION" | awk '{print $1}')

        case $choice in
            1)
                echo -e "${YELLOW}🗑️  清理现有会话...${NC}"
                screen -S "$session_id" -X quit 2>/dev/null || true
                sleep 1
                screen -wipe >/dev/null 2>&1 || true
                echo -e "${GREEN}✅ 会话已清理，继续新安装${NC}"
                return 0
                ;;
            2)
                echo -e "${BLUE}🔗 尝试连接现有会话...${NC}"
                if screen -r "$session_id" 2>/dev/null; then
                    # 连接成功，不会到达这里
                    :
                else
                    echo -e "${RED}❌ 连接失败，自动清理并继续${NC}"
                    screen -wipe >/dev/null 2>&1 || true
                    return 0
                fi
                ;;
            3)
                echo -e "${BLUE}📄 安装日志:${NC}"
                if [ -f "$LOG_FILE" ]; then
                    echo -e "${CYAN}════════════════════════════════════════${NC}"
                    tail -30 "$LOG_FILE"
                    echo -e "${CYAN}════════════════════════════════════════${NC}"
                else
                    echo -e "${YELLOW}⚠️  日志文件不存在${NC}"
                fi
                echo ""
                echo -e "${CYAN}💡 按回车继续...${NC}"
                read
                return 0
                ;;
            4)
                echo -e "${BLUE}👋 退出安装${NC}"
                exit 0
                ;;
            *)
                echo -e "${YELLOW}⚠️  无效选择，默认清理并重新开始${NC}"
                screen -S "$session_id" -X quit 2>/dev/null || true
                screen -wipe >/dev/null 2>&1 || true
                return 0
                ;;
        esac
    fi
    return 0
}

# 在Screen中启动安装
start_in_screen() {
    echo -e "${BLUE}🖥️  启动Screen持久化安装...${NC}"
    echo -e "${CYAN}📋 Screen会话信息:${NC}"
    echo -e "${WHITE}  会话名称: ${CYAN}$SCREEN_SESSION${NC}"
    echo -e "${WHITE}  日志文件: ${CYAN}$LOG_FILE${NC}"
    echo -e "${WHITE}  重连命令: ${CYAN}screen -r $SCREEN_SESSION${NC}"
    echo ""
    echo -e "${GREEN}✅ SSH断开后安装将继续进行${NC}"
    echo -e "${YELLOW}💡 您可以安全地关闭SSH连接${NC}"
    echo -e "${YELLOW}💡 重新连接后使用: screen -r $SCREEN_SESSION${NC}"
    echo ""

    # 创建日志文件
    touch "$LOG_FILE"
    chmod 644 "$LOG_FILE"

    # 创建简单的启动脚本
    local start_script="/tmp/sm_start_$$.sh"
    cat > "$start_script" << 'SCRIPT_EOF'
#!/bin/bash
# SM安装启动脚本

# 设置日志
LOG_FILE="/tmp/sm_install.log"
exec > >(tee -a "$LOG_FILE") 2>&1

echo "🚀 SM智能代理系统 - Screen持久化安装开始"
echo "时间: $(date)"
echo "会话: sm-install"
echo "=================================="

# 切换到项目目录
# 获取当前脚本的实际路径
CURRENT_SCRIPT_DIR="$(cd "$(dirname "\$0")" && pwd)"
echo "📍 当前脚本目录: \$CURRENT_SCRIPT_DIR"

# 尝试多个可能的路径
if [ -f "\$CURRENT_SCRIPT_DIR/setup.sh" ]; then
    cd "\$CURRENT_SCRIPT_DIR"
    echo "✅ 使用脚本目录: \$CURRENT_SCRIPT_DIR"
elif [ -f "/root/sm/setup.sh" ]; then
    cd "/root/sm"
    echo "✅ 使用项目目录: /root/sm"
elif [ -f "$SCRIPT_DIR/setup.sh" ]; then
    cd "$SCRIPT_DIR"
    echo "✅ 使用检测目录: $SCRIPT_DIR"
else
    echo "❌ 找不到setup.sh文件"
    echo "📍 当前目录: \$(pwd)"
    echo "📍 脚本目录: \$CURRENT_SCRIPT_DIR"
    echo "📍 检测目录: $SCRIPT_DIR"
    ls -la
    exit 1
fi

echo "📍 工作目录: \$(pwd)"
echo "📋 检查文件:"
ls -la setup.sh 2>/dev/null || echo "❌ setup.sh 不存在"

# 确保在交互模式下执行
export FORCE_INTERACTIVE=1

# 执行安装 - 避免循环调用
echo "📋 Screen持久化模式 - 执行完整安装流程..."

# 检查脚本权限和存在性
if [ -f "./setup.sh" ]; then
    if [ ! -x "./setup.sh" ]; then
        chmod +x ./setup.sh
        echo "✅ 设置setup.sh执行权限"
    fi

    # 直接执行setup.sh，但跳过Screen检查
    export SKIP_SCREEN_CHECK=1
    export FORCE_INTERACTIVE=1
    echo "🚀 执行setup.sh..."
    echo "🔧 环境变量:"
    echo "   SKIP_SCREEN_CHECK=\$SKIP_SCREEN_CHECK"
    echo "   FORCE_INTERACTIVE=\$FORCE_INTERACTIVE"
    echo "   PWD=\$(pwd)"

    # 使用exec确保不会返回到这个脚本
    exec ./setup.sh
else
    echo "❌ 错误: setup.sh文件不存在"
    echo "📍 当前目录: \$(pwd)"
    echo "📋 目录内容:"
    ls -la
    exit 1
fi

echo "=================================="
echo "安装完成时间: $(date)"
echo "会话将在30秒后关闭，按Ctrl+C保持打开"

# 倒计时
for i in {30..1}; do
    echo -ne "\r会话将在 $i 秒后关闭..."
    sleep 1
done
echo ""
echo "会话已关闭"
SCRIPT_EOF

    chmod +x "$start_script"

    # 启动Screen会话
    echo -e "${BLUE}🚀 启动Screen会话...${NC}"
    screen -dmS "$SCREEN_SESSION" "$start_script"

    # 等待会话启动
    sleep 2

    # 验证会话是否启动成功
    if screen -list 2>/dev/null | grep -q "$SCREEN_SESSION"; then
        echo -e "${GREEN}✅ Screen会话启动成功${NC}"
        echo -e "${BLUE}🔗 连接到安装会话...${NC}"
        echo -e "${CYAN}💡 使用 Ctrl+A, D 可以分离会话而不中断安装${NC}"
        echo ""

        # 清理启动脚本
        rm -f "$start_script"

        # 连接到Screen会话
        exec screen -r "$SCREEN_SESSION"
    else
        echo -e "${RED}❌ Screen会话启动失败${NC}"
        echo -e "${YELLOW}💡 回退到直接执行模式${NC}"

        # 清理启动脚本
        rm -f "$start_script"

        # 直接执行安装
        main_without_screen "$@"
    fi
}



# 主安装流程（在Screen中执行）
main_installation_process() {
    echo -e "${GREEN}🚀 开始主安装流程...${NC}"

    # 执行原来的main函数逻辑
    main_without_screen
}

# 原main函数重命名
main_without_screen() {
    echo -e "${CYAN}🔍 DEBUG: 进入 main_without_screen 函数${NC}"

    # 检查是否强制使用原版本
    if [ "${USE_LEGACY_DEPLOY:-}" = "1" ]; then
        echo -e "${YELLOW}🔄 使用传统部署流程...${NC}"
        echo -e "${CYAN}🔍 DEBUG: 调用 main_deploy${NC}"
        main_deploy "$@"
    else
        echo -e "${GREEN}⚡ 使用优化部署流程...${NC}"
        echo -e "${CYAN}🔍 DEBUG: 调用 main_deploy_optimized${NC}"
        main_deploy_optimized "$@"
    fi

    echo -e "${CYAN}🔍 DEBUG: main_without_screen 函数执行完成${NC}"
}

# Screen管理功能
screen_manager() {
    case "${1:-help}" in
        status)
            echo -e "${BLUE}📊 Screen会话状态:${NC}"
            if screen -list 2>/dev/null | grep -q "$SCREEN_SESSION"; then
                echo -e "${GREEN}✅ 安装会话: 运行中${NC}"
                screen -list | grep "$SCREEN_SESSION"
                echo -e "${CYAN}💡 连接命令: screen -r sm-install${NC}"
            else
                echo -e "${YELLOW}⚠️  没有运行的安装会话${NC}"
            fi

            if [ -f "$LOG_FILE" ]; then
                echo -e "${GREEN}✅ 日志文件: 存在${NC}"
                echo -e "${WHITE}   大小: $(du -h "$LOG_FILE" | cut -f1)${NC}"
                echo -e "${CYAN}💡 查看日志: tail -f $LOG_FILE${NC}"
            else
                echo -e "${YELLOW}⚠️  日志文件: 不存在${NC}"
            fi
            ;;
        connect)
            if screen -list 2>/dev/null | grep -q "$SCREEN_SESSION"; then
                echo -e "${BLUE}🔗 连接到安装会话...${NC}"
                exec screen -r "$SCREEN_SESSION"
            else
                echo -e "${RED}❌ 没有运行的安装会话${NC}"
            fi
            ;;
        log)
            if [ -f "$LOG_FILE" ]; then
                echo -e "${BLUE}📄 安装日志:${NC}"
                tail -50 "$LOG_FILE"
            else
                echo -e "${YELLOW}⚠️  日志文件不存在${NC}"
            fi
            ;;
        clean)
            echo -e "${BLUE}🧹 清理Screen会话和日志...${NC}"
            screen -wipe >/dev/null 2>&1 || true
            if screen -list 2>/dev/null | grep -q "$SCREEN_SESSION"; then
                local session_id=$(screen -list 2>/dev/null | grep "$SCREEN_SESSION" | awk '{print $1}')
                screen -S "$session_id" -X quit 2>/dev/null || true
            fi
            rm -f "$LOG_FILE" /tmp/sm_start_*.sh 2>/dev/null || true
            echo -e "${GREEN}✅ 清理完成${NC}"
            ;;
        help|*)
            echo -e "${GREEN}🖥️  SM Screen管理${NC}"
            echo -e "${WHITE}用法: $0 [管理命令]${NC}"
            echo ""
            echo -e "${CYAN}管理命令:${NC}"
            echo -e "${WHITE}  status  - 查看Screen会话状态${NC}"
            echo -e "${WHITE}  connect - 连接到安装会话${NC}"
            echo -e "${WHITE}  log     - 查看安装日志${NC}"
            echo -e "${WHITE}  clean   - 清理会话和日志${NC}"
            echo ""
            echo -e "${YELLOW}示例:${NC}"
            echo -e "${WHITE}  $0 status   # 查看状态${NC}"
            echo -e "${WHITE}  $0 connect  # 连接会话${NC}"
            echo -e "${WHITE}  $0 clean    # 清理所有${NC}"
            ;;
    esac
}

# 检查是否以正确方式运行
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    # 检查是否是管理命令
    case "${1:-}" in
        status|connect|log|clean)
            screen_manager "$1"
            exit 0
            ;;
    esac

    # 检查是否跳过Screen检查（用于Screen内部调用）
    if [ "${SKIP_SCREEN_CHECK:-}" = "1" ]; then
        echo -e "${BLUE}🖥️  Screen内部执行模式${NC}"
        main_without_screen "$@"
    # 检查是否已经在Screen中
    elif is_in_screen; then
        echo -e "${BLUE}🖥️  检测到已在Screen/Tmux会话中${NC}"
        echo -e "${YELLOW}💡 Screen模式下将显示完整的交互提示${NC}"
        main_without_screen "$@"
    else
        # 检查Screen支持
        if check_screen_support; then
            # 检查现有会话
            check_existing_session

            # 询问是否使用Screen持久化
            echo -e "${YELLOW}🤔 是否启用Screen持久化执行？${NC}"
            echo -e "${WHITE}  启用后即使SSH断开，安装也会继续进行${NC}"
            echo -e "${WHITE}  推荐在网络不稳定或长时间编译时使用${NC}"
            echo ""
            echo -e "${WHITE}  y) 是，启用Screen持久化 (推荐)${NC}"
            echo -e "${WHITE}  n) 否，直接在当前终端执行${NC}"
            echo ""
            read -p "请选择 (y/n): " use_screen

            case $use_screen in
                [Yy]*)
                    start_in_screen
                    ;;
                [Nn]*)
                    echo -e "${BLUE}🖥️  在当前终端执行安装${NC}"
                    main_without_screen "$@"
                    ;;
                *)
                    echo -e "${YELLOW}⚠️  无效输入，默认启用Screen持久化${NC}"
                    start_in_screen
                    ;;
            esac
        else
            echo -e "${YELLOW}⚠️  Screen不可用，在当前终端执行安装${NC}"
            main_without_screen "$@"
        fi
    fi
fi
