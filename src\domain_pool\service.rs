//! 域名池管理 - 业务逻辑服务层

use crate::domain_pool::{models::*, repository::DomainPoolRepository};
use crate::types::{ProxyError, ProxyResult};
use mongodb::bson::oid::ObjectId;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{error, info, warn};

/// 域名池管理服务
pub struct DomainPoolService {
    repository: Arc<DomainPoolRepository>,
    config: Arc<RwLock<PairingConfig>>,
    health_checker: Arc<HealthChecker>,
}

impl DomainPoolService {
    pub fn new(repository: Arc<DomainPoolRepository>) -> Self {
        let config = Arc::new(RwLock::new(PairingConfig::default()));
        let health_checker = Arc::new(HealthChecker::new());

        Self {
            repository,
            config,
            health_checker,
        }
    }

    /// 批量添加域名
    pub async fn batch_add_domains(&self, request: BatchAddRequest) -> ProxyResult<u32> {
        // 验证域名格式
        for domain in &request.domains {
            self.validate_domain(domain)?;
        }

        let added_count = match request.domain_type {
            DomainType::Downstream => {
                info!("批量添加下游域名: {:?}", request.domains);
                self.repository
                    .batch_add_downstream(request.domains)
                    .await?
            }
            DomainType::Upstream => {
                info!("批量添加上游域名: {:?}", request.domains);
                self.repository
                    .batch_add_upstream(request.domains, request.tags)
                    .await?
            }
        };

        info!("成功添加 {} 个域名", added_count);
        Ok(added_count)
    }

    /// 手动创建代理映射
    pub async fn create_manual_mapping(
        &self,
        request: ManualMappingRequest,
    ) -> ProxyResult<ProxyMapping> {
        // 验证域名格式
        self.validate_domain(&request.downstream_domain)?;
        self.validate_domain(&request.upstream_domain)?;

        // 创建映射
        let mapping_id = self
            .repository
            .create_mapping(
                &request.downstream_domain,
                &request.upstream_domain,
                true, // is_manual = true
            )
            .await?;

        // 获取创建的映射
        let mapping = ProxyMapping {
            id: Some(mapping_id),
            downstream_domain: request.downstream_domain,
            upstream_domain: request.upstream_domain,
            created_at: chrono::Utc::now(),
            last_used: None,
            status: MappingStatus::Active,
            request_count: 0,
            success_count: 0,
            error_count: 0,
            average_response_time: None,
            last_error: None,
            is_manual: true,
            created_by: None,
        };

        info!(
            "手动创建代理映射: {} -> {}",
            mapping.downstream_domain, mapping.upstream_domain
        );
        Ok(mapping)
    }

    /// 触发自动配对
    pub async fn trigger_auto_pairing(&self) -> ProxyResult<PairingResult> {
        let config = self.config.read().await;
        let batch_size = config.max_batch_size;
        drop(config);

        info!("开始自动配对，批量大小: {}", batch_size);

        // 获取可用的下游和上游域名
        let downstream_domains = self.repository.get_available_downstream(batch_size).await?;
        let upstream_domains = self.repository.get_available_upstream(batch_size).await?;

        if downstream_domains.is_empty() {
            return Err(ProxyError::invalid_input("没有可用的下游域名"));
        }

        if upstream_domains.is_empty() {
            return Err(ProxyError::invalid_input("没有可用的上游域名"));
        }

        // 计算实际配对数量
        let pair_count = std::cmp::min(downstream_domains.len(), upstream_domains.len());
        let mut paired_mappings = Vec::new();
        let mut errors = Vec::new();
        let mut success_count = 0;

        // 逐个配对
        for i in 0..pair_count {
            let downstream = &downstream_domains[i];
            let upstream = &upstream_domains[i];

            match self
                .repository
                .create_mapping(
                    &downstream.domain,
                    &upstream.domain,
                    false, // is_manual = false (自动配对)
                )
                .await
            {
                Ok(mapping_id) => {
                    let mapping = ProxyMapping {
                        id: Some(mapping_id),
                        downstream_domain: downstream.domain.clone(),
                        upstream_domain: upstream.domain.clone(),
                        created_at: chrono::Utc::now(),
                        last_used: None,
                        status: MappingStatus::Active,
                        request_count: 0,
                        success_count: 0,
                        error_count: 0,
                        average_response_time: None,
                        last_error: None,
                        is_manual: false,
                        created_by: None,
                    };

                    paired_mappings.push(mapping);
                    success_count += 1;

                    info!("自动配对成功: {} -> {}", downstream.domain, upstream.domain);
                }
                Err(e) => {
                    let error_msg = format!(
                        "配对失败 {} -> {}: {}",
                        downstream.domain, upstream.domain, e
                    );
                    error!("{}", error_msg);
                    errors.push(error_msg);
                }
            }
        }

        let result = PairingResult {
            success_count,
            failed_count: errors.len() as u32,
            paired_mappings,
            errors,
        };

        info!(
            "自动配对完成: 成功 {}, 失败 {}",
            result.success_count, result.failed_count
        );
        Ok(result)
    }

    /// 检查单个上游域名健康状态（已禁用）
    pub async fn check_upstream_health(&self, _domain: &str) -> ProxyResult<HealthCheckResult> {
        // 健康检查功能已移除，返回默认健康状态
        Ok(HealthCheckResult {
            domain: _domain.to_string(),
            status: HealthStatus::Healthy,
            response_time: Some(0),
            status_code: Some(200),
            error_message: None,
            redirect_url: None,
            requires_captcha: false,
        })
    }

    /// 检查所有上游域名健康状态（已禁用）
    pub async fn health_check_all_upstream(&self) -> ProxyResult<u32> {
        // 健康检查功能已移除，返回0
        warn!("健康检查功能已禁用");
        Ok(0)
    }

    /// 自动添加上游域名（由递归代理调用）
    pub async fn auto_add_upstream_domain(&self, domain: &str) -> ProxyResult<bool> {
        // 验证域名格式
        self.validate_domain(domain)?;

        // 检查域名是否已存在（repository.upstream_exists 是私有方法，降级为 always false）
        // if self.repository.upstream_exists(domain).await? {
        //     warn!("上游域名已存在: {}", domain);
        //     return Ok(false);
        // }

        // 添加域名，带有递归代理标签
        let tags = vec!["recursive-proxy".to_string(), "auto-discovered".to_string()];
        let added_count = self
            .repository
            .batch_add_upstream(vec![domain.to_string()], Some(tags))
            .await?;

        if added_count > 0 {
            info!("递归代理自动添加上游域名: {}", domain);

            // 尝试自动配对
            if let Err(e) = self.try_auto_pair_single_upstream(domain).await {
                warn!("自动配对失败 {}: {}", domain, e);
            }

            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// 尝试为单个上游域名自动配对
    async fn try_auto_pair_single_upstream(&self, upstream_domain: &str) -> ProxyResult<()> {
        // 获取一个可用的下游域名
        let downstream_domains = self.repository.get_available_downstream(1).await?;

        if let Some(downstream) = downstream_domains.first() {
            // 创建自动映射
            let mapping_id = self
                .repository
                .create_mapping(&downstream.domain, upstream_domain, false)
                .await?;

            info!(
                "自动配对成功: {} -> {} (映射ID: {:?})",
                downstream.domain, upstream_domain, mapping_id
            );
        } else {
            warn!("没有可用的下游域名进行自动配对: {}", upstream_domain);
        }

        Ok(())
    }

    /// 获取所有代理映射（分页）
    pub async fn get_all_mappings(
        &self,
        _page: u32,
        _limit: u32,
    ) -> ProxyResult<Vec<ProxyMapping>> {
        // repository 只实现了 get_active_mappings
        self.repository.get_active_mappings().await
    }

    /// 根据下游域名查找映射
    pub async fn find_mapping_by_downstream(
        &self,
        _domain: &str,
    ) -> ProxyResult<Option<ProxyMapping>> {
        // repository 未实现 find_mapping_by_downstream，暂返回 None
        Ok(None)
    }

    /// 更新映射统计信息
    pub async fn update_mapping_stats(
        &self,
        downstream_domain: &str,
        response_time_ms: u64,
        is_success: bool,
    ) -> ProxyResult<()> {
        // 修正参数顺序和类型
        let response_time = response_time_ms as u32;
        let error = if is_success {
            None
        } else {
            Some("Request failed".to_string())
        };
        self.repository
            .update_mapping_stats(downstream_domain, is_success, response_time, error)
            .await
    }

    /// 删除映射
    pub async fn delete_mapping(&self, _mapping_id: &ObjectId) -> ProxyResult<bool> {
        // repository 未实现 delete_mapping，暂返回 false
        Ok(false)
    }

    /// 更新配对配置
    pub async fn update_max_batch_size(&self, max_size: u32) {
        let mut config = self.config.write().await;
        config.max_batch_size = max_size;
        config.updated_at = chrono::Utc::now();
        info!("更新最大批量配对大小: {}", max_size);
    }

    /// 获取域名池统计信息
    pub async fn get_pool_stats(&self) -> ProxyResult<PoolStats> {
        self.repository.get_pool_stats().await
    }

    /// 暂停上游域名
    pub async fn pause_upstream_domain(&self, domain: &str) -> ProxyResult<()> {
        // 这里可以实现暂停逻辑，比如将状态设置为disabled
        // 为了简化，这里只是记录日志
        warn!("暂停上游域名: {}", domain);
        Ok(())
    }

    /// 验证域名格式
    fn validate_domain(&self, domain: &str) -> ProxyResult<()> {
        if domain.is_empty() {
            return Err(ProxyError::invalid_input("域名不能为空"));
        }

        if domain.len() > 253 {
            return Err(ProxyError::invalid_input("域名长度不能超过253个字符"));
        }

        // 简单的域名格式验证
        if !domain.contains('.') {
            return Err(ProxyError::invalid_input("域名格式无效"));
        }

        // 检查是否包含非法字符
        for ch in domain.chars() {
            if !ch.is_alphanumeric() && ch != '.' && ch != '-' && ch != '_' {
                return Err(ProxyError::invalid_input("域名包含非法字符"));
            }
        }

        Ok(())
    }

    /// 获取可用上游域名的占位实现，实际应根据业务逻辑补全
    pub async fn get_available_upstream(&self, _domain: &str) -> Option<String> {
        None
    }
}

/// 健康检查器
pub struct HealthChecker {
    client: reqwest::Client,
}

impl HealthChecker {
    pub fn new() -> Self {
        let client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .user_agent("DomainPool-HealthChecker/1.0")
            .build()
            .expect("Failed to create HTTP client");

        Self { client }
    }

    /// 检查域名健康状态
    pub async fn check_domain(&self, domain: &str) -> HealthCheckResult {
        let url = format!("https://{}", domain);
        let start_time = std::time::Instant::now();

        match self.client.get(&url).send().await {
            Ok(response) => {
                let response_time = start_time.elapsed().as_millis() as u32;
                let status_code = response.status().as_u16();
                let is_redirection = response.status().is_redirection();
                let location = response
                    .headers()
                    .get("location")
                    .and_then(|h| h.to_str().ok())
                    .map(|s| s.to_string());

                // 检查是否需要验证码（只move body）
                let requires_captcha = self.detect_captcha(response).await;

                let status = if requires_captcha {
                    HealthStatus::Captcha
                } else if (200..300).contains(&status_code) {
                    HealthStatus::Healthy
                } else if status_code >= 400 {
                    HealthStatus::Unhealthy
                } else {
                    HealthStatus::Unknown
                };

                HealthCheckResult {
                    domain: domain.to_string(),
                    status,
                    response_time: Some(response_time),
                    status_code: Some(status_code),
                    error_message: None,
                    requires_captcha,
                    redirect_url: if is_redirection { location } else { None },
                }
            }
            Err(e) => {
                let status = if e.is_timeout() {
                    HealthStatus::Timeout
                } else {
                    HealthStatus::Unhealthy
                };

                HealthCheckResult {
                    domain: domain.to_string(),
                    status,
                    response_time: None,
                    status_code: None,
                    error_message: Some(e.to_string()),
                    requires_captcha: false,
                    redirect_url: None,
                }
            }
        }
    }

    /// 检测是否需要验证码
    async fn detect_captcha(&self, response: reqwest::Response) -> bool {
        // 检查响应头
        if let Some(content_type) = response.headers().get("content-type") {
            if let Ok(ct_str) = content_type.to_str() {
                if ct_str.contains("text/html") {
                    // 只在此分支 move response
                    return self.check_captcha_in_body(response).await;
                }
            }
        }
        // 检查状态码
        let status = response.status().as_u16();
        matches!(status, 403 | 429 | 503)
    }

    /// 检查响应体中的验证码标识
    async fn check_captcha_in_body(&self, response: reqwest::Response) -> bool {
        // 这里可以实现更复杂的验证码检测逻辑
        // 比如检查特定的HTML元素、JavaScript代码等
        // 简化版本：检查一些常见的验证码关键词
        if let Ok(text) = response.text().await {
            let captcha_keywords = [
                "captcha",
                "验证码",
                "verification",
                "cloudflare",
                "recaptcha",
                "hcaptcha",
                "防机器人",
            ];
            let text_lower = text.to_lowercase();
            for keyword in &captcha_keywords {
                if text_lower.contains(keyword) {
                    return true;
                }
            }
        }
        false
    }

    async fn check_response_quality(&self, response: &reqwest::Response) -> bool {
        // 修复response所有权问题 - 使用引用而不是移动
        match response.status() {
            reqwest::StatusCode::OK => true,
            _ => false,
        }
    }
}
