# Rust编译输出
/target/
Cargo.lock

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 系统文件
.DS_Store
Thumbs.db

# 日志文件内容（保留目录结构）
logs/*.log
logs/*.log.*
logs/*.gz
logs/app.log*
logs/security.log*
logs/audit.log*
logs/error.log*
logs/access.log*
logs/cache.log*
logs/debug.log*

# 环境配置文件
.env
.env.local
.env.production

# 运行时数据
data/
*.pid
*.sock

# 备份文件
*.backup
*.bak
*.tmp

# SSL证书文件
*.pem
*.key
*.crt
*.p12

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 临时文件
temp/
tmp/