use crate::error::AppResult;
use crate::types::Database;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use tokio::time::interval;
use tracing::{error, info, warn};

/// 健康检查器
#[derive(Clone)]
pub struct HealthChecker {
    database: Arc<dyn Database>,
    last_check: Arc<tokio::sync::RwLock<Instant>>,
    check_interval: Duration,
    config: HealthConfig,
}

/// 健康检查配置
#[derive(Debug, Clone)]
pub struct HealthConfig {
    /// 检查间隔
    pub check_interval: Duration,
    /// 数据库连接超时
    pub db_timeout: Duration,
    /// 内存使用阈值 (MB)
    pub memory_threshold_mb: u64,
    /// CPU使用阈值 (%)
    pub cpu_threshold_percent: f64,
    /// 磁盘使用阈值 (%)
    pub disk_threshold_percent: f64,
    /// 是否启用自动恢复
    pub auto_recovery_enabled: bool,
}

impl Default for HealthConfig {
    fn default() -> Self {
        Self {
            check_interval: Duration::from_secs(30),
            db_timeout: Duration::from_secs(5),
            memory_threshold_mb: 1024, // 1GB
            cpu_threshold_percent: 80.0,
            disk_threshold_percent: 90.0,
            auto_recovery_enabled: true,
        }
    }
}

/// 健康状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum HealthStatus {
    Healthy,
    Warning,
    Critical,
    Unknown,
}

impl std::fmt::Display for HealthStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            HealthStatus::Healthy => write!(f, "Healthy"),
            HealthStatus::Warning => write!(f, "Warning"),
            HealthStatus::Critical => write!(f, "Critical"),
            HealthStatus::Unknown => write!(f, "Unknown"),
        }
    }
}

/// 组件健康信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentHealth {
    pub name: String,
    pub status: HealthStatus,
    pub message: String,
    pub last_check: SystemTime,
    pub response_time_ms: u64,
    pub details: Option<serde_json::Value>,
}

/// 系统健康报告
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthReport {
    pub overall_status: HealthStatus,
    pub timestamp: SystemTime,
    pub uptime_seconds: u64,
    pub components: Vec<ComponentHealth>,
    pub system_metrics: SystemMetrics,
    pub recommendations: Vec<String>,
}

/// 系统指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemMetrics {
    pub memory_usage_mb: u64,
    pub memory_usage_percent: f64,
    pub cpu_usage_percent: f64,
    pub disk_usage_percent: f64,
    pub active_connections: u64,
    pub request_rate_per_minute: f64,
    pub error_rate_percent: f64,
}

impl Default for SystemMetrics {
    fn default() -> Self {
        Self {
            memory_usage_mb: 0,
            memory_usage_percent: 0.0,
            cpu_usage_percent: 0.0,
            disk_usage_percent: 0.0,
            active_connections: 0,
            request_rate_per_minute: 0.0,
            error_rate_percent: 0.0,
        }
    }
}

impl HealthChecker {
    pub fn new(database: Arc<dyn Database>) -> Self {
        Self {
            database,
            last_check: Arc::new(tokio::sync::RwLock::new(Instant::now())),
            check_interval: Duration::from_secs(30),
            config: HealthConfig::default(),
        }
    }

    pub fn with_config(database: Arc<dyn Database>, config: HealthConfig) -> Self {
        let check_interval = config.check_interval;
        Self {
            database,
            last_check: Arc::new(tokio::sync::RwLock::new(Instant::now())),
            check_interval,
            config,
        }
    }

    /// 启动健康检查监控
    pub async fn start_monitoring(&self) -> AppResult<()> {
        let mut interval = interval(self.check_interval);
        let checker = self.clone();

        tokio::spawn(async move {
            loop {
                interval.tick().await;
                
                match checker.perform_health_check().await {
                    Ok(report) => {
                        // 更新最后检查时间
                        {
                            let mut last_check = checker.last_check.write().await;
                            *last_check = Instant::now();
                        }

                        // 根据健康状态采取行动
                        match report.overall_status {
                            HealthStatus::Critical => {
                                error!("🚨 系统健康状态: 严重 - {}", 
                                    report.components.iter()
                                        .filter(|c| c.status == HealthStatus::Critical)
                                        .map(|c| &c.name)
                                        .collect::<Vec<_>>()
                                        .join(", ")
                                );
                                
                                if checker.config.auto_recovery_enabled {
                                    if let Err(e) = checker.attempt_auto_recovery(&report).await {
                                        error!("自动恢复失败: {}", e);
                                    }
                                }
                            }
                            HealthStatus::Warning => {
                                warn!("⚠️ 系统健康状态: 警告 - 建议检查系统资源");
                            }
                            HealthStatus::Healthy => {
                                info!("✅ 系统健康状态: 正常");
                            }
                            HealthStatus::Unknown => {
                                warn!("❓ 系统健康状态: 未知 - 无法获取完整健康信息");
                            }
                        }
                    }
                    Err(e) => {
                        error!("健康检查失败: {}", e);
                    }
                }
            }
        });

        info!("🏥 健康检查监控已启动，检查间隔: {:?}", self.check_interval);
        Ok(())
    }

    /// 执行健康检查
    pub async fn perform_health_check(&self) -> AppResult<HealthReport> {
        let start_time = Instant::now();
        let mut components = Vec::new();
        let mut recommendations = Vec::new();

        // 检查数据库健康
        components.push(self.check_database_health().await);

        // 检查系统指标
        let system_metrics = self.collect_system_metrics().await;
        
        // 检查内存使用
        if system_metrics.memory_usage_percent > 90.0 {
            components.push(ComponentHealth {
                name: "Memory".to_string(),
                status: HealthStatus::Critical,
                message: format!("内存使用率过高: {:.1}%", system_metrics.memory_usage_percent),
                last_check: SystemTime::now(),
                response_time_ms: 0,
                details: None,
            });
            recommendations.push("考虑增加内存或优化内存使用".to_string());
        } else if system_metrics.memory_usage_percent > 80.0 {
            components.push(ComponentHealth {
                name: "Memory".to_string(),
                status: HealthStatus::Warning,
                message: format!("内存使用率较高: {:.1}%", system_metrics.memory_usage_percent),
                last_check: SystemTime::now(),
                response_time_ms: 0,
                details: None,
            });
            recommendations.push("监控内存使用趋势".to_string());
        } else {
            components.push(ComponentHealth {
                name: "Memory".to_string(),
                status: HealthStatus::Healthy,
                message: format!("内存使用正常: {:.1}%", system_metrics.memory_usage_percent),
                last_check: SystemTime::now(),
                response_time_ms: 0,
                details: None,
            });
        }

        // 检查CPU使用
        if system_metrics.cpu_usage_percent > self.config.cpu_threshold_percent {
            components.push(ComponentHealth {
                name: "CPU".to_string(),
                status: HealthStatus::Warning,
                message: format!("CPU使用率较高: {:.1}%", system_metrics.cpu_usage_percent),
                last_check: SystemTime::now(),
                response_time_ms: 0,
                details: None,
            });
            recommendations.push("检查CPU密集型进程".to_string());
        } else {
            components.push(ComponentHealth {
                name: "CPU".to_string(),
                status: HealthStatus::Healthy,
                message: format!("CPU使用正常: {:.1}%", system_metrics.cpu_usage_percent),
                last_check: SystemTime::now(),
                response_time_ms: 0,
                details: None,
            });
        }

        // 确定整体健康状态
        let overall_status = self.determine_overall_status(&components);

        let uptime_seconds = start_time.elapsed().as_secs();

        Ok(HealthReport {
            overall_status,
            timestamp: SystemTime::now(),
            uptime_seconds,
            components,
            system_metrics,
            recommendations,
        })
    }

    /// 检查数据库健康
    async fn check_database_health(&self) -> ComponentHealth {
        let start_time = Instant::now();
        
        match tokio::time::timeout(self.config.db_timeout, self.database.health_check()).await {
            Ok(Ok(health)) => ComponentHealth {
                name: "Database".to_string(),
                status: HealthStatus::Healthy,
                message: "数据库连接正常".to_string(),
                last_check: SystemTime::now(),
                response_time_ms: start_time.elapsed().as_millis() as u64,
                details: Some(serde_json::to_value(health).unwrap_or_default()),
            },
            Ok(Err(e)) => ComponentHealth {
                name: "Database".to_string(),
                status: HealthStatus::Critical,
                message: format!("数据库错误: {}", e),
                last_check: SystemTime::now(),
                response_time_ms: start_time.elapsed().as_millis() as u64,
                details: None,
            },
            Err(_) => ComponentHealth {
                name: "Database".to_string(),
                status: HealthStatus::Critical,
                message: "数据库连接超时".to_string(),
                last_check: SystemTime::now(),
                response_time_ms: self.config.db_timeout.as_millis() as u64,
                details: None,
            },
        }
    }

    /// 收集系统指标
    async fn collect_system_metrics(&self) -> SystemMetrics {
        // 简化的系统指标收集
        // 在实际实现中，这里应该使用系统API获取真实的指标
        SystemMetrics {
            memory_usage_mb: self.get_memory_usage().await.unwrap_or(0),
            memory_usage_percent: self.get_memory_usage_percent().await.unwrap_or(0.0),
            cpu_usage_percent: self.get_cpu_usage().await.unwrap_or(0.0),
            disk_usage_percent: self.get_disk_usage().await.unwrap_or(0.0),
            active_connections: 0, // 需要从代理服务获取
            request_rate_per_minute: 0.0, // 需要从统计服务获取
            error_rate_percent: 0.0, // 需要从错误统计获取
        }
    }

    /// 获取内存使用量 (MB)
    async fn get_memory_usage(&self) -> AppResult<u64> {
        // 简化实现，返回模拟值
        // 实际实现应该使用系统API
        Ok(256) // 256MB
    }

    /// 获取内存使用百分比
    async fn get_memory_usage_percent(&self) -> AppResult<f64> {
        // 简化实现
        Ok(45.0) // 45%
    }

    /// 获取CPU使用百分比
    async fn get_cpu_usage(&self) -> AppResult<f64> {
        // 简化实现
        Ok(25.0) // 25%
    }

    /// 获取磁盘使用百分比
    async fn get_disk_usage(&self) -> AppResult<f64> {
        // 简化实现
        Ok(60.0) // 60%
    }

    /// 确定整体健康状态
    fn determine_overall_status(&self, components: &[ComponentHealth]) -> HealthStatus {
        if components.iter().any(|c| c.status == HealthStatus::Critical) {
            HealthStatus::Critical
        } else if components.iter().any(|c| c.status == HealthStatus::Warning) {
            HealthStatus::Warning
        } else if components.iter().all(|c| c.status == HealthStatus::Healthy) {
            HealthStatus::Healthy
        } else {
            HealthStatus::Unknown
        }
    }

    /// 尝试自动恢复
    async fn attempt_auto_recovery(&self, report: &HealthReport) -> AppResult<()> {
        info!("🔧 尝试自动恢复...");

        for component in &report.components {
            if component.status == HealthStatus::Critical {
                match component.name.as_str() {
                    "Database" => {
                        warn!("数据库连接异常，尝试重新连接...");
                        // 这里可以实现数据库重连逻辑
                    }
                    "Memory" => {
                        warn!("内存使用过高，尝试清理缓存...");
                        // 这里可以实现内存清理逻辑
                        // 例如：清理应用缓存、触发GC等
                    }
                    _ => {
                        warn!("组件 {} 状态异常，无自动恢复策略", component.name);
                    }
                }
            }
        }

        Ok(())
    }

    /// 获取最后检查时间
    pub async fn get_last_check_time(&self) -> Instant {
        *self.last_check.read().await
    }

    /// 更新配置
    pub fn update_config(&mut self, config: HealthConfig) {
        self.check_interval = config.check_interval;
        self.config = config;
    }
}
