/**
 * 域名分组管理器
 * 负责域名分组的增删改查、域名管理等功能
 */
class DomainGroupManager {
    constructor() {
        this.groups = [];
        this.filteredGroups = [];
        this.currentFilters = {
            status: '',
            search: ''
        };

        this.init();
    }

    init() {
        // 监听认证状态变化
        document.addEventListener('authenticated', () => {
            this.loadGroups();
        });

        document.addEventListener('logout', () => {
            this.resetGroups();
        });

        // 监听标签切换
        document.addEventListener('tab-change', (e) => {
            if (e.detail.tab === 'domain-groups') {
                this.refreshGroups();
            }
        });

        // 绑定事件
        this.bindEvents();

        // 如果已经认证，立即加载数据
        if (window.authManager && window.authManager.isAuthenticated()) {
            this.loadGroups();
        }
    }

    bindEvents() {
        // 监听自定义事件来显示模态框
        window.addEventListener('show-modal', (e) => {
            const { modalType } = e.detail;
            switch (modalType) {
                case 'add-group':
                    this.showAddGroupModal();
                    break;
                default:
                    console.log(`未处理的模态框类型: ${modalType}`);
            }
        });

        // 过滤器事件
        const statusFilter = document.getElementById('group-status-filter');
        const searchInput = document.getElementById('group-search');
        const clearFiltersBtn = document.getElementById('clear-group-filters');

        if (statusFilter) {
            statusFilter.addEventListener('change', () => this.handleFilterChange());
        }

        if (searchInput) {
            searchInput.addEventListener('input', () => this.handleFilterChange());
        }

        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => this.clearFilters());
        }

        // 刷新按钮
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="refresh-groups"]')) {
                this.refreshGroups();
            }
        });

        // 绑定模态框按钮
        this.bindModalEvents();

        // 立即登录按钮
        const promptLoginBtn = document.getElementById('prompt-login-groups');
        if (promptLoginBtn) {
            promptLoginBtn.addEventListener('click', () => this.showLoginModal());
        }
    }

    bindModalEvents() {
        // 新建分组按钮
        const saveGroupBtn = document.getElementById('save-group-btn');
        if (saveGroupBtn) {
            saveGroupBtn.addEventListener('click', () => this.handleAddGroup());
        }

        // 更新分组按钮
        const updateGroupBtn = document.getElementById('update-group-btn');
        if (updateGroupBtn) {
            updateGroupBtn.addEventListener('click', () => this.handleUpdateGroup());
        }
    }

    async loadGroups() {
        try {
            const response = await fetch('/api/domain-groups', {
                headers: {
                    'Authorization': `Bearer ${window.authManager.getToken()}`
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();
            this.groups = data.data || [];
            this.applyFilters();
            this.renderGroupTable();
            this.updateGroupBadge();

        } catch (error) {
            console.error('加载域名分组失败:', error);
            this.showError('加载域名分组失败');
        }
    }

    refreshGroups() {
        if (window.authManager && window.authManager.isAuthenticated()) {
            this.loadGroups();
        }
    }

    resetGroups() {
        this.groups = [];
        this.filteredGroups = [];
        this.renderGroupTable();
        this.updateGroupBadge();
    }

    handleFilterChange() {
        const statusFilter = document.getElementById('group-status-filter');
        const searchInput = document.getElementById('group-search');

        this.currentFilters = {
            status: statusFilter ? statusFilter.value : '',
            search: searchInput ? searchInput.value.toLowerCase() : ''
        };

        this.applyFilters();
        this.renderGroupTable();
    }

    applyFilters() {
        this.filteredGroups = this.groups.filter(group => {
            // 状态筛选
            if (this.currentFilters.status) {
                const isActive = group.is_active;
                if (this.currentFilters.status === 'active' && !isActive) return false;
                if (this.currentFilters.status === 'inactive' && isActive) return false;
            }

            // 搜索筛选
            if (this.currentFilters.search) {
                const searchTerm = this.currentFilters.search;
                const matchesName = group.name.toLowerCase().includes(searchTerm);
                const matchesDesc = group.description && group.description.toLowerCase().includes(searchTerm);
                if (!matchesName && !matchesDesc) return false;
            }

            return true;
        });
    }

    clearFilters() {
        const statusFilter = document.getElementById('group-status-filter');
        const searchInput = document.getElementById('group-search');

        if (statusFilter) statusFilter.value = '';
        if (searchInput) searchInput.value = '';

        this.currentFilters = { status: '', search: '' };
        this.applyFilters();
        this.renderGroupTable();
    }

    renderGroupTable() {
        const container = document.getElementById('group-table');
        if (!container) return;

        if (!window.authManager || !window.authManager.isAuthenticated()) {
            container.innerHTML = `
                <div class="auth-required">
                    <div class="auth-prompt">
                        <div class="auth-icon">🔐</div>
                        <p>请先登录以查看域名分组</p>
                        <button class="btn btn-primary" onclick="window.authManager.showLoginModal()">立即登录</button>
                    </div>
                </div>
            `;
            return;
        }

        if (this.filteredGroups.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📁</div>
                    <h3>暂无域名分组</h3>
                    <p>您还没有创建任何域名分组，点击下方按钮开始创建</p>
                    <button class="btn btn-primary" onclick="domainGroupManager.showAddGroupModal()">
                        ➕ 新建分组
                    </button>
                </div>
            `;
            return;
        }

        // 构建表格
        let html = `
            <div class="table-header">
                <div class="table-actions">
                    <span class="result-count">共 ${this.filteredGroups.length} 个分组</span>
                    <div class="table-controls">
                        <button class="btn btn-sm btn-outline" onclick="domainGroupManager.selectAll()">全选</button>
                        <button class="btn btn-sm btn-outline" onclick="domainGroupManager.batchDelete()">批量删除</button>
                    </div>
                </div>
            </div>
            <div class="data-table">
                <table>
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="select-all-groups"></th>
                            <th>分组名称</th>
                            <th>状态</th>
                            <th>域名数量</th>
                            <th>描述</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        this.filteredGroups.forEach(group => {
            html += this.renderGroupRow(group);
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = html;
        this.bindTableEvents();
    }

    renderGroupRow(group) {
        const statusClass = group.is_active ? 'active' : 'inactive';
        const statusText = group.is_active ? '活跃' : '停用';
        const domainCount = group.domains ? group.domains.length : 0;
        const createdAt = group.created_at ? new Date(group.created_at).toLocaleDateString() : '-';

        return `
            <tr data-group-id="${group.id}">
                <td><input type="checkbox" class="group-checkbox" value="${group.id}"></td>
                <td>
                    <div class="group-info">
                        <div class="group-name">${group.name}</div>
                    </div>
                </td>
                <td>
                    <span class="status-badge ${statusClass}">${statusText}</span>
                </td>
                <td>
                    <span class="domain-count">${domainCount} 个域名</span>
                </td>
                <td>
                    <span class="group-description">${group.description || '-'}</span>
                </td>
                <td>
                    <span class="created-time">${createdAt}</span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-outline" onclick="domainGroupManager.editGroup('${group.id}')">
                            ✏️ 编辑
                        </button>
                        <button class="btn btn-sm btn-outline" onclick="domainGroupManager.viewGroupDomains('${group.id}')">
                            👁️ 查看域名
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="domainGroupManager.deleteGroup('${group.id}')">
                            🗑️ 删除
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    updateGroupBadge() {
        const badge = document.getElementById('group-badge');
        if (badge) {
            badge.textContent = this.groups.length;
        }
    }

    showAddGroupModal() {
        const modal = document.getElementById('add-group-modal');
        if (modal && window.componentManager) {
            window.componentManager.showModal('add-group-modal');
        }
    }

    showLoginModal() {
        if (window.authManager && window.authManager.showLoginModal) {
            window.authManager.showLoginModal();
        }
    }

    // 显示错误消息
    showError(message) {
        if (window.showToast) {
            window.showToast(message, 'error');
        } else {
            alert(message);
        }
    }

    // 显示成功消息
    showSuccess(message) {
        if (window.showToast) {
            window.showToast(message, 'success');
        } else {
            alert(message);
        }
    }

    // 处理添加分组
    async handleAddGroup() {
        const form = document.getElementById('add-group-form');
        if (!form) return;

        const formData = new FormData(form);
        const domainsText = formData.get('domains');
        const domains = domainsText ? domainsText.split('\n').map(d => d.trim()).filter(d => d) : [];

        const groupData = {
            name: formData.get('name'),
            description: formData.get('description') || null,
            domains: domains
        };

        // 验证数据
        if (!groupData.name) {
            this.showError('请填写分组名称');
            return;
        }

        try {
            const response = await fetch('/api/domain-groups', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${window.authManager.getToken()}`
                },
                body: JSON.stringify(groupData)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const result = await response.json();
            if (result.success) {
                this.showSuccess('域名分组创建成功');
                this.loadGroups(); // 重新加载列表

                // 关闭模态框
                if (window.componentManager) {
                    window.componentManager.hideModal('add-group-modal');
                }

                // 清空表单
                form.reset();
            } else {
                this.showError(result.error || '创建分组失败');
            }

        } catch (error) {
            console.error('创建分组失败:', error);
            this.showError('创建分组失败');
        }
    }

    // 编辑分组
    async editGroup(groupId) {
        try {
            const response = await fetch(`/api/domain-groups/${groupId}`, {
                headers: {
                    'Authorization': `Bearer ${window.authManager.getToken()}`
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const result = await response.json();
            if (result.success && result.data) {
                this.showEditGroupModal(result.data);
            } else {
                this.showError('获取分组信息失败');
            }

        } catch (error) {
            console.error('获取分组信息失败:', error);
            this.showError('获取分组信息失败');
        }
    }

    // 显示编辑分组模态框
    showEditGroupModal(group) {
        const modal = document.getElementById('edit-group-modal');
        if (!modal) return;

        // 填充表单数据
        document.getElementById('edit-group-id').value = group.id;
        document.getElementById('edit-group-name').value = group.name;
        document.getElementById('edit-group-description').value = group.description || '';
        document.getElementById('edit-group-active').checked = group.is_active;

        // 显示模态框
        if (window.componentManager) {
            window.componentManager.showModal('edit-group-modal');
        }
    }

    // 处理更新分组
    async handleUpdateGroup() {
        const form = document.getElementById('edit-group-form');
        if (!form) return;

        const formData = new FormData(form);
        const groupId = formData.get('id');

        const updateData = {
            name: formData.get('name'),
            description: formData.get('description') || null,
            is_active: formData.has('is_active')
        };

        // 验证数据
        if (!updateData.name) {
            this.showError('请填写分组名称');
            return;
        }

        try {
            const response = await fetch(`/api/domain-groups/${groupId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${window.authManager.getToken()}`
                },
                body: JSON.stringify(updateData)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const result = await response.json();
            if (result.success) {
                this.showSuccess('域名分组更新成功');
                this.loadGroups(); // 重新加载列表

                // 关闭模态框
                if (window.componentManager) {
                    window.componentManager.hideModal('edit-group-modal');
                }
            } else {
                this.showError(result.error || '更新分组失败');
            }

        } catch (error) {
            console.error('更新分组失败:', error);
            this.showError('更新分组失败');
        }
    }

    // 删除分组
    async deleteGroup(groupId) {
        if (!confirm('确定要删除这个域名分组吗？此操作不可恢复。')) {
            return;
        }

        try {
            const response = await fetch(`/api/domain-groups/${groupId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${window.authManager.getToken()}`
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const result = await response.json();
            if (result.success) {
                this.showSuccess('域名分组删除成功');
                this.loadGroups(); // 重新加载列表
            } else {
                this.showError(result.error || '删除分组失败');
            }

        } catch (error) {
            console.error('删除分组失败:', error);
            this.showError('删除分组失败');
        }
    }

    // 查看分组中的域名
    viewGroupDomains(groupId) {
        const group = this.groups.find(g => g.id === groupId);
        if (!group) {
            this.showError('分组不存在');
            return;
        }

        if (!group.domains || group.domains.length === 0) {
            this.showError('该分组暂无域名');
            return;
        }

        const domainList = group.domains.join('\n');
        alert(`分组 "${group.name}" 包含的域名：\n\n${domainList}`);
    }

    // 绑定表格事件
    bindTableEvents() {
        // 全选功能
        const selectAllCheckbox = document.getElementById('select-all-groups');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                const checkboxes = document.querySelectorAll('.group-checkbox');
                checkboxes.forEach(cb => cb.checked = e.target.checked);
            });
        }
    }

    // 全选功能
    selectAll() {
        const selectAllCheckbox = document.getElementById('select-all-groups');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = !selectAllCheckbox.checked;
            selectAllCheckbox.dispatchEvent(new Event('change'));
        }
    }

    // 批量删除
    async batchDelete() {
        const selectedCheckboxes = document.querySelectorAll('.group-checkbox:checked');
        if (selectedCheckboxes.length === 0) {
            this.showError('请选择要删除的分组');
            return;
        }

        if (!confirm(`确定要删除选中的 ${selectedCheckboxes.length} 个分组吗？此操作不可恢复。`)) {
            return;
        }

        const deletePromises = Array.from(selectedCheckboxes).map(cb => {
            return this.deleteGroupById(cb.value);
        });

        try {
            await Promise.all(deletePromises);
            this.showSuccess('批量删除完成');
            this.loadGroups(); // 重新加载列表
        } catch (error) {
            console.error('批量删除失败:', error);
            this.showError('批量删除失败');
        }
    }

    // 删除单个分组（内部方法）
    async deleteGroupById(groupId) {
        const response = await fetch(`/api/domain-groups/${groupId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${window.authManager.getToken()}`
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        return response.json();
    }
}

// 初始化域名分组管理器
document.addEventListener('DOMContentLoaded', () => {
    window.domainGroupManager = new DomainGroupManager();
});

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DomainGroupManager;
}
