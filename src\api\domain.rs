// src/api/domain.rs

// 域名管理相关 handler、结构体、路由注册函数实现

use crate::api::ApiResponse;
use crate::db::models::Domain;
use crate::security::{UnifiedValidator, ValidationConfig};
use crate::types::{AppState, InputValidator, Pagination, ProxyError, ProxyResult};
use axum::{
    extract::{Json, Path, Query, State},
    http::StatusCode,
    response::IntoResponse,
    routing::Router,
};
use serde::Deserialize;
use std::sync::Arc;
use tracing::{error, info, warn};

/// 域名创建请求 - 带输入验证
#[derive(Debug, Deserialize)]
pub struct CreateDomainRequest {
    pub domain: String,
    pub backend_url: String,
    pub ssl_enabled: Option<bool>,
    pub ssl_cert_path: Option<String>,
    pub ssl_key_path: Option<String>,
    pub cache_enabled: Option<bool>,
    pub cache_ttl: Option<i32>,
    pub rate_limit: Option<i32>,
    pub health_check_enabled: Option<bool>,
    pub health_check_path: Option<String>,
    pub health_check_interval: Option<i32>,
    pub compression_enabled: Option<bool>,
    pub websocket_enabled: Option<bool>,
    pub max_request_size: Option<i64>,
    pub timeout_secs: Option<i32>,
    pub retry_attempts: Option<i32>,
    pub cors_enabled: Option<bool>,
    pub cors_origins: Option<Vec<String>>,
}

impl CreateDomainRequest {
    /// 验证域名创建请求
    pub fn validate(&self) -> ProxyResult<()> {
        let validator = InputValidator::new();

        // 验证域名
        validator
            .validate_domain(&self.domain)
            .map_err(|e| ProxyError::invalid_input(&e))?;

        // 验证后端URL
        validator
            .validate_url(&self.backend_url)
            .map_err(|e| ProxyError::invalid_input(&e))?;

        // 验证SSL证书路径 - 使用安全的路径验证
        if let Some(ref cert_path) = self.ssl_cert_path {
            self.validate_file_path_secure(cert_path, "SSL证书路径")?;
        }

        // 验证SSL密钥路径 - 使用安全的路径验证
        if let Some(ref key_path) = self.ssl_key_path {
            self.validate_file_path_secure(key_path, "SSL密钥路径")?;
        }

        // 验证健康检查路径
        if let Some(ref health_path) = self.health_check_path {
            self.validate_url_path(health_path, "健康检查路径")?;
        }

        // 验证数值范围
        self.validate_numeric_fields()?;

        // 验证CORS来源
        if let Some(ref origins) = self.cors_origins {
            self.validate_cors_origins(origins)?;
        }

        Ok(())
    }

    /// 安全的文件路径验证 - 简化版本
    fn validate_file_path_secure(&self, path: &str, field_name: &str) -> ProxyResult<()> {
        let validator = InputValidator::new();

        // 基本路径验证
        validator
            .validate_string(path, field_name)
            .map_err(|e| ProxyError::invalid_input(&e))?;

        // 检查路径长度
        if path.len() > 500 {
            return Err(ProxyError::invalid_input(&format!(
                "{}长度不能超过500个字符",
                field_name
            )));
        }

        // 额外的系统路径保护 - 仅保留必要的检查
        let forbidden_system_paths = [
            "/etc/passwd",
            "/etc/shadow",
            "/etc/hosts",
            "/proc/",
            "/sys/",
            "/dev/",
            "/root/",
            "/boot/",
            "/usr/bin/",
            "/usr/sbin/",
            "/bin/",
            "/sbin/",
        ];

        let path_lower = path.to_lowercase();
        for forbidden in &forbidden_system_paths {
            if path_lower.starts_with(forbidden) {
                return Err(ProxyError::invalid_input(&format!(
                    "{}不能访问系统保护路径",
                    field_name
                )));
            }
        }

        // 必须是合法的文件路径格式
        if !path.starts_with('/')
            && !path.starts_with("./")
            && !path.starts_with("../")
            && (path.chars().nth(1) != Some(':'))
        {
            return Err(ProxyError::invalid_input(&format!(
                "{}必须是有效的文件路径",
                field_name
            )));
        }

        Ok(())
    }

    fn validate_url_path(&self, path: &str, field_name: &str) -> ProxyResult<()> {
        let validator = InputValidator::new();
        validator
            .validate_string(path, field_name)
            .map_err(|e| ProxyError::invalid_input(&e))?;

        if !path.starts_with('/') {
            return Err(ProxyError::invalid_input(&format!(
                "{}必须以/开头",
                field_name
            )));
        }

        if path.len() > 200 {
            return Err(ProxyError::invalid_input(&format!(
                "{}长度不能超过200个字符",
                field_name
            )));
        }

        Ok(())
    }

    fn validate_numeric_fields(&self) -> ProxyResult<()> {
        // 验证缓存TTL
        if let Some(ttl) = self.cache_ttl {
            if !(0..=86400).contains(&ttl) {
                return Err(ProxyError::invalid_input("缓存TTL必须在0到86400秒之间"));
            }
        }

        // 验证速率限制
        if let Some(rate_limit) = self.rate_limit {
            if !(0..=10000).contains(&rate_limit) {
                return Err(ProxyError::invalid_input("速率限制必须在0到10000之间"));
            }
        }

        // 验证健康检查间隔
        if let Some(interval) = self.health_check_interval {
            if !(5..=3600).contains(&interval) {
                return Err(ProxyError::invalid_input("健康检查间隔必须在5到3600秒之间"));
            }
        }

        // 验证最大请求大小
        if let Some(max_size) = self.max_request_size {
            if !(0..=**********).contains(&max_size) {
                // 1GB
                return Err(ProxyError::invalid_input("最大请求大小必须在0到1GB之间"));
            }
        }

        // 验证超时时间
        if let Some(timeout) = self.timeout_secs {
            if !(1..=300).contains(&timeout) {
                return Err(ProxyError::invalid_input("超时时间必须在1到300秒之间"));
            }
        }

        // 验证重试次数
        if let Some(retry) = self.retry_attempts {
            if !(0..=10).contains(&retry) {
                return Err(ProxyError::invalid_input("重试次数必须在0到10之间"));
            }
        }

        Ok(())
    }

    fn validate_cors_origins(&self, origins: &[String]) -> ProxyResult<()> {
        if origins.len() > 50 {
            return Err(ProxyError::invalid_input("CORS来源数量不能超过50个"));
        }

        let validator = InputValidator::new();

        for origin in origins {
            // 检查通配符
            if origin == "*" {
                continue;
            }

            // 验证URL格式
            if origin.starts_with("http://") || origin.starts_with("https://") {
                validator
                    .validate_url(origin)
                    .map_err(|e| ProxyError::invalid_input(&e))?;
            } else {
                // 验证域名格式
                validator
                    .validate_domain(origin)
                    .map_err(|e| ProxyError::invalid_input(&e))?;
            }
        }

        Ok(())
    }

    /// 转换为Domain对象
    pub fn into_domain(self) -> Domain {
        let now = chrono::Utc::now();
        Domain {
            id: None,
            domain: self.domain,
            backend_url: self.backend_url,
            ssl_enabled: self.ssl_enabled.unwrap_or(false),
            cert_path: self.ssl_cert_path,
            key_path: self.ssl_key_path,
            health_check_url: self.health_check_path,
            health_check_interval: self.health_check_interval.map(|i| i as i64),
            max_connections: None,
            timeout: self.timeout_secs.map(|t| t as i64),
            retry_count: self.retry_attempts.map(|r| r as i64),
            load_balancing: Some("round_robin".to_string()),
            cache_enabled: self.cache_enabled.unwrap_or(true),
            cache_ttl: self.cache_ttl.map(|t| t as i64),
            rate_limit: self.rate_limit.map(|r| r as i64),
            rate_limit_window: None,
            is_active: true,
            created_at: Some(now),
            updated_at: Some(now),
        }
    }

    /// 转换为更新用的HashMap<String, Bson>
    pub fn to_update_map(&self) -> std::collections::HashMap<String, mongodb::bson::Bson> {
        use mongodb::bson::Bson;
        let mut updates = std::collections::HashMap::new();

        updates.insert(
            "backend_url".to_string(),
            Bson::String(self.backend_url.clone()),
        );
        updates.insert(
            "ssl_enabled".to_string(),
            Bson::Boolean(self.ssl_enabled.unwrap_or(false)),
        );

        if let Some(ref cert_path) = self.ssl_cert_path {
            updates.insert("ssl_cert_path".to_string(), Bson::String(cert_path.clone()));
        }

        if let Some(ref key_path) = self.ssl_key_path {
            updates.insert("ssl_key_path".to_string(), Bson::String(key_path.clone()));
        }

        updates.insert(
            "cache_enabled".to_string(),
            Bson::Boolean(self.cache_enabled.unwrap_or(true)),
        );

        if let Some(cache_ttl) = self.cache_ttl {
            updates.insert("cache_ttl".to_string(), Bson::Int32(cache_ttl));
        }

        if let Some(rate_limit) = self.rate_limit {
            updates.insert("rate_limit".to_string(), Bson::Int32(rate_limit));
        }

        updates.insert(
            "health_check_enabled".to_string(),
            Bson::Boolean(self.health_check_enabled.unwrap_or(true)),
        );

        if let Some(ref health_path) = self.health_check_path {
            updates.insert(
                "health_check_path".to_string(),
                Bson::String(health_path.clone()),
            );
        }

        if let Some(health_interval) = self.health_check_interval {
            updates.insert(
                "health_check_interval".to_string(),
                Bson::Int32(health_interval),
            );
        }

        updates.insert(
            "compression_enabled".to_string(),
            Bson::Boolean(self.compression_enabled.unwrap_or(true)),
        );
        updates.insert(
            "websocket_enabled".to_string(),
            Bson::Boolean(self.websocket_enabled.unwrap_or(false)),
        );

        if let Some(max_size) = self.max_request_size {
            updates.insert("max_request_size".to_string(), Bson::Int64(max_size));
        }

        if let Some(timeout) = self.timeout_secs {
            updates.insert("timeout_secs".to_string(), Bson::Int32(timeout));
        }

        if let Some(retry) = self.retry_attempts {
            updates.insert("retry_attempts".to_string(), Bson::Int32(retry));
        }

        updates.insert(
            "cors_enabled".to_string(),
            Bson::Boolean(self.cors_enabled.unwrap_or(false)),
        );

        if let Some(ref origins) = self.cors_origins {
            updates.insert("cors_origins".to_string(), Bson::String(origins.join(",")));
        }

        updates.insert(
            "updated_at".to_string(),
            Bson::DateTime(mongodb::bson::DateTime::now()),
        );

        updates
    }
}

/// 域名更新请求
#[derive(Debug, Deserialize)]
pub struct UpdateDomainRequest {
    pub backend_url: Option<String>,
    pub ssl_enabled: Option<bool>,
    pub ssl_cert_path: Option<String>,
    pub ssl_key_path: Option<String>,
    pub enabled: Option<bool>,
    pub cache_enabled: Option<bool>,
    pub cache_ttl: Option<i32>,
    pub rate_limit: Option<i32>,
    pub health_check_enabled: Option<bool>,
    pub health_check_path: Option<String>,
    pub health_check_interval: Option<i32>,
    pub compression_enabled: Option<bool>,
    pub websocket_enabled: Option<bool>,
    pub max_request_size: Option<i64>,
    pub timeout_secs: Option<i32>,
    pub retry_attempts: Option<i32>,
    pub cors_enabled: Option<bool>,
    pub cors_origins: Option<Vec<String>>,
}

impl UpdateDomainRequest {
    pub fn validate(&self) -> ProxyResult<()> {
        let validator = InputValidator::new();

        // 验证后端URL
        if let Some(ref backend_url) = self.backend_url {
            validator.validate_url(backend_url)?;
        }

        // 验证SSL证书路径 - 使用安全验证
        if let Some(ref cert_path) = self.ssl_cert_path {
            if !cert_path.is_empty() {
                self.validate_file_path_secure(cert_path, "SSL证书路径")?;
            }
        }

        // 验证SSL密钥路径 - 使用安全验证
        if let Some(ref key_path) = self.ssl_key_path {
            if !key_path.is_empty() {
                self.validate_file_path_secure(key_path, "SSL密钥路径")?;
            }
        }

        // 验证健康检查路径
        if let Some(ref health_path) = self.health_check_path {
            if !health_path.is_empty() {
                self.validate_url_path(health_path, "健康检查路径")?;
            }
        }

        // 验证数值字段
        self.validate_numeric_fields()?;

        // 验证CORS来源
        if let Some(ref origins) = self.cors_origins {
            self.validate_cors_origins(origins)?;
        }

        Ok(())
    }

    /// 安全的文件路径验证 - 与CreateDomainRequest保持一致
    fn validate_file_path_secure(&self, path: &str, field_name: &str) -> ProxyResult<()> {
        let validator = UnifiedValidator::new(ValidationConfig {
            strict_mode: true,
            max_path_length: 500,
            ..Default::default()
        });

        // 完整的路径验证
        if validator.validate_path(path, field_name).is_err() {
            return Err(ProxyError::security(&format!("{}包含安全威胁", field_name)));
        }

        // 检查路径长度
        if path.len() > 500 {
            return Err(ProxyError::invalid_input(&format!(
                "{}长度不能超过500个字符",
                field_name
            )));
        }

        let forbidden_system_paths = [
            "/etc/passwd",
            "/etc/shadow",
            "/etc/hosts",
            "/proc/",
            "/sys/",
            "/dev/",
            "/root/",
            "/boot/",
            "/usr/bin/",
            "/usr/sbin/",
            "/bin/",
            "/sbin/",
        ];

        let path_lower = path.to_lowercase();
        for forbidden in &forbidden_system_paths {
            if path_lower.starts_with(forbidden) {
                return Err(ProxyError::security(&format!(
                    "{}不能访问系统保护路径",
                    field_name
                )));
            }
        }

        Ok(())
    }

    fn validate_url_path(&self, path: &str, field_name: &str) -> ProxyResult<()> {
        let validator = InputValidator::new();
        validator.validate_string(path, field_name)?;

        if !path.starts_with('/') {
            return Err(ProxyError::invalid_input(&format!(
                "{}必须以/开头",
                field_name
            )));
        }

        if path.len() > 200 {
            return Err(ProxyError::invalid_input(&format!(
                "{}长度不能超过200个字符",
                field_name
            )));
        }

        Ok(())
    }

    fn validate_numeric_fields(&self) -> ProxyResult<()> {
        if let Some(ttl) = self.cache_ttl {
            if !(0..=86400).contains(&ttl) {
                return Err(ProxyError::invalid_input("缓存TTL必须在0到86400秒之间"));
            }
        }

        if let Some(rate_limit) = self.rate_limit {
            if !(0..=10000).contains(&rate_limit) {
                return Err(ProxyError::invalid_input("速率限制必须在0到10000之间"));
            }
        }

        if let Some(interval) = self.health_check_interval {
            if !(5..=3600).contains(&interval) {
                return Err(ProxyError::invalid_input("健康检查间隔必须在5到3600秒之间"));
            }
        }

        if let Some(max_size) = self.max_request_size {
            if !(0..=**********).contains(&max_size) {
                return Err(ProxyError::invalid_input("最大请求大小必须在0到1GB之间"));
            }
        }

        if let Some(timeout) = self.timeout_secs {
            if !(1..=300).contains(&timeout) {
                return Err(ProxyError::invalid_input("超时时间必须在1到300秒之间"));
            }
        }

        if let Some(retry) = self.retry_attempts {
            if !(0..=10).contains(&retry) {
                return Err(ProxyError::invalid_input("重试次数必须在0到10之间"));
            }
        }

        Ok(())
    }

    fn validate_cors_origins(&self, origins: &[String]) -> ProxyResult<()> {
        if origins.len() > 50 {
            return Err(ProxyError::invalid_input("CORS来源数量不能超过50个"));
        }

        let validator = InputValidator::new();

        for origin in origins {
            if origin == "*" {
                continue;
            }

            if origin.starts_with("http://") || origin.starts_with("https://") {
                validator.validate_url(origin)?;
            } else {
                validator.validate_domain(origin)?;
            }
        }

        Ok(())
    }
}

/// 查询参数
#[derive(Debug, Deserialize)]
pub struct ListDomainsQuery {
    pub enabled: Option<bool>,
    pub ssl_enabled: Option<bool>,
    pub page: Option<u32>,
    pub limit: Option<u32>,
}

impl ListDomainsQuery {
    pub fn validate(&self) -> ProxyResult<()> {
        if let Some(page) = self.page {
            if page > 1000 {
                return Err(ProxyError::invalid_input("页码不能超过1000"));
            }
        }

        if let Some(limit) = self.limit {
            if limit == 0 || limit > 100 {
                return Err(ProxyError::invalid_input("每页数量必须在1到100之间"));
            }
        }

        Ok(())
    }
}

/// 域名路由
pub fn routes() -> Router<Arc<AppState>> {
    Router::new()
        .route("/api/domains", axum::routing::get(list_domains))
        .route("/api/domains", axum::routing::post(create_domain_handler))
        .route("/api/domains/:id", axum::routing::get(get_domain))
        .route("/api/domains/:id", axum::routing::put(update_domain))
        .route("/api/domains/:id", axum::routing::delete(delete_domain))
}

/// 获取域名列表 - 带分页和过滤
pub async fn list_domains(
    State(state): State<Arc<AppState>>,
    Query(query): Query<ListDomainsQuery>,
) -> impl IntoResponse {
    if let Err(e) = query.validate() {
        return (
            StatusCode::BAD_REQUEST,
            Json(ApiResponse::<serde_json::Value>::error(e.to_string())),
        );
    }
    let page = query.page.unwrap_or(1);
    let limit = query.limit.unwrap_or(20) as usize;
    let pagination = Pagination::new(page, limit as u32, 0);
    match state.database.list_domains(&pagination).await {
        Ok(paginated_domains) => (
            StatusCode::OK,
            Json(ApiResponse::<serde_json::Value>::success(
                serde_json::json!({
                    "items": paginated_domains.items,
                    "total": paginated_domains.pagination.total,
                    "page": paginated_domains.pagination.page,
                    "per_page": paginated_domains.pagination.per_page
                }),
            )),
        ),
        Err(e) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ApiResponse::<serde_json::Value>::error(e.to_string())),
        ),
    }
}

/// 创建域名 - 带全面验证
pub async fn create_domain(
    State(state): State<Arc<AppState>>,
    Json(req): Json<CreateDomainRequest>,
) -> impl IntoResponse {
    if let Err(e) = req.validate() {
        warn!("创建域名请求验证失败: {}", e);
        return (
            StatusCode::BAD_REQUEST,
            Json(ApiResponse::<serde_json::Value>::error(e.to_string())),
        );
    }

    // 检查域名是否已存在
    match state.database.get_domain_by_name(&req.domain).await {
        Ok(Some(_)) => {
            let error = ProxyError::invalid_operation("域名已存在");
            return (
                StatusCode::CONFLICT,
                Json(ApiResponse::<serde_json::Value>::error(error.to_string())),
            );
        }
        Ok(None) => {}
        Err(e) => {
            error!("检查域名是否存在失败: {}", e);
            let error = ProxyError::database(&e.to_string());
            return (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse::<serde_json::Value>::error(error.to_string())),
            );
        }
    }

    let domain = req.into_domain();
    match state.database.create_domain(&domain).await {
        Ok(id) => {
            info!("成功创建域名: {} (ID: {})", domain.domain, id);
            (
                StatusCode::CREATED,
                Json(ApiResponse::<serde_json::Value>::success(
                    serde_json::json!({
                        "id": id,
                        "domain": domain.domain,
                        "backend_url": domain.backend_url
                    }),
                )),
            )
        }
        Err(e) => {
            error!("创建域名失败: {}", e);
            let error = ProxyError::database(&e.to_string());
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse::<serde_json::Value>::error(error.to_string())),
            )
        }
    }
}

/// 获取单个域名信息
pub async fn get_domain(
    State(state): State<Arc<AppState>>,
    Path(domain): Path<String>,
) -> impl IntoResponse {
    let validator = InputValidator::new();
    if let Err(e) = validator.validate_domain(&domain) {
        let error = ProxyError::invalid_input(&e.to_string());
        return (
            StatusCode::BAD_REQUEST,
            Json(ApiResponse::<serde_json::Value>::error(error.to_string())),
        );
    }

    match state.database.get_domain_by_name(&domain).await {
        Ok(Some(domain_info)) => (
            StatusCode::OK,
            Json(ApiResponse::<serde_json::Value>::success(
                serde_json::to_value(domain_info).unwrap_or_default(),
            )),
        ),
        Ok(None) => {
            let error = ProxyError::invalid_input("域名不存在");
            (
                StatusCode::NOT_FOUND,
                Json(ApiResponse::<serde_json::Value>::error(error.to_string())),
            )
        }
        Err(e) => {
            error!("获取域名失败: {}", e);
            let error = ProxyError::database(&e.to_string());
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse::<serde_json::Value>::error(error.to_string())),
            )
        }
    }
}

/// 更新域名配置
pub async fn update_domain(
    State(state): State<Arc<AppState>>,
    Path(domain): Path<String>,
    Json(req): Json<CreateDomainRequest>,
) -> impl IntoResponse {
    if let Err(e) = req.validate() {
        let error = ProxyError::invalid_input(&e.to_string());
        return (
            StatusCode::BAD_REQUEST,
            Json(ApiResponse::<serde_json::Value>::error(error.to_string())),
        );
    }

    match state.database.get_domain_by_name(&domain).await {
        Ok(Some(mut existing_domain)) => {
            existing_domain.backend_url = req.backend_url;
            existing_domain.ssl_enabled = req.ssl_enabled.unwrap_or(existing_domain.ssl_enabled);
            existing_domain.cert_path = req.ssl_cert_path.or(existing_domain.cert_path);
            existing_domain.key_path = req.ssl_key_path.or(existing_domain.key_path);
            existing_domain.cache_enabled =
                req.cache_enabled.unwrap_or(existing_domain.cache_enabled);
            existing_domain.cache_ttl = req
                .cache_ttl
                .map(|t| t as i64)
                .or(existing_domain.cache_ttl);
            existing_domain.rate_limit = req
                .rate_limit
                .map(|r| r as i64)
                .or(existing_domain.rate_limit);
            existing_domain.health_check_url =
                req.health_check_path.or(existing_domain.health_check_url);
            existing_domain.health_check_interval = req
                .health_check_interval
                .map(|i| i as i64)
                .or(existing_domain.health_check_interval);
            existing_domain.timeout = req
                .timeout_secs
                .map(|t| t as i64)
                .or(existing_domain.timeout);
            existing_domain.retry_count = req
                .retry_attempts
                .map(|r| r as i64)
                .or(existing_domain.retry_count);
            existing_domain.updated_at = Some(chrono::Utc::now());

            match state.database.update_domain(&existing_domain).await {
                Ok(_) => (
                    StatusCode::OK,
                    Json(ApiResponse::<serde_json::Value>::success(
                        serde_json::to_value(existing_domain).unwrap_or_default(),
                    )),
                ),
                Err(e) => {
                    let error = ProxyError::database(&e.to_string());
                    (
                        StatusCode::INTERNAL_SERVER_ERROR,
                        Json(ApiResponse::<serde_json::Value>::error(error.to_string())),
                    )
                }
            }
        }
        Ok(None) => {
            let error = ProxyError::invalid_input("域名不存在");
            (
                StatusCode::NOT_FOUND,
                Json(ApiResponse::<serde_json::Value>::error(error.to_string())),
            )
        }
        Err(e) => {
            let error = ProxyError::database(&e.to_string());
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse::<serde_json::Value>::error(error.to_string())),
            )
        }
    }
}

/// 删除域名
pub async fn delete_domain(
    State(state): State<Arc<AppState>>,
    Path(domain): Path<String>,
) -> impl IntoResponse {
    let validator = InputValidator::new();
    if let Err(e) = validator.validate_domain(&domain) {
        let error = ProxyError::invalid_input(&e.to_string());
        return (
            StatusCode::BAD_REQUEST,
            Json(ApiResponse::<serde_json::Value>::error(error.to_string())),
        );
    }

    match state.database.delete_domain(&domain).await {
        Ok(_) => (
            StatusCode::OK,
            Json(ApiResponse::<serde_json::Value>::success(
                serde_json::json!({"message": "域名删除成功"}),
            )),
        ),
        Err(e) => {
            error!("删除域名失败: {}", e);
            let error = ProxyError::database(&e.to_string());
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse::<serde_json::Value>::error(error.to_string())),
            )
        }
    }
}

/// 测试域名连通性
pub async fn test_domain(
    State(state): State<Arc<AppState>>,
    Path(domain): Path<String>,
) -> impl IntoResponse {
    let validator = InputValidator::new();
    if let Err(e) = validator.validate_domain(&domain) {
        let error = ProxyError::invalid_input(&e.to_string());
        return (
            StatusCode::BAD_REQUEST,
            Json(ApiResponse::<serde_json::Value>::error(error.to_string())),
        );
    }

    match state.database.get_domain_by_name(&domain).await {
        Ok(Some(domain_config)) => {
            let test_result = test_backend_connectivity(&domain_config.backend_url).await;
            (
                StatusCode::OK,
                Json(ApiResponse::<serde_json::Value>::success(
                    serde_json::json!({
                        "domain": domain,
                        "target": domain_config.backend_url,
                        "connectivity": test_result,
                        "tested_at": chrono::Utc::now()
                    }),
                )),
            )
        }
        Ok(None) => {
            let error = ProxyError::invalid_input("Domain not found");
            (
                StatusCode::NOT_FOUND,
                Json(ApiResponse::<serde_json::Value>::error(error.to_string())),
            )
        }
        Err(e) => {
            let error = ProxyError::database(&e.to_string());
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse::<serde_json::Value>::error(error.to_string())),
            )
        }
    }
}

/// 测试后端服务连通性
async fn test_backend_connectivity(backend_url: &str) -> bool {
    use reqwest::Client;
    use std::time::Duration;

    let client = Client::new();
    let resp = client
        .get(backend_url)
        .timeout(Duration::from_secs(3))
        .send()
        .await;

    resp.map(|r| r.status().is_success()).unwrap_or(false)
}

/// 创建域名的处理函数别名
pub async fn create_domain_handler(
    State(state): State<Arc<AppState>>,
    Json(req): Json<CreateDomainRequest>,
) -> impl IntoResponse {
    create_domain(State(state), Json(req)).await
}
