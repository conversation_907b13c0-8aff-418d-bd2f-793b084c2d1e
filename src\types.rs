//! 统一的类型定义
//!
//! 这个模块包含所有共享的类型定义，避免重复定义

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

/// 简化的代理错误类型，替代proxy_core::ProxyError
#[derive(Debug, Clone)]
pub struct ProxyError {
    pub kind: ProxyErrorKind,
    pub message: String,
}

#[derive(Debug, <PERSON><PERSON>)]
pub enum ProxyErrorKind {
    Authentication,
    Database,
    Security,
    InvalidInput,
    InvalidOperation,
    Network,
    Internal,
}

impl ProxyError {
    pub fn authentication(msg: &str) -> Self {
        Self {
            kind: ProxyErrorKind::Authentication,
            message: msg.to_string(),
        }
    }

    pub fn database(msg: &str) -> Self {
        Self {
            kind: ProxyErrorKind::Database,
            message: msg.to_string(),
        }
    }

    pub fn security(msg: &str) -> Self {
        Self {
            kind: ProxyErrorKind::Security,
            message: msg.to_string(),
        }
    }

    pub fn invalid_input(msg: &str) -> Self {
        Self {
            kind: ProxyErrorKind::InvalidInput,
            message: msg.to_string(),
        }
    }

    pub fn invalid_operation(msg: &str) -> Self {
        Self {
            kind: ProxyErrorKind::InvalidOperation,
            message: msg.to_string(),
        }
    }

    pub fn network(msg: &str) -> Self {
        Self {
            kind: ProxyErrorKind::Network,
            message: msg.to_string(),
        }
    }

    pub fn internal(msg: &str) -> Self {
        Self {
            kind: ProxyErrorKind::Internal,
            message: msg.to_string(),
        }
    }

    pub fn config(msg: &str) -> Self {
        Self {
            kind: ProxyErrorKind::InvalidInput, // 配置错误归类为无效输入
            message: msg.to_string(),
        }
    }
}

impl std::fmt::Display for ProxyError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{:?}: {}", self.kind, self.message)
    }
}

impl std::error::Error for ProxyError {}

// 添加从proxy_core::ProxyError的转换支持
impl From<proxy_core::ProxyError> for ProxyError {
    fn from(err: proxy_core::ProxyError) -> Self {
        match err {
            proxy_core::ProxyError::Authentication { message: _ } => {
                ProxyError::authentication(&err.to_string())
            }
            proxy_core::ProxyError::Database {
                message: _,
                source: _,
            } => ProxyError::database(&err.to_string()),
            proxy_core::ProxyError::InvalidInput { message: _ } => {
                ProxyError::invalid_input(&err.to_string())
            }
            proxy_core::ProxyError::Network {
                message: _,
                source: _,
            } => ProxyError::network(&err.to_string()),
            _ => ProxyError::internal(&err.to_string()),
        }
    }
}

// 添加从bson::ser::Error的转换支持
impl From<bson::ser::Error> for ProxyError {
    fn from(err: bson::ser::Error) -> Self {
        ProxyError::database(&format!("BSON serialization error: {}", err))
    }
}

// 添加从serde_json::Error的转换支持
impl From<serde_json::Error> for ProxyError {
    fn from(err: serde_json::Error) -> Self {
        ProxyError::internal(&format!("JSON error: {}", err))
    }
}

// 添加从String的转换支持
impl From<String> for ProxyError {
    fn from(err: String) -> Self {
        ProxyError::internal(&err)
    }
}

// 添加从&str的转换支持
impl From<&str> for ProxyError {
    fn from(err: &str) -> Self {
        ProxyError::internal(err)
    }
}

// 添加从std::io::Error的转换支持
impl From<std::io::Error> for ProxyError {
    fn from(err: std::io::Error) -> Self {
        ProxyError::network(&format!("IO error: {}", err))
    }
}

// 添加从mongodb::error::Error的转换支持
impl From<mongodb::error::Error> for ProxyError {
    fn from(err: mongodb::error::Error) -> Self {
        ProxyError::database(&format!("MongoDB error: {}", err))
    }
}

// 添加从reqwest::Error的转换支持
impl From<reqwest::Error> for ProxyError {
    fn from(err: reqwest::Error) -> Self {
        ProxyError::network(&format!("Reqwest error: {}", err))
    }
}

// 添加从tokio::task::JoinError的转换支持
impl From<tokio::task::JoinError> for ProxyError {
    fn from(err: tokio::task::JoinError) -> Self {
        ProxyError::internal(&format!("JoinError: {}", err))
    }
}

// 添加从anyhow::Error的转换支持
impl From<anyhow::Error> for ProxyError {
    fn from(err: anyhow::Error) -> Self {
        ProxyError::internal(&format!("Anyhow error: {}", err))
    }
}

// 简化的Result类型
pub type ProxyResult<T> = Result<T, ProxyError>;

/// 简化的输入验证器
pub struct InputValidator {
    max_length: usize,
    max_domain_length: usize,
}

impl InputValidator {
    pub fn new() -> Self {
        Self {
            max_length: 1000,
            max_domain_length: 253,
        }
    }

    /// 验证字符串输入
    pub fn validate_string(&self, input: &str, field_name: &str) -> Result<(), String> {
        if input.is_empty() {
            return Err(format!("{} 不能为空", field_name));
        }
        if input.len() > self.max_length {
            return Err(format!(
                "{} 长度超出限制 (最大 {} 字符)",
                field_name, self.max_length
            ));
        }

        // 检查危险字符
        if input.contains('\0') || input.contains('\r') || input.contains('\n') {
            return Err(format!("{} 包含非法字符", field_name));
        }

        Ok(())
    }

    /// 验证用户名
    pub fn validate_username(&self, username: &str) -> Result<(), String> {
        if username.is_empty() {
            return Err("用户名不能为空".to_string());
        }
        if username.len() < 3 {
            return Err("用户名长度不能少于3个字符".to_string());
        }
        if username.len() > 50 {
            return Err("用户名长度不能超过50个字符".to_string());
        }

        // 基本用户名格式检查
        if !username
            .chars()
            .all(|c| c.is_ascii_alphanumeric() || c == '_' || c == '-')
        {
            return Err("用户名只能包含字母、数字、下划线和连字符".to_string());
        }

        Ok(())
    }

    /// 验证域名
    pub fn validate_domain(&self, domain: &str) -> Result<(), String> {
        if domain.is_empty() {
            return Err("域名不能为空".to_string());
        }
        if domain.len() > self.max_domain_length {
            return Err(format!(
                "域名长度超出限制 (最大 {} 字符)",
                self.max_domain_length
            ));
        }

        // 基本域名格式检查
        if !domain
            .chars()
            .all(|c| c.is_ascii_alphanumeric() || c == '.' || c == '-')
        {
            return Err("域名包含非法字符".to_string());
        }

        if domain.starts_with('.') || domain.ends_with('.') {
            return Err("域名不能以点开头或结尾".to_string());
        }

        if domain.starts_with('-') || domain.ends_with('-') {
            return Err("域名不能以连字符开头或结尾".to_string());
        }

        Ok(())
    }

    /// 验证URL
    pub fn validate_url(&self, url: &str) -> Result<(), String> {
        if url.is_empty() {
            return Err("URL不能为空".to_string());
        }
        if url.len() > self.max_length {
            return Err(format!("URL长度超出限制 (最大 {} 字符)", self.max_length));
        }

        // 基本URL格式检查
        if !url.starts_with("http://") && !url.starts_with("https://") {
            return Err("URL必须以http://或https://开头".to_string());
        }

        // 使用url crate进行更严格的验证
        match url::Url::parse(url) {
            Ok(_) => Ok(()),
            Err(_) => Err("URL格式无效".to_string()),
        }
    }
}

impl Default for InputValidator {
    fn default() -> Self {
        Self::new()
    }
}

/// 性能监控器
#[derive(Debug, Clone)]
pub struct PerfMonitor {
    pub start_time: DateTime<Utc>,
    pub config: Arc<RwLock<PerfConfig>>, // 新增字段
}

impl PerfMonitor {
    pub fn new() -> Self {
        Self {
            start_time: Utc::now(),
            config: Arc::new(RwLock::new(PerfConfig::default())),
        }
    }

    pub async fn get_current_metrics(&self) -> PerfMetrics {
        PerfMetrics {
            cpu_usage_percent: 0.0,
            memory_usage_mb: 0.0,
            active_connections: 0,
            uptime_seconds: (Utc::now() - self.start_time).num_seconds() as u64,
        }
    }

    pub async fn update_config(&self, config: PerfConfig) {
        let mut guard = self.config.write().await;
        *guard = config;
    }
}

/// 性能指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerfMetrics {
    pub cpu_usage_percent: f64,
    pub memory_usage_mb: f64,
    pub active_connections: u64,
    pub uptime_seconds: u64,
}

// Import models from db module
use crate::db::models::{Domain, DomainGroup};

// 常量定义 - 避免重复分配
const DEFAULT_ROLE: &str = "user";
const DEFAULT_RESOURCE: &str = "unknown";
const DEFAULT_VERSION: &str = "1.0";

/// 统一的用户类型 - 整合所有模块的用户定义，简化版本
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct User {
    pub id: String,            // 改回String避免序列化问题
    pub username: String,      // 改回String
    pub email: Option<String>, // 改回String
    /// 密码哈希（仅用于数据库层，API层不应返回此字段）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub password_hash: Option<String>, // 改回String
    pub role: String,          // 改回String
    pub roles: Vec<String>,    // 改回String
    pub permissions: Vec<Permission>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_login: Option<DateTime<Utc>>,
    pub is_active: bool,
    pub force_password_change: Option<bool>,
    pub metadata: HashMap<String, String>, // 改回String
}

impl User {
    /// 从数据库模型转换为统一的User类型 - 简化版本
    pub fn from_db_model(db_user: crate::db::models::User) -> Self {
        let id = db_user
            .id
            .map(|i| i.to_string())
            .unwrap_or_else(generate_user_id);

        let role = db_user.role;

        Self {
            id,
            username: db_user.username,
            email: None, // 数据库模型中没有email字段
            password_hash: Some(db_user.password_hash),
            role: role.clone(),
            roles: vec![role],
            permissions: db_user
                .permissions
                .into_iter()
                .map(|p| Permission {
                    resource: DEFAULT_RESOURCE.to_string(),
                    actions: vec![p],
                    conditions: None,
                })
                .collect(),
            created_at: db_user.created_at.unwrap_or_else(Utc::now),
            updated_at: db_user.updated_at.unwrap_or_else(Utc::now),
            last_login: db_user.last_login,
            is_active: db_user.is_active,
            force_password_change: db_user.force_password_change,
            metadata: HashMap::new(),
        }
    }

    /// 创建新用户 - 简化版本
    pub fn new(username: impl Into<String>, email: Option<impl Into<String>>) -> Self {
        let now = Utc::now();
        let default_role = DEFAULT_ROLE.to_string();

        Self {
            id: generate_user_id(),
            username: username.into(),
            email: email.map(|e| e.into()),
            password_hash: None,
            role: default_role.clone(),
            roles: vec![default_role],
            permissions: Vec::new(),
            created_at: now,
            updated_at: now,
            last_login: None,
            is_active: true,
            force_password_change: None,
            metadata: HashMap::new(),
        }
    }

    /// 创建带密码的用户（数据库层使用）- 简化版本
    pub fn new_with_password_hash(
        username: impl Into<String>,
        password_hash: impl Into<String>,
        email: Option<impl Into<String>>,
    ) -> Self {
        let mut user = Self::new(username, email);
        user.password_hash = Some(password_hash.into());
        user
    }

    /// 获取适合API返回的用户信息（不包含密码哈希）
    pub fn to_public(&self) -> Self {
        let mut public_user = self.clone();
        public_user.password_hash = None;
        public_user
    }

    /// 从数据库记录转换（兼容旧的i64时间戳）- 简化版本
    pub fn from_db_record(
        id: impl Into<String>,
        username: impl Into<String>,
        password_hash: impl Into<String>,
        role: impl Into<String>,
        created_at: i64,
        updated_at: i64,
    ) -> Self {
        let role = role.into();

        Self {
            id: id.into(),
            username: username.into(),
            email: None,
            password_hash: Some(password_hash.into()),
            role: role.clone(),
            roles: vec![role],
            permissions: Vec::new(),
            created_at: DateTime::from_timestamp(created_at, 0).unwrap_or_else(Utc::now),
            updated_at: DateTime::from_timestamp(updated_at, 0).unwrap_or_else(Utc::now),
            last_login: None,
            is_active: true,
            force_password_change: None,
            metadata: HashMap::new(),
        }
    }

    /// 转换为数据库格式（兼容旧的数据库schema）- 内存优化版本
    pub fn to_db_format(&self) -> DbUser {
        DbUser {
            id: self.id.to_string(),
            username: self.username.to_string(),
            password_hash: self
                .password_hash
                .as_ref()
                .map(|h| h.to_string())
                .unwrap_or_default(),
            role: self
                .roles
                .first()
                .map(|r| r.to_string())
                .unwrap_or_else(|| DEFAULT_ROLE.to_string()),
            created_at: self.created_at.timestamp(),
            updated_at: self.updated_at.timestamp(),
        }
    }

    /// 检查用户是否有特定角色 - 使用借用避免分配
    pub fn has_role(&self, role: &str) -> bool {
        self.roles.iter().any(|r| r == role)
    }

    /// 检查用户是否有特定权限 - 简化版本
    pub fn has_permission(
        &self,
        resource: &str,
        action: &str,
        context: Option<&HashMap<String, String>>,
    ) -> bool {
        self.permissions.iter().any(|p| {
            if p.resource == resource && p.actions.iter().any(|a| a == action) {
                // 如果权限定义了条件，则检查条件
                if let Some(conditions) = &p.conditions {
                    if let Some(ctx) = context {
                        // 简单的条件检查示例：所有条件都必须在上下文中匹配
                        conditions.iter().all(|(key, value)| {
                            ctx.get(key) == Some(value)
                        })
                    } else {
                        false
                    }
                } else {
                    true
                }
            } else {
                false
            }
        })
    }

    /// 添加角色 - 避免重复分配
    pub fn add_role(&mut self, role: impl Into<String>) {
        let role = role.into();
        if !self.roles.iter().any(|r| r == &role) {
            self.roles.push(role);
            self.updated_at = Utc::now();
        }
    }

    /// 移除角色 - 使用借用避免分配
    pub fn remove_role(&mut self, role: &str) {
        self.roles.retain(|r| r != role);
        self.updated_at = Utc::now();
    }

    /// 添加权限
    pub fn add_permission(&mut self, permission: Permission) {
        // 检查是否已存在相同资源的权限
        if let Some(existing) = self
            .permissions
            .iter_mut()
            .find(|p| p.resource == permission.resource)
        {
            // 合并操作
            for action in permission.actions {
                if !existing.actions.iter().any(|a| a == &action) {
                    existing.actions.push(action);
                }
            }
        } else {
            self.permissions.push(permission);
        }
        self.updated_at = Utc::now();
    }

    /// 更新最后登录时间
    pub fn update_last_login(&mut self) {
        self.last_login = Some(Utc::now());
        self.updated_at = Utc::now();
    }
}

/// 生成用户ID - 简化版本
fn generate_user_id() -> String {
    // 使用更简单的ID格式，避免完整UUID的开销
    use std::sync::atomic::{AtomicU64, Ordering};
    static COUNTER: AtomicU64 = AtomicU64::new(1);

    let id = COUNTER.fetch_add(1, Ordering::Relaxed);
    format!("user_{}", id)
}

/// 权限定义 - 简化版本
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct Permission {
    pub resource: String,                            // 改为String
    pub actions: Vec<String>,                        // 改为String
    pub conditions: Option<HashMap<String, String>>, // 改为String
}

impl Permission {
    /// 创建新权限 - 简化版本
    pub fn new(resource: impl Into<String>, actions: Vec<impl Into<String>>) -> Self {
        Self {
            resource: resource.into(),
            actions: actions.into_iter().map(|a| a.into()).collect(),
            conditions: None,
        }
    }

    /// 创建带条件的权限 - 简化版本
    pub fn with_conditions(
        resource: impl Into<String>,
        actions: Vec<impl Into<String>>,
        conditions: HashMap<String, String>,
    ) -> Self {
        Self {
            resource: resource.into(),
            actions: actions.into_iter().map(|a| a.into()).collect(),
            conditions: Some(conditions),
        }
    }
}

/// 认证上下文 - 简化版本
#[derive(Debug, Clone)]
pub struct AuthContext {
    pub user: Option<User>,
    pub token: Option<String>,      // 改为String
    pub session_id: Option<String>, // 改为String
    pub expires_at: Option<DateTime<Utc>>,
    pub permissions: Vec<Permission>,
}

impl AuthContext {
    /// 创建新的认证上下文
    pub fn new(user: User, token: Option<impl Into<String>>) -> Self {
        let permissions = user.permissions.clone();
        Self {
            user: Some(user),
            token: token.map(|t| t.into()),
            session_id: None,
            expires_at: None,
            permissions,
        }
    }

    /// 检查是否已认证
    pub fn is_authenticated(&self) -> bool {
        self.user.is_some()
    }

    /// 检查是否有特定权限 - 简化版本
    pub fn has_permission(
        &self,
        resource: &str,
        action: &str,
        context: Option<&HashMap<String, String>>,
    ) -> bool {
        if self.user.is_some() {
            // 使用 AuthContext 自身的权限列表
            self.permissions.iter().any(|p| {
                if p.resource == resource && p.actions.iter().any(|a| a == action) {
                    if let Some(conditions) = &p.conditions {
                        if let Some(ctx) = context {
                            conditions.iter().all(|(key, value)| {
                                ctx.get(key) == Some(value)
                            })
                        } else {
                            false
                        }
                    } else {
                        true
                    }
                } else {
                    false
                }
            })
        } else {
            false
        }
    }

    /// 检查是否有管理员权限
    pub fn is_admin(&self) -> bool {
        if let Some(user) = &self.user {
            user.has_role("admin")
        } else {
            false
        }
    }
}

/// 统一配置结构
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct UnifiedConfig {
    pub api: ApiConfiguration,
    pub security: SecurityConfiguration,
    pub proxy: ProxyConfiguration,
    pub system: SystemConfiguration,
}

/// API配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiConfiguration {
    pub host: String,
    pub port: u16,
    pub cors_enabled: bool,
    pub rate_limit: u32,
}

impl Default for ApiConfiguration {
    fn default() -> Self {
        Self {
            host: "127.0.0.1".to_string(),
            port: 1319,
            cors_enabled: true,
            rate_limit: 100,
        }
    }
}

/// 安全配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfiguration {
    pub jwt_secret: String,
    pub session_timeout: u64,
    pub max_login_attempts: u32,
    pub enable_2fa: bool,
    pub database: DatabaseConfiguration,
    pub tls: TlsConfiguration,
}

impl Default for SecurityConfiguration {
    fn default() -> Self {
        Self {
            jwt_secret: "default_secret".to_string(),
            session_timeout: 3600,
            max_login_attempts: 5,
            enable_2fa: false,
            database: DatabaseConfiguration::default(),
            tls: TlsConfiguration::default(),
        }
    }
}

/// 代理配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProxyConfiguration {
    pub listen_addr: String,
    pub upstream_timeout: u64,
    pub max_connections: u32,
    pub enable_compression: bool,
}

impl Default for ProxyConfiguration {
    fn default() -> Self {
        Self {
            listen_addr: "127.0.0.1:1911".to_string(),
            upstream_timeout: 30,
            max_connections: 1000,
            enable_compression: true,
        }
    }
}

/// 系统配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemConfiguration {
    pub log_level: String,
    pub data_dir: String,
    pub temp_dir: String,
    pub max_memory_mb: u64,
}

impl Default for SystemConfiguration {
    fn default() -> Self {
        Self {
            log_level: "info".to_string(),
            data_dir: "./data".to_string(),
            temp_dir: "./tmp".to_string(),
            max_memory_mb: 1024,
        }
    }
}

/// 统一的配置类型基础trait
pub trait ConfigType: Send + Sync + Clone + std::fmt::Debug {
    /// 验证配置的有效性
    fn validate(&self) -> Result<(), String>;

    /// 获取配置的版本
    fn version(&self) -> String {
        "1.0".to_string()
    }
}

/// 服务状态枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ServiceStatus {
    Starting,
    Running,
    Stopping,
    Stopped,
    Error,
}

impl std::fmt::Display for ServiceStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ServiceStatus::Starting => write!(f, "Starting"),
            ServiceStatus::Running => write!(f, "Running"),
            ServiceStatus::Stopping => write!(f, "Stopping"),
            ServiceStatus::Stopped => write!(f, "Stopped"),
            ServiceStatus::Error => write!(f, "Error"),
        }
    }
}

/// 服务信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceInfo {
    pub name: String,
    pub status: ServiceStatus,
    pub started_at: Option<DateTime<Utc>>,
    pub version: String,
    pub health_check_url: Option<String>,
    pub metrics: HashMap<String, String>,
}

impl ServiceInfo {
    pub fn new(name: String, version: String) -> Self {
        Self {
            name,
            status: ServiceStatus::Stopped,
            started_at: None,
            version,
            health_check_url: None,
            metrics: HashMap::new(),
        }
    }

    pub fn start(&mut self) {
        self.status = ServiceStatus::Starting;
        self.started_at = Some(Utc::now());
    }

    pub fn set_running(&mut self) {
        self.status = ServiceStatus::Running;
    }

    pub fn stop(&mut self) {
        self.status = ServiceStatus::Stopped;
        self.started_at = None;
    }

    pub fn set_error(&mut self) {
        self.status = ServiceStatus::Error;
    }
}

/// 统一的响应类型 - 简化版本
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
    pub error_code: Option<String>,
    pub timestamp: DateTime<Utc>,
    pub request_id: Option<String>, // 改为String
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
            error_code: None,
            timestamp: Utc::now(),
            request_id: None,
        }
    }

    pub fn error(error: impl Into<String>) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(error.into()),
            error_code: None,
            timestamp: Utc::now(),
            request_id: None,
        }
    }

    pub fn with_error_code(mut self, code: impl Into<String>) -> Self {
        self.error_code = Some(code.into());
        self
    }

    pub fn with_request_id(mut self, request_id: impl Into<String>) -> Self {
        self.request_id = Some(request_id.into());
        self
    }
}

/// 分页信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Pagination {
    pub page: u32,
    pub per_page: u32,
    pub total: u32,
    pub total_pages: u32,
}

impl Pagination {
    pub fn new(page: u32, per_page: u32, total: u32) -> Self {
        let total_pages = total.div_ceil(per_page);
        Self {
            page,
            per_page,
            total,
            total_pages,
        }
    }
}

/// 分页响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginatedResponse<T> {
    pub items: Vec<T>,
    pub pagination: Pagination,
}

impl<T> PaginatedResponse<T> {
    pub fn new(items: Vec<T>, pagination: Pagination) -> Self {
        Self { items, pagination }
    }
}

/// 通用ID类型
pub type Id = String;

/// 通用时间戳类型
pub type Timestamp = DateTime<Utc>;

/// 通用元数据类型
pub type Metadata = HashMap<String, String>;

/// 数据库兼容性类型 - 用于兼容现有数据库schema
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DbUser {
    pub id: String,
    pub username: String,
    pub password_hash: String,
    pub role: String,
    pub created_at: i64,
    pub updated_at: i64,
}

impl From<User> for DbUser {
    fn from(user: User) -> Self {
        user.to_db_format()
    }
}

impl From<DbUser> for User {
    fn from(db_user: DbUser) -> Self {
        User::from_db_record(
            db_user.id,
            db_user.username,
            db_user.password_hash,
            db_user.role,
            db_user.created_at,
            db_user.updated_at,
        )
    }
}

/// 服务层兼容性类型 - 用于兼容现有服务层接口
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceUser {
    pub id: String,
    pub username: String,
    pub roles: Vec<String>,
    pub permissions: Vec<String>,
}

impl From<User> for ServiceUser {
    fn from(user: User) -> Self {
        Self {
            id: user.id,
            username: user.username,
            roles: user.roles,
            permissions: user
                .permissions
                .iter()
                .flat_map(|p| p.actions.iter().map(|a| format!("{}:{}", p.resource, a)))
                .collect(),
        }
    }
}

impl From<ServiceUser> for User {
    fn from(service_user: ServiceUser) -> Self {
        let mut user = User::new(service_user.username, None::<String>);
        user.id = service_user.id;
        user.roles = service_user.roles;

        // 解析权限字符串回权限对象
        let mut permissions_map: HashMap<String, Vec<String>> = HashMap::new();
        for perm_str in service_user.permissions {
            if let Some((resource, action)) = perm_str.split_once(':') {
                permissions_map
                    .entry(resource.to_string())
                    .or_default()
                    .push(action.to_string());
            }
        }

        user.permissions = permissions_map
            .into_iter()
            .map(|(resource, actions)| Permission::new(resource, actions))
            .collect();

        user
    }
}

/// 应用状态 - 完整版，包含所有必要的服务和状态
#[derive(Clone)]
pub struct AppState {
    pub database: std::sync::Arc<dyn Database>,
    pub auth_service: crate::auth::AuthService,
    pub domain_pool_service: std::sync::Arc<crate::domain_pool::DomainPoolService>,
    pub service_start_time: chrono::DateTime<chrono::Utc>,
    pub events: std::sync::Arc<tokio::sync::Mutex<Vec<EventRecord>>>,
    pub perf: std::sync::Arc<PerfMonitor>,
    pub recursive_proxy: std::sync::Arc<crate::recursive_proxy::service::RecursiveProxyService>,
    pub config: std::sync::Arc<tokio::sync::RwLock<UnifiedConfig>>,
}

impl std::fmt::Debug for AppState {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("AppState")
            .field("database", &"Arc<dyn Database>")
            .field("auth_service", &"AuthService")
            .field("domain_pool_service", &"Arc<DomainPoolService>")
            .field("service_start_time", &self.service_start_time)
            .field("events", &"Arc<Mutex<Vec<EventRecord>>>")
            .field("perf_monitor", &"Arc<PerfMonitor>")
            .field("config", &"Arc<RwLock<UnifiedConfig>>")
            .finish()
    }
}

impl AppState {
    pub fn new(
        database: std::sync::Arc<dyn Database>,
        auth_service: crate::auth::AuthService,
        domain_pool_service: std::sync::Arc<crate::domain_pool::DomainPoolService>,
        perf: std::sync::Arc<PerfMonitor>,
        recursive_proxy: std::sync::Arc<crate::recursive_proxy::service::RecursiveProxyService>,
    ) -> Self {
        Self {
            database,
            auth_service,
            domain_pool_service,
            service_start_time: chrono::Utc::now(),
            events: std::sync::Arc::new(tokio::sync::Mutex::new(Vec::new())),
            perf,
            recursive_proxy,
            config: std::sync::Arc::new(tokio::sync::RwLock::new(UnifiedConfig::default())),
        }
    }

    /// 获取数据库引用
    pub fn database(&self) -> &std::sync::Arc<dyn Database> {
        &self.database
    }

    /// 获取认证服务引用
    pub fn auth_service(&self) -> &crate::auth::AuthService {
        &self.auth_service
    }

    /// 获取域名池服务引用
    pub fn domain_pool_service(&self) -> &std::sync::Arc<crate::domain_pool::DomainPoolService> {
        &self.domain_pool_service
    }

    /// 获取配置引用
    pub async fn config(&self) -> tokio::sync::RwLockReadGuard<UnifiedConfig> {
        self.config.read().await
    }

    /// 更新配置
    pub async fn update_config(&self, config: UnifiedConfig) {
        let mut config_guard = self.config.write().await;
        *config_guard = config;
    }

    /// 添加事件记录
    pub async fn add_event(&self, event: EventRecord) {
        let mut events = self.events.lock().await;
        events.push(event);

        // 保持事件列表大小在合理范围内
        if events.len() > 1000 {
            events.drain(0..100); // 移除最旧的100个事件
        }
    }

    /// 获取最近的事件
    pub async fn get_recent_events(&self, limit: usize) -> Vec<EventRecord> {
        let events = self.events.lock().await;
        events.iter().rev().take(limit).cloned().collect()
    }
}

/// 简化版应用状态，仅包含最基础的认证和用户信息
#[derive(Clone)]
pub struct SimpleAppState {
    pub database: std::sync::Arc<dyn Database>,
    pub auth_service: crate::auth::AuthService,
    pub domain_pool_service: Option<std::sync::Arc<crate::domain_pool::DomainPoolService>>,
    pub user: Option<User>,
}

/// 事件记录 - 用于日志和审计，简化版本
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EventRecord {
    pub id: String, // 改为String
    pub event_type: EventType,
    pub user_id: Option<String>,    // 改为String
    pub domain: Option<String>,     // 改为String
    pub ip_address: Option<String>, // 改为String
    pub user_agent: Option<String>, // 改为String
    pub message: String,            // 改为String
    pub details: Option<serde_json::Value>,
    pub timestamp: DateTime<Utc>,
    pub severity: EventSeverity,
}

impl EventRecord {
    pub fn new(event_type: EventType, message: impl Into<String>) -> Self {
        Self {
            id: generate_event_id(),
            event_type,
            user_id: None,
            domain: None,
            ip_address: None,
            user_agent: None,
            message: message.into(),
            details: None,
            timestamp: Utc::now(),
            severity: EventSeverity::Info,
        }
    }

    pub fn with_user(mut self, user_id: impl Into<String>) -> Self {
        self.user_id = Some(user_id.into());
        self
    }

    pub fn with_domain(mut self, domain: impl Into<String>) -> Self {
        self.domain = Some(domain.into());
        self
    }

    pub fn with_ip(mut self, ip_address: impl Into<String>) -> Self {
        self.ip_address = Some(ip_address.into());
        self
    }

    pub fn with_user_agent(mut self, user_agent: impl Into<String>) -> Self {
        self.user_agent = Some(user_agent.into());
        self
    }

    pub fn with_details(mut self, details: serde_json::Value) -> Self {
        self.details = Some(details);
        self
    }

    pub fn with_severity(mut self, severity: EventSeverity) -> Self {
        self.severity = severity;
        self
    }
}

/// 生成事件ID - 简化版本
fn generate_event_id() -> String {
    use std::sync::atomic::{AtomicU64, Ordering};
    static COUNTER: AtomicU64 = AtomicU64::new(1);

    let id = COUNTER.fetch_add(1, Ordering::Relaxed);
    format!("event_{}", id)
}

/// 事件类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum EventType {
    Authentication,
    Authorization,
    ProxyRequest,
    ConfigChange,
    SystemError,
    SecurityViolation,
    PerformanceAlert,
    /// 递归代理相关事件
    RecursiveProxy,
    DomainPoolUsage,
    CacheOperation,
    UserLogin,
    UserLogout,
    DomainAdded,
    DomainRemoved,
    ConfigUpdated,
    SecurityAlert,
    SystemStart,
    SystemStop,
    BlacklistUpdate, // 添加缺失的变量
}

/// 事件严重程度
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum EventSeverity {
    Debug,
    Info,
    Warn,
    Error,
    Critical,
}

/// 递归代理统计信息 - 与数据库模型对应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecursiveProxyStats {
    /// 总递归请求数
    pub total_requests: u64,
    /// 成功递归请求数
    pub successful_requests: u64,
    /// 失败递归请求数
    pub failed_requests: u64,
    /// 平均递归深度
    pub avg_recursion_depth: f64,
    /// 最大递归深度
    pub max_recursion_depth: u32,
    /// 平均链路长度
    pub avg_chain_length: f64,
    /// 最大链路长度
    pub max_chain_length: usize,
    /// 平均响应时间（毫秒）
    pub avg_response_time_ms: f64,
    /// 缓存命中率
    pub cache_hit_rate: f64,
    /// 最常见的触发原因
    pub common_trigger_reasons: Vec<(String, u64)>,
    /// 最常见的错误类型
    pub common_errors: Vec<(String, u64)>,
    /// 统计时间范围
    pub time_range: (chrono::DateTime<chrono::Utc>, chrono::DateTime<chrono::Utc>),
}

impl Default for RecursiveProxyStats {
    fn default() -> Self {
        let now = chrono::Utc::now();
        Self {
            total_requests: 0,
            successful_requests: 0,
            failed_requests: 0,
            avg_recursion_depth: 0.0,
            max_recursion_depth: 0,
            avg_chain_length: 0.0,
            max_chain_length: 0,
            avg_response_time_ms: 0.0,
            cache_hit_rate: 0.0,
            common_trigger_reasons: Vec::new(),
            common_errors: Vec::new(),
            time_range: (now, now),
        }
    }
}

/// 域名池使用状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DomainPoolUsageStatus {
    /// 域名
    pub domain: String,
    /// 上游URL
    pub upstream_url: String,
    /// 使用次数
    pub usage_count: u64,
    /// 成功次数
    pub success_count: u64,
    /// 失败次数  
    pub failure_count: u64,
    /// 平均响应时间
    pub avg_response_time_ms: f64,
    /// 成功率
    pub success_rate: f64,
    /// 最后使用时间
    pub last_used_at: chrono::DateTime<chrono::Utc>,
    /// 健康状态
    pub health_status: DomainPoolHealthStatus,
}

impl DomainPoolUsageStatus {
    pub fn new(domain: String, upstream_url: String) -> Self {
        Self {
            domain,
            upstream_url,
            usage_count: 0,
            success_count: 0,
            failure_count: 0,
            avg_response_time_ms: 0.0,
            success_rate: 0.0,
            last_used_at: chrono::Utc::now(),
            health_status: DomainPoolHealthStatus::Unknown,
        }
    }

    pub fn calculate_success_rate(&mut self) {
        if self.usage_count > 0 {
            self.success_rate = (self.success_count as f64 / self.usage_count as f64) * 100.0;
        }
    }

    pub fn update_health_status(&mut self) {
        self.health_status = if self.success_rate >= 95.0 {
            DomainPoolHealthStatus::Healthy
        } else if self.success_rate >= 80.0 {
            DomainPoolHealthStatus::Warning
        } else {
            DomainPoolHealthStatus::Critical
        };
    }
}

/// 域名池健康状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum DomainPoolHealthStatus {
    Healthy,
    Warning,
    Critical,
    Unknown,
}

impl std::fmt::Display for DomainPoolHealthStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DomainPoolHealthStatus::Healthy => write!(f, "Healthy"),
            DomainPoolHealthStatus::Warning => write!(f, "Warning"),
            DomainPoolHealthStatus::Critical => write!(f, "Critical"),
            DomainPoolHealthStatus::Unknown => write!(f, "Unknown"),
        }
    }
}

/// 递归代理配置历史记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecursiveProxyConfigRecord {
    pub id: Option<String>,
    /// 配置版本
    pub version: u32,
    /// 配置内容（JSON格式）
    pub config_data: String,
    /// 变更原因
    pub change_reason: Option<String>,
    /// 操作用户
    pub changed_by: String,
    /// 是否为当前活跃配置
    pub is_active: bool,
    /// 创建时间
    pub created_at: chrono::DateTime<chrono::Utc>,
    /// 配置摘要
    pub config_summary: Option<String>,
}

impl RecursiveProxyConfigRecord {
    pub fn new(
        version: u32,
        config_data: String,
        changed_by: String,
        change_reason: Option<String>,
    ) -> Self {
        Self {
            id: None,
            version,
            config_data,
            change_reason,
            changed_by,
            is_active: false,
            created_at: chrono::Utc::now(),
            config_summary: None,
        }
    }

    pub fn with_summary(mut self, summary: String) -> Self {
        self.config_summary = Some(summary);
        self
    }

    pub fn activate(&mut self) {
        self.is_active = true;
    }

    pub fn deactivate(&mut self) {
        self.is_active = false;
    }
}

/// 递归链路查询条件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecursiveChainQuery {
    /// 按原始URL过滤
    pub original_url: Option<String>,
    /// 按客户端IP过滤
    pub client_ip: Option<String>,
    /// 按状态码过滤
    pub status_code: Option<u16>,
    /// 按递归深度范围过滤
    pub depth_range: Option<(u32, u32)>,
    /// 按链路长度范围过滤
    pub chain_length_range: Option<(usize, usize)>,
    /// 按时间范围过滤
    pub time_range: Option<(chrono::DateTime<chrono::Utc>, chrono::DateTime<chrono::Utc>)>,
    /// 是否只显示缓存命中
    pub cache_hit_only: Option<bool>,
    /// 是否只显示有错误的记录
    pub error_only: Option<bool>,
    /// 按触发原因过滤
    pub trigger_reason: Option<String>,
    /// 按请求方法过滤
    pub method: Option<String>,
    /// 分页信息
    pub pagination: Option<Pagination>,
}

impl Default for RecursiveChainQuery {
    fn default() -> Self {
        Self {
            original_url: None,
            client_ip: None,
            status_code: None,
            depth_range: None,
            chain_length_range: None,
            time_range: None,
            cache_hit_only: None,
            error_only: None,
            trigger_reason: None,
            method: None,
            pagination: Some(Pagination::new(1, 10, 0)),
        }
    }
}

/// 递归代理性能指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecursiveProxyMetrics {
    /// 总请求数
    pub total_requests: u64,
    /// 每分钟请求数
    pub requests_per_minute: f64,
    /// 平均响应时间
    pub avg_response_time_ms: f64,
    /// 95%分位响应时间
    pub p95_response_time_ms: f64,
    /// 99%分位响应时间
    pub p99_response_time_ms: f64,
    /// 错误率
    pub error_rate: f64,
    /// 缓存命中率
    pub cache_hit_rate: f64,
    /// 域名池使用率
    pub domain_pool_usage_rate: f64,
    /// 平均递归深度
    pub avg_recursion_depth: f64,
    /// 最大递归深度
    pub max_recursion_depth: u32,
    /// 最常见的错误类型
    pub top_errors: Vec<(String, u64)>,
    /// 最活跃的域名
    pub top_domains: Vec<(String, u64)>,
    /// 收集时间
    pub collected_at: chrono::DateTime<chrono::Utc>,
}

impl Default for RecursiveProxyMetrics {
    fn default() -> Self {
        Self {
            total_requests: 0,
            requests_per_minute: 0.0,
            avg_response_time_ms: 0.0,
            p95_response_time_ms: 0.0,
            p99_response_time_ms: 0.0,
            error_rate: 0.0,
            cache_hit_rate: 0.0,
            domain_pool_usage_rate: 0.0,
            avg_recursion_depth: 0.0,
            max_recursion_depth: 0,
            top_errors: Vec::new(),
            top_domains: Vec::new(),
            collected_at: chrono::Utc::now(),
        }
    }
}

impl RecursiveProxyMetrics {
    pub fn calculate_error_rate(&mut self, failed_requests: u64) {
        if self.total_requests > 0 {
            self.error_rate = (failed_requests as f64 / self.total_requests as f64) * 100.0;
        }
    }

    pub fn calculate_cache_hit_rate(&mut self, cache_hits: u64) {
        if self.total_requests > 0 {
            self.cache_hit_rate = (cache_hits as f64 / self.total_requests as f64) * 100.0;
        }
    }

    pub fn update_collected_time(&mut self) {
        self.collected_at = chrono::Utc::now();
    }
}

/// 数据库抽象 trait - 定义数据库操作接口
#[async_trait::async_trait]
pub trait Database: Send + Sync {
    /// 用户相关操作
    async fn create_user(&self, user: &crate::db::models::User) -> ProxyResult<String>;
    async fn get_user_by_id(&self, id: &str) -> ProxyResult<Option<crate::db::models::User>>;
    async fn get_user_by_username(
        &self,
        username: &str,
    ) -> ProxyResult<Option<crate::db::models::User>>;
    async fn update_user(&self, user: &crate::db::models::User) -> ProxyResult<()>;
    async fn delete_user(&self, id: &str) -> ProxyResult<()>;
    async fn list_users(
        &self,
        pagination: &Pagination,
    ) -> ProxyResult<PaginatedResponse<crate::db::models::User>>;
    async fn count_admin_users(&self) -> ProxyResult<i64>;
    async fn create_default_admin_user(&self) -> ProxyResult<()>; // 已废弃
    async fn create_secure_admin_user(&self, temp_password: &str) -> ProxyResult<()>;
    async fn update_last_login(&self, username: &str) -> ProxyResult<()>;
    async fn update_user_password(
        &self,
        username: &str,
        password_hash: &str,
        force_change: bool,
    ) -> ProxyResult<()>;
    /// 域名相关操作
    async fn create_domain(&self, domain: &Domain) -> ProxyResult<String>;
    async fn get_domain_by_name(&self, name: &str) -> ProxyResult<Option<Domain>>;
    async fn update_domain(&self, domain: &Domain) -> ProxyResult<()>;
    async fn delete_domain(&self, name: &str) -> ProxyResult<()>;
    async fn list_domains(&self, pagination: &Pagination)
        -> ProxyResult<PaginatedResponse<Domain>>;
    /// 域名组相关操作
    async fn create_domain_group(&self, group: &DomainGroup) -> ProxyResult<String>;
    async fn get_domain_group_by_id(&self, id: &str) -> ProxyResult<Option<DomainGroup>>;
    async fn update_domain_group(&self, group: &DomainGroup) -> ProxyResult<()>;
    async fn delete_domain_group(&self, id: &str) -> ProxyResult<()>;
    async fn list_domain_groups(
        &self,
        pagination: &Pagination,
    ) -> ProxyResult<PaginatedResponse<DomainGroup>>;
    /// 事件记录相关操作
    async fn create_event(&self, event: &EventRecord) -> ProxyResult<String>;
    async fn list_events(
        &self,
        pagination: &Pagination,
    ) -> ProxyResult<PaginatedResponse<EventRecord>>;
    /// 健康检查
    async fn health_check(&self) -> ProxyResult<DatabaseHealth>;
    /// 递归代理历史记录相关操作
    async fn save_recursive_chain_history(
        &self,
        history: &crate::db::models::RecursiveChainHistory,
    ) -> ProxyResult<String>;
    async fn get_recursive_chain_history_by_id(
        &self,
        id: &str,
    ) -> ProxyResult<Option<crate::db::models::RecursiveChainHistory>>;
    async fn list_recursive_chain_history(
        &self,
        pagination: &Pagination,
        filter: Option<&crate::db::models::RecursiveChainFilter>,
    ) -> ProxyResult<PaginatedResponse<crate::db::models::RecursiveChainHistory>>;
    async fn get_recursive_chain_stats(
        &self,
        start_time: chrono::DateTime<chrono::Utc>,
        end_time: chrono::DateTime<chrono::Utc>,
    ) -> ProxyResult<crate::db::models::RecursiveChainStats>;
    /// 域名池使用统计相关操作
    async fn save_domain_pool_usage(
        &self,
        stats: &crate::db::models::DomainPoolUsageStats,
    ) -> ProxyResult<String>;
    async fn update_domain_pool_usage(
        &self,
        domain: &str,
        upstream_url: &str,
        success: bool,
        response_time_ms: u64,
    ) -> ProxyResult<()>;
    async fn get_domain_pool_usage_stats(
        &self,
        domain: &str,
        start_time: chrono::DateTime<chrono::Utc>,
        end_time: chrono::DateTime<chrono::Utc>,
    ) -> ProxyResult<Vec<crate::db::models::DomainPoolUsageStats>>;
    /// 递归代理配置历史相关操作
    async fn save_recursive_config_history(
        &self,
        config: &crate::db::models::RecursiveProxyConfigHistory,
    ) -> ProxyResult<String>;
    async fn get_active_recursive_config(
        &self,
    ) -> ProxyResult<Option<crate::db::models::RecursiveProxyConfigHistory>>;
    async fn list_recursive_config_history(
        &self,
        pagination: &Pagination,
    ) -> ProxyResult<PaginatedResponse<crate::db::models::RecursiveProxyConfigHistory>>;
    /// 性能指标相关操作
    async fn save_performance_metric(
        &self,
        metric: &crate::db::models::PerformanceMetric,
    ) -> ProxyResult<String>;
    async fn get_performance_metrics(
        &self,
        metric_type: &str,
        start_time: chrono::DateTime<chrono::Utc>,
        end_time: chrono::DateTime<chrono::Utc>,
    ) -> ProxyResult<Vec<crate::db::models::PerformanceMetric>>;
}

/// 数据库健康状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseHealth {
    pub status: String,
    pub connection_pool_size: Option<usize>,
    pub active_connections: Option<usize>,
    pub response_time_ms: Option<u64>,
    pub last_error: Option<String>,
}

/// 兼容性类型定义（如有需要可补充字段）
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct DatabaseConfiguration {
    pub url: String,
    pub pool_size: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct TlsConfiguration {
    pub cert_path: String,
    pub key_path: String,
    pub enabled: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct RateLimitConfiguration {
    pub enabled: bool,
    pub requests_per_minute: u32,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct PerfConfig {
    pub cache_enabled: bool,
    pub cache_size_mb: u64,
    pub request_timeout_ms: u64,
    pub max_concurrent_requests: u32,
}

impl Default for PerfConfig {
    fn default() -> Self {
        Self {
            cache_enabled: true,
            cache_size_mb: 256,
            request_timeout_ms: 30000,
            max_concurrent_requests: 100,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_user_creation() {
        let user = User::new("testuser".to_string(), Some("<EMAIL>".to_string()));
        assert_eq!(user.username, "testuser");
        assert_eq!(user.email, Some("<EMAIL>".to_string()));
        assert!(user.has_role("user"));
        assert!(!user.has_role("admin"));
    }

    #[test]
    fn test_user_permissions() {
        let mut user = User::new("testuser".to_string(), None::<String>);
        let permission = Permission::new(
            "posts".to_string(),
            vec!["read".to_string(), "write".to_string()],
        );
        user.add_permission(permission);
        assert!(user.has_permission("posts", "read", None));
        assert!(user.has_permission("posts", "write", None));
        assert!(!user.has_permission("posts", "delete", None));
    }

    #[test]
    fn test_auth_context() {
        let user = User::new("testuser".to_string(), None);
        let auth_context = AuthContext::new(user, Some("token123".to_string()));

        assert!(auth_context.is_authenticated());
        assert_eq!(auth_context.token, Some("token123".to_string()));
    }

    #[test]
    fn test_api_response() {
        let response: ApiResponse<String> = ApiResponse::success("test data".to_string());
        assert!(response.success);
        assert_eq!(response.data, Some("test data".to_string()));
        assert!(response.error.is_none());
    }

    #[test]
    fn test_pagination() {
        let pagination = Pagination::new(1, 10, 25);
        assert_eq!(pagination.page, 1);
        assert_eq!(pagination.per_page, 10);
        assert_eq!(pagination.total, 25);
        assert_eq!(pagination.total_pages, 3);
    }
}
