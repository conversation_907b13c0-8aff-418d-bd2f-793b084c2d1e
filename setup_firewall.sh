#!/bin/bash

# SM代理系统 - 防火墙安全配置脚本
# 实现简单的双服务 + 防火墙方案

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

echo -e "${GREEN}🛡️  SM代理系统 - 防火墙安全配置${NC}"
echo -e "${BLUE}📋 配置方案: 双服务 + 防火墙${NC}"
echo ""

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}❌ 此脚本需要root权限运行${NC}"
    echo -e "${YELLOW}💡 请使用: sudo $0${NC}"
    exit 1
fi

# 检查ufw是否安装
if ! command -v ufw >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  UFW未安装，正在安装...${NC}"
    if command -v apt >/dev/null 2>&1; then
        apt update && apt install -y ufw
    elif command -v yum >/dev/null 2>&1; then
        yum install -y ufw
    elif command -v dnf >/dev/null 2>&1; then
        dnf install -y ufw
    else
        echo -e "${RED}❌ 无法自动安装UFW，请手动安装${NC}"
        exit 1
    fi
fi

echo -e "${BLUE}🔧 配置防火墙规则...${NC}"

# 重置防火墙规则
echo -e "${CYAN}  重置现有规则...${NC}"
ufw --force reset

# 设置默认策略
echo -e "${CYAN}  设置默认策略...${NC}"
ufw default deny incoming
ufw default allow outgoing

# 允许SSH连接（重要！）
echo -e "${CYAN}  允许SSH连接...${NC}"
ufw allow ssh
ufw allow 22

# 允许前端端口（对外开放）
echo -e "${CYAN}  允许前端端口 1319...${NC}"
ufw allow 1319/tcp comment "SM前端管理界面"

# 确保API和代理端口不对外开放（默认已拒绝，但明确说明）
echo -e "${CYAN}  确认内部端口安全...${NC}"
echo -e "${WHITE}    - API端口 1320: 仅内部访问 ✓${NC}"
echo -e "${WHITE}    - 代理端口 1911: 仅内部访问 ✓${NC}"

# 启用防火墙
echo -e "${CYAN}  启用防火墙...${NC}"
ufw --force enable

echo ""
echo -e "${GREEN}✅ 防火墙配置完成！${NC}"
echo ""
echo -e "${WHITE}🛡️  安全配置总结:${NC}"
echo -e "${GREEN}  ✅ 前端管理界面: 端口1319 - 对外开放${NC}"
echo -e "${GREEN}  ✅ API服务: 端口1320 - 仅内部访问${NC}"
echo -e "${GREEN}  ✅ 代理服务: 端口1911 - 仅内部访问${NC}"
echo -e "${GREEN}  ✅ SSH: 端口22 - 对外开放${NC}"
echo ""
echo -e "${CYAN}📊 当前防火墙状态:${NC}"
ufw status numbered

echo ""
echo -e "${BLUE}🌐 访问方式:${NC}"
echo -e "${WHITE}  前端管理: http://your-server-ip:1319${NC}"
echo -e "${WHITE}  内部API: http://127.0.0.1:1320 (仅服务器内部)${NC}"
echo -e "${WHITE}  内部代理: http://127.0.0.1:1911 (仅服务器内部)${NC}"

echo ""
echo -e "${YELLOW}💡 管理命令:${NC}"
echo -e "${WHITE}  查看状态: sudo ufw status${NC}"
echo -e "${WHITE}  禁用防火墙: sudo ufw disable${NC}"
echo -e "${WHITE}  启用防火墙: sudo ufw enable${NC}"
echo -e "${WHITE}  重置规则: sudo ufw --force reset${NC}"

echo ""
echo -e "${GREEN}🎉 安全配置完成！现在你的系统已经按照最简单的方案配置好了。${NC}"
