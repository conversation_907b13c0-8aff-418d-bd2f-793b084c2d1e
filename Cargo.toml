[workspace]
members = [
    "crates/proxy-core",
    "crates/proxy-config",
    "crates/proxy-cache",
    "crates/proxy-types"
]

[package]
name = "sm-simple"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "sm"
path = "src/main.rs"

[dependencies]
# 核心依赖 - 为大流量优化
anyhow = "1.0"
tokio = { version = "1.35", features = ["rt-multi-thread", "net", "fs", "time", "macros", "signal"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Pingora反向代理核心（移除负载均衡功能）
pingora = { version = "0.5" }
pingora-core = { version = "0.5" }
pingora-proxy = { version = "0.5" }
# 移除负载均衡依赖

# Web框架 - 管理界面
axum = { version = "0.7", default-features = false, features = ["http1", "json", "tokio", "macros", "form", "query", "ws"] }
tower = { version = "0.4", features = ["util"] }
tower-http = { version = "0.5", features = ["fs", "cors", "trace"], default-features = false }

# 本地crates
proxy-core = { path = "crates/proxy-core" }
proxy-config = { path = "crates/proxy-config" }
proxy-cache = { path = "crates/proxy-cache", features = ["auth"] }

# 安全认证
jsonwebtoken = "9.2"
bcrypt = "0.15"

# 数据库 - 可选配置（测试用文件存储，生产用MongoDB）
mongodb = { version = "2.8", default-features = false, features = ["tokio-runtime", "bson-chrono-0_4"], optional = true }

# HTTP客户端 - 递归代理必需
reqwest = { version = "0.11", features = ["json", "rustls-tls"], default-features = false }

# 系统信息 - status API使用
hostname = "0.3"
num_cpus = "1.16"

# 安全和加密 - security模块使用
hmac = "0.12"
sha2 = "0.10"
hex = "0.4"
base64 = "0.22"

# 错误处理
thiserror = "1.0"

# Security模块依赖
uuid = { version = "1.6", features = ["v4"] }
parking_lot = "0.12"
once_cell = "1.19"
regex = "1.10"

# Status API依赖
local_ipaddress = "0.1"
sys-info = "0.9"

# 缺失的核心依赖
rand = "0.8"
futures = "0.3"
bson = "2.8"
serde_yaml = "0.9"
rustls-pemfile = "2.0"
x509-parser = "0.15"
hyper = { version = "1.0", features = ["client", "http1"] }
hyper-tls = "0.6"
hyper-util = { version = "0.1", features = ["client-legacy"] }
async-trait = "0.1"
bytes = "1.5"
zeroize = "1.7"
url = "2.5"
futures-util = "0.3"

# 时间处理 - 精简版
chrono = { version = "0.4", features = ["serde"], default-features = false }

# 日志 - 生产环境必需
tracing = { version = "0.1", default-features = false, features = ["std"] }
tracing-subscriber = { version = "0.3", default-features = false, features = ["env-filter", "fmt"] }
http-body-util = "0.1.3"
bincode = "2.0.1"

# 功能特性配置
[features]
default = ["mongodb-storage"]        # 默认使用MongoDB（适合大流量）
file-storage = []                    # 文件存储模式（轻量级测试）
mongodb-storage = ["dep:mongodb"]    # MongoDB存储模式（生产环境）
production = ["mongodb-storage"]     # 生产环境完整功能
