# SM智能代理系统架构文档

## 🏗️ 系统架构概览

SM智能代理系统采用现代化的微服务架构，基于Rust语言和Pingora框架构建，提供高性能的反向代理服务和完整的Web管理界面。

### 🎯 核心设计理念

- **高性能**: 基于Pingora框架，提供企业级性能
- **安全性**: 多层安全防护，代码质量经过深度优化
- **可扩展**: 模块化设计，易于扩展和维护
- **智能化**: 递归代理和机器学习能力

## 🔧 技术栈

### 后端技术
- **语言**: Rust 1.70+
- **代理框架**: Pingora (字节跳动开源)
- **Web框架**: Axum (异步Web框架)
- **数据库**: MongoDB 7.0+ (强制要求)
- **缓存**: 内存+磁盘混合缓存
- **认证**: JWT + 安全哈希

### 前端技术
- **架构**: SPA (单页应用)
- **技术**: HTML5 + CSS3 + JavaScript
- **通信**: HTMX (实时前后端通信)
- **UI**: 响应式设计，深色主题

## 🏗️ 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    SM智能代理系统                              │
├─────────────────────────────────────────────────────────────┤
│  前端层 (Port 1319)                                          │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │   Web管理界面    │  │   静态资源服务   │                   │
│  │   (SPA + HTMX)  │  │   (CSS/JS)     │                   │
│  └─────────────────┘  └─────────────────┘                   │
├─────────────────────────────────────────────────────────────┤
│  API层 (Axum Web Server)                                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   认证授权API    │  │   域名管理API    │  │  系统状态API  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  递归代理API     │  │   配置管理API    │  │  监控统计API  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  业务服务层                                                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  递归代理服务    │  │   域名池服务     │  │   安全服务    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   缓存服务      │  │   事件服务      │  │  数据库服务   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  代理核心层 (Port 1911)                                      │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Pingora代理核心                             │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐  │ │
│  │  │  请求路由    │  │  负载均衡    │  │    TLS终端      │  │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘  │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  数据存储层                                                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │    MongoDB      │  │   磁盘缓存      │  │   日志文件    │ │
│  │   (持久化)      │  │  (智能清理)     │  │  (系统日志)   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🔄 数据流架构

### 请求处理流程

```
用户请求 → Pingora代理(1911) → 上游服务器 → 响应处理 → 用户
    ↓
递归处理 → URL提取 → 域名发现 → 缓存存储 → 统计记录
    ↓
Web管理(1319) → API处理 → 数据库操作 → 前端展示
```

### 递归代理流程

```
1. 接收代理请求
2. 检查缓存 (命中率优化)
3. 发起上游请求
4. 响应内容分析
5. URL自动提取
6. 域名自动发现
7. 递归深度控制
8. 结果缓存存储
9. 统计数据记录
10. 返回最终响应
```

## 🛡️ 安全架构

### 多层安全防护

1. **网络层安全**
   - Pingora代理仅本地访问 (127.0.0.1:1911)
   - Web管理界面可配置访问控制
   - 防火墙规则自动配置

2. **应用层安全**
   - JWT认证机制
   - 输入验证和防护
   - SQL注入防护
   - XSS攻击防护

3. **代码层安全**
   - 修复跨await持有锁问题
   - 内存安全保证
   - 类型安全检查
   - 错误处理完善

### 安全优化成果

- ✅ 修复250+代码质量警告
- ✅ 解决关键安全问题
- ✅ 优化异步代码安全性
- ✅ 强化类型系统安全

## 📊 性能架构

### 性能优化策略

1. **代理性能**
   - Pingora高性能框架
   - 异步I/O处理
   - 连接池管理
   - 内存优化

2. **缓存性能**
   - 多层缓存架构
   - 智能缓存策略
   - 基于命中率清理
   - 磁盘缓存优化

3. **数据库性能**
   - MongoDB索引优化
   - 连接池管理
   - 查询优化
   - 批量操作

### 性能指标

- **并发处理**: 支持高并发请求
- **响应时间**: 毫秒级响应
- **内存使用**: 优化内存占用
- **CPU效率**: 高效CPU利用

## 🔧 部署架构

### 生产环境部署

```
┌─────────────────────────────────────────┐
│              负载均衡器                  │
│         (Nginx/HAProxy)                │
└─────────────────┬───────────────────────┘
                  │
    ┌─────────────┼─────────────┐
    │             │             │
┌───▼───┐    ┌───▼───┐    ┌───▼───┐
│ SM实例1│    │ SM实例2│    │ SM实例3│
│1319/1911│   │1319/1911│   │1319/1911│
└───┬───┘    └───┬───┘    └───┬───┘
    │             │             │
    └─────────────┼─────────────┘
                  │
        ┌─────────▼─────────┐
        │    MongoDB集群     │
        │   (主从复制)      │
        └───────────────────┘
```

### 单机部署

```
┌─────────────────────────────────────────┐
│              SM服务器                    │
│  ┌─────────────────────────────────────┐ │
│  │        SM智能代理系统                │ │
│  │  Web管理(1319) + Pingora(1911)     │ │
│  └─────────────────────────────────────┘ │
│  ┌─────────────────────────────────────┐ │
│  │           MongoDB                   │ │
│  │         (本地实例)                   │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

## 🔄 扩展架构

### 水平扩展

- **多实例部署**: 支持多个SM实例
- **负载均衡**: 请求分发和故障转移
- **数据库集群**: MongoDB副本集
- **缓存分布**: 分布式缓存策略

### 垂直扩展

- **资源优化**: CPU和内存使用优化
- **性能调优**: 参数配置优化
- **监控告警**: 实时性能监控
- **自动扩容**: 基于负载的自动扩容

---

**相关文档**: [部署指南](deployment.md) | [API文档](api.md) | [安全配置](security.md)
